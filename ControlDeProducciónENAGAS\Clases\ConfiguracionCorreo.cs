using System;
using System.IO;
using System.Text.Json;
using System.Security.Cryptography;
using System.Text;

namespace ControlDeProducciónENAGAS.Clases
{
    /// <summary>
    /// Clase para manejar la configuración del correo electrónico EWS
    /// </summary>
    public class ConfiguracionCorreo
    {
        // Propiedades de configuración EWS
        public string EwsServerUrl { get; set; } = "https://outlook.office365.com/EWS/Exchange.asmx";
        public string EwsUsername { get; set; } = "";
        public string EwsPassword { get; set; } = "";
        public string EwsDomain { get; set; } = "";
        public string EmailNotification { get; set; } = "<EMAIL>";
        public bool AutoStartEmailChecking { get; set; } = true;
        public int CheckIntervalMinutes { get; set; } = 5;

        // Configuración de archivos
        private static readonly string ConfigDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
            "ControlDeProducciónENAGAS"
        );
        private static readonly string ConfigFilePath = Path.Combine(ConfigDirectory, "config_correo.json");
        private static readonly byte[] Entropy = Encoding.UTF8.GetBytes("CorreoApp-ExtraSalt-2024");

        /// <summary>
        /// Constructor por defecto
        /// </summary>
        public ConfiguracionCorreo()
        {
            // Valores por defecto ya establecidos en las propiedades
        }

        /// <summary>
        /// Guarda la configuración en archivo JSON encriptado
        /// </summary>
        public void GuardarConfiguracion()
        {
            try
            {
                // Crear directorio si no existe
                if (!Directory.Exists(ConfigDirectory))
                {
                    Directory.CreateDirectory(ConfigDirectory);
                }

                // Crear copia de la configuración para encriptar la contraseña
                var configParaGuardar = new ConfiguracionCorreo
                {
                    EwsServerUrl = this.EwsServerUrl,
                    EwsUsername = this.EwsUsername,
                    EwsPassword = EncriptarPassword(this.EwsPassword),
                    EwsDomain = this.EwsDomain,
                    EmailNotification = this.EmailNotification,
                    AutoStartEmailChecking = this.AutoStartEmailChecking,
                    CheckIntervalMinutes = this.CheckIntervalMinutes
                };

                // Serializar a JSON
                var jsonString = JsonSerializer.Serialize(configParaGuardar, new JsonSerializerOptions
                {
                    WriteIndented = true
                });

                // Guardar archivo
                File.WriteAllText(ConfigFilePath, jsonString, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error guardando configuración de correo: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Carga la configuración desde archivo JSON
        /// </summary>
        public static ConfiguracionCorreo CargarConfiguracion()
        {
            try
            {
                if (!File.Exists(ConfigFilePath))
                {
                    // Si no existe el archivo, crear configuración por defecto
                    var configDefault = new ConfiguracionCorreo();
                    configDefault.GuardarConfiguracion();
                    return configDefault;
                }

                // Leer archivo
                var jsonString = File.ReadAllText(ConfigFilePath, Encoding.UTF8);
                var config = JsonSerializer.Deserialize<ConfiguracionCorreo>(jsonString);

                if (config != null)
                {
                    // Desencriptar contraseña
                    config.EwsPassword = DesencriptarPassword(config.EwsPassword);
                    return config;
                }

                return new ConfiguracionCorreo();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error cargando configuración de correo: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Encripta la contraseña usando DPAPI
        /// </summary>
        private static string EncriptarPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                return "";

            try
            {
                byte[] passwordBytes = Encoding.UTF8.GetBytes(password);
                byte[] encryptedBytes = ProtectedData.Protect(passwordBytes, Entropy, DataProtectionScope.CurrentUser);
                return Convert.ToBase64String(encryptedBytes);
            }
            catch
            {
                return password; // Si falla la encriptación, devolver texto plano
            }
        }

        /// <summary>
        /// Desencripta la contraseña usando DPAPI
        /// </summary>
        private static string DesencriptarPassword(string encryptedPassword)
        {
            if (string.IsNullOrEmpty(encryptedPassword))
                return "";

            try
            {
                byte[] encryptedBytes = Convert.FromBase64String(encryptedPassword);
                byte[] passwordBytes = ProtectedData.Unprotect(encryptedBytes, Entropy, DataProtectionScope.CurrentUser);
                return Encoding.UTF8.GetString(passwordBytes);
            }
            catch
            {
                return encryptedPassword; // Si falla la desencriptación, asumir que es texto plano
            }
        }

        /// <summary>
        /// Valida que la configuración tenga los datos mínimos necesarios
        /// </summary>
        public bool EsConfiguracionValida()
        {
            return !string.IsNullOrWhiteSpace(EwsServerUrl) &&
                   !string.IsNullOrWhiteSpace(EwsUsername) &&
                   !string.IsNullOrWhiteSpace(EwsPassword);
        }

        /// <summary>
        /// Obtiene una descripción del estado de la configuración
        /// </summary>
        public string ObtenerEstadoConfiguracion()
        {
            if (!EsConfiguracionValida())
            {
                return "Configuración incompleta";
            }

            return $"Configurado para {EwsUsername} en {EwsServerUrl}";
        }
    }
}
