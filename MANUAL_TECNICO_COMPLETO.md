# MANUAL TÉCNICO COMPLETO - CONTROL DE PRODUCCIÓN ENAGAS

## ÍNDICE GENERAL

1. [ARQUITECTURA GENERAL DEL SISTEMA](#arquitectura-general)
2. [FLUJO DE INICIALIZACIÓN](#flujo-inicializacion)
3. [EVENTOS DE BOTONES](#eventos-botones)
4. [SISTEMA MQTT](#sistema-mqtt)
5. [SISTEMA MODBUS](#sistema-modbus)
6. [SISTEMA EMAIL](#sistema-email)
7. [SISTEMA SRAP](#sistema-srap)
8. [MODO AUTOMÁTICO](#modo-automatico)
9. [TIMERS Y PROCESOS AUTOMÁTICOS](#timers-procesos)
10. [MANEJO DE EXCEPCIONES](#manejo-excepciones)
11. [FLUJOS DE DATOS](#flujos-datos)
12. [DIAGNÓSTICO DE AVERÍAS](#diagnostico-averias)

---

## 1. ARQUITECTURA GENERAL DEL SISTEMA {#arquitectura-general}

### 1.1 ESTRUCTURA DE CLASES PRINCIPALES

```
ControlDeProducciónENAGAS
├── Program.cs (Punto de entrada)
├── Formularios/
│   ├── MAIN.cs (Formulario principal)
│   └── MAIN.Designer.cs (Diseño visual)
├── Clases/
│   ├── ConexionMQTT.cs
│   ├── ConfiguracionMQTT.cs
│   ├── MqttUIManager.cs
│   ├── ConfiguracionCorreo.cs
│   ├── GestorDeCorreo.cs
│   └── ServicioEWS.cs
└── Referencias externas:
    ├── EasyModbusTCP.NET v5.6.0
    ├── MQTTnet v4.3.7.1207
    └── Microsoft.Exchange.WebServices v2.2.0
```

### 1.2 VARIABLES GLOBALES CRÍTICAS

**Variables de Estado de Conexión:**
- `bool MQTTConectado` - Estado de conexión MQTT
- `bool ModbusConectado` - Estado de conexión Modbus  
- `bool correoConectado` - Estado de conexión Email
- `bool reconexionMQTTHabilitada` - Control de reconexión automática MQTT
- `bool reconexionModbusHabilitada` - Control de reconexión automática Modbus

**Variables del Sistema SRAP:**
- `bool paradarActiva` - Estado de parada PARADAR
- `bool paradamActiva` - Estado de parada PARADAM
- `bool paradalActiva` - Estado de parada PARADAL

**Variables del Modo Automático:**
- `bool modoAutomaticoActivo` - Estado del modo automático
- `string prioridadActiva` - Prioridad actual del sistema
- `int valorModoAutomaticoCalculado` - Valor calculado para envío
- `int valorConsignaPa` - Valor recibido de CONSIGNAPA
- `int consignaManual` - Valor manual del usuario

**Variables de Configuración:**
- `bool limitacionesEmailHabilitadas` - Habilitar limitaciones por email
- `bool paradasSRAPHabilitadas` - Habilitar paradas SRAP

---

## 2. FLUJO DE INICIALIZACIÓN {#flujo-inicializacion}

### 2.1 SECUENCIA DE INICIO DEL PROGRAMA

**PASO 1: Program.Main()**
```csharp
// Archivo: Program.cs línea 16
Application.Run(new MAIN());
```
- Inicia la aplicación Windows Forms
- Crea una nueva instancia del formulario MAIN
- Transfiere control al constructor MAIN()

**PASO 2: Constructor MAIN()**
```csharp
// Archivo: MAIN.cs líneas 23-40
public MAIN()
{
    InitializeComponent(); // Inicializa controles del Designer
    
    if (!DesignMode) // Solo en ejecución, no en diseñador
    {
        CargarConfiguracionMQTT();    // Carga config MQTT desde archivo
        CargarConfiguracionModbus();  // Carga config Modbus desde archivo  
        CargarConfiguracionCorreo();  // Carga config Email desde archivo
    }
}
```

**PASO 3: Evento MAIN_Load()**
```csharp
// Archivo: MAIN.cs líneas 45-87
private void MAIN_Load(object sender, EventArgs e)
{
    if (!DesignMode)
    {
        OcultarPestañasEnEjecucion();        // Oculta tabs del TabControl
        ConfigurarEstadoInicial();           // Configura estado inicial UI
        InicializarDataGridViewTopics();     // Inicializa grid de topics MQTT
        InicializarMqttUIManager();          // Inicializa gestor UI MQTT
        InicializarBotonRefrescarMQTT();     // Configura botón refresh MQTT
        InicializarLabelsTopics();           // Inicializa labels de topics
        InicializarModbus();                 // Inicializa sistema Modbus
        InicializarDataGridViewEstadosHorarios(); // Grid estados horarios
        InicializarEventosCorreo();          // Eventos del sistema email
        InicializarModoAutomatico();         // Sistema modo automático
        InicializarLimitacionesHorarias();   // Limitaciones horarias
        InicializarSistemaReconexion();      // Sistema reconexión automática
        timer1.Enabled = true;               // Habilita timer principal
    }
}
```

### 2.2 INICIALIZACIÓN DE CADA SUBSISTEMA

**MQTT (InicializarMqttUIManager):**
- Crea instancia de MqttUIManager
- Configura eventos de conexión/desconexión
- Configura suscripción a topics hardcodeados
- Inicializa diccionario de últimas actualizaciones

**Modbus (InicializarModbus):**
- Inicializa cliente EasyModbusTCP
- Configura parámetros de conexión por defecto
- Prepara sistema de reconexión automática

**Email (InicializarEventosCorreo):**
- Asigna event handlers a botones
- Configura eventos TextChanged para autoguardado
- Inicializa sistema de limitaciones horarias

**SRAP (dentro de InicializarModoAutomatico):**
- Inicializa variables de estado de paradas
- Configura labels informativos
- Prepara sistema de feedback automático

---

## 3. EVENTOS DE BOTONES {#eventos-botones}

### 3.1 BOTONES DE NAVEGACIÓN

**btnNavCorreo_Click:**
```csharp
// Línea 225
private void btnNavCorreo_Click(object sender, EventArgs e)
{
    NavegarHacia(tabPageCorreo, btnNavCorreo);
}
```
- **Función:** Navega a la pestaña de correo
- **Flujo:** Llama a NavegarHacia() que cambia la pestaña activa y actualiza colores de botones
- **Efectos secundarios:** Ninguno
- **Posibles errores:** Ninguno (método seguro)

**btnNavMqtt_Click, btnNavModbus_Click, btnNavSinoptico_Click:**
- Mismo patrón que btnNavCorreo_Click
- Navegan a sus respectivas pestañas

### 3.2 BOTONES DE CONEXIÓN MQTT

**btnMqttConectar_Click:**
```csharp
// Líneas 285-434
private async void btnMqttConectar_Click(object sender, EventArgs e)
{
    // VALIDACIÓN DE DATOS
    if (!int.TryParse(txtMqttPuerto.Text.Trim(), out int port))
    {
        MessageBox.Show("El puerto MQTT debe ser un número válido.");
        return;
    }
    
    // PROCESO DE CONEXIÓN
    bool conectado = await mqttUIManager.ConectarAsync(
        cmbMqttProtocolo.SelectedItem?.ToString() ?? "mqtts://",
        txtMqttHost.Text.Trim(),
        port,
        txtMqttClientId.Text.Trim(),
        txtMqttUsuario.Text.Trim(),
        txtMqttPassword.Text,
        chkMqttSslTls.Checked
    );
    
    // ACTUALIZACIÓN DE ESTADO
    if (conectado)
    {
        MQTTConectado = true;
        reconexionMQTTHabilitada = true;
        // Actualizar UI...
    }
}
```

**Flujo detallado:**
1. **Validación:** Verifica que el puerto sea numérico
2. **Conexión:** Llama a mqttUIManager.ConectarAsync()
3. **Suscripción:** Se suscribe automáticamente a topics hardcodeados
4. **Actualización UI:** Cambia estados de botones y labels
5. **Logging:** Registra resultado en rtbLog
6. **Reconexión:** Habilita reconexión automática

**Posibles errores:**
- Puerto inválido → MessageBox de error
- Fallo de conexión → Exception capturada, log de error
- Timeout de conexión → Manejado por MqttUIManager

**btnMqttDesconectar_Click:**
```csharp
// Líneas 451-473
private void btnMqttDesconectar_Click(object sender, EventArgs e)
{
    mqttUIManager?.Desconectar();           // Desconecta MQTT
    mqtt = null;                            // Limpia referencia
    lblEstadoMqtt.Text = "MQTT: Desconectado ❌";
    MQTTConectado = false;
    reconexionMQTTHabilitada = false;       // Deshabilita reconexión
    ultimaActualizacionTopics.Clear();      // Limpia cache de topics
}
```

### 3.3 BOTONES DE CONEXIÓN MODBUS

**btnModbusConectar_Click:**
```csharp
// Líneas 1392-1529
private async void btnModbusConectar_Click(object sender, EventArgs e)
{
    // VALIDACIÓN
    if (string.IsNullOrWhiteSpace(txtModbusIp.Text))
    {
        MessageBox.Show("❌ Ingrese una dirección IP válida");
        return;
    }
    
    // CONFIGURACIÓN
    modbusClient = new ModbusTcpClient(txtModbusIp.Text.Trim(), (int)nudModbusPuerto.Value);
    modbusClient.UnitIdentifier = (byte)nudModbusDeviceId.Value;
    modbusClient.ConnectionTimeout = 5000;
    
    // CONEXIÓN CON REINTENTOS
    int maxReintentos = 3;
    for (int intento = 1; intento <= maxReintentos; intento++)
    {
        try
        {
            await Task.Run(() => modbusClient.Connect());
            if (modbusClient.Connected)
            {
                // ÉXITO - Actualizar estado
                ModbusConectado = true;
                reconexionModbusHabilitada = true;
                break;
            }
        }
        catch (Exception ex)
        {
            // MANEJO DE ERRORES POR INTENTO
            if (intento < maxReintentos)
            {
                await Task.Delay(2000); // Espera 2 segundos
                continue;
            }
            else
            {
                // FALLO FINAL
                throw; // Re-lanza la excepción
            }
        }
    }
}
```

**Flujo detallado:**
1. **Validación:** IP no vacía
2. **Configuración:** Crea ModbusTcpClient con parámetros
3. **Reintentos:** Hasta 3 intentos con delay de 2 segundos
4. **Conexión:** Usa Task.Run para operación asíncrona
5. **Verificación:** Comprueba modbusClient.Connected
6. **Estado:** Actualiza variables globales y UI
7. **Logging:** Registra cada intento y resultado final

**Posibles errores:**
- IP vacía → MessageBox inmediato
- Timeout de conexión → ConnectionException
- IP inválida → SocketException
- Puerto cerrado → ConnectionException
- Todos manejados con reintentos automáticos

**btnModbusDesconectar_Click:**
```csharp
// Líneas 1532-1588
private void btnModbusDesconectar_Click(object sender, EventArgs e)
{
    if (modbusClient != null && modbusClient.Connected)
    {
        modbusClient.Disconnect();          // Desconecta Modbus
    }
    ModbusConectado = false;                // Actualiza estado global
    reconexionModbusHabilitada = false;     // Deshabilita reconexión
    // Actualizar UI...
}
```

### 3.4 BOTONES DE OPERACIONES MODBUS

**btnModbusLeerRegistros_Click:**
```csharp
// Líneas 1592-1748
private async void btnModbusLeerRegistros_Click(object sender, EventArgs e)
{
    // VALIDACIONES
    if (modbusClient == null || !modbusClient.Connected)
    {
        MessageBox.Show("❌ No hay conexión Modbus activa");
        return;
    }

    if (!int.TryParse(txtModbusDireccionRegistro.Text, out int direccion))
    {
        MessageBox.Show("❌ Dirección de registro inválida");
        return;
    }

    // LECTURA CON MANEJO DE EXCEPCIONES
    try
    {
        int[] valores = await Task.Run(() =>
            modbusClient.ReadHoldingRegisters(direccion, 1));

        // MOSTRAR RESULTADO
        txtModbusValorLeido.Text = valores[0].ToString();
        rtbLog.AppendText($"✅ MODBUS READ: Registro {direccion} = {valores[0]}\n");
    }
    catch (EasyModbus.Exceptions.ConnectionException ex)
    {
        // MANEJO ESPECÍFICO DE CONEXIÓN
        ModbusConectado = false;
        MessageBox.Show($"❌ Error de conexión Modbus: {ex.Message}");
    }
    catch (Exception ex)
    {
        // OTROS ERRORES
        MessageBox.Show($"❌ Error leyendo registro: {ex.Message}");
    }
}
```

**btnModbusEscribirRegistro_Click:**
```csharp
// Líneas 1751-1883
private async void btnModbusEscribirRegistro_Click(object sender, EventArgs e)
{
    // VALIDACIONES MÚLTIPLES
    if (modbusClient == null || !modbusClient.Connected) return;
    if (!int.TryParse(txtModbusDireccionRegistro.Text, out int direccion)) return;
    if (!int.TryParse(txtModbusValorEscribir.Text, out int valor)) return;

    // ESCRITURA CON CONFIRMACIÓN
    try
    {
        await Task.Run(() => modbusClient.WriteSingleRegister(direccion, valor));

        // VERIFICACIÓN DE ESCRITURA
        int[] verificacion = await Task.Run(() =>
            modbusClient.ReadHoldingRegisters(direccion, 1));

        if (verificacion[0] == valor)
        {
            rtbLog.AppendText($"✅ MODBUS WRITE: Registro {direccion} = {valor} (verificado)\n");
        }
        else
        {
            rtbLog.AppendText($"⚠️ MODBUS WRITE: Valor escrito pero verificación falló\n");
        }
    }
    catch (Exception ex)
    {
        // MANEJO DE ERRORES DE ESCRITURA
        MessageBox.Show($"❌ Error escribiendo registro: {ex.Message}");
    }
}
```

### 3.5 BOTONES DEL SISTEMA EMAIL

**BtnConectarCorreo_Click:**
```csharp
// Líneas 2916-2983
private async void BtnConectarCorreo_Click(object sender, EventArgs e)
{
    btnConectarCorreo.Enabled = false;
    btnConectarCorreo.Text = "Conectando...";

    // GUARDAR CONFIGURACIÓN
    GuardarConfiguracionCorreo();

    // CREAR GESTOR DE CORREO
    gestorCorreo = new GestorDeCorreo(configuracionCorreo);

    // CONECTAR CON TIMEOUT
    bool conectado = await gestorCorreo.ConectarAsync();

    if (conectado)
    {
        correoConectado = true;
        btnConectarCorreo.Text = "Conectado ✅";
        btnDesconectarCorreo.Enabled = true;

        // INICIAR REVISIÓN AUTOMÁTICA
        gestorCorreo.IniciarRevisionAutomatica();

        // PROCESAR CORREOS INICIALES
        await gestorCorreo.ProcesarCorreosAsync();
    }
    else
    {
        btnConectarCorreo.Text = "Conectar";
        btnConectarCorreo.Enabled = true;
        MessageBox.Show("❌ Error conectando al servidor EWS");
    }
}
```

**BtnDesconectarCorreo_Click:**
```csharp
// Líneas 2986-3020
private void BtnDesconectarCorreo_Click(object sender, EventArgs e)
{
    if (gestorCorreo != null)
    {
        gestorCorreo.DetenerRevisionAutomatica();   // Para timer automático
        gestorCorreo.Dispose();                     // Libera recursos
        gestorCorreo = null;
    }

    correoConectado = false;
    // Actualizar UI...
}
```

**BtnProcesarCorreos_Click:**
```csharp
// Líneas 3023-3079
private async void BtnProcesarCorreos_Click(object sender, EventArgs e)
{
    if (!correoConectado || gestorCorreo == null)
    {
        MessageBox.Show("Debe conectarse primero al servidor EWS");
        return;
    }

    btnProcesarCorreos.Enabled = false;
    btnProcesarCorreos.Text = "Procesando...";

    try
    {
        // PROCESAR CORREOS
        await gestorCorreo.ProcesarCorreosAsync();

        // ACTUALIZAR VISUALIZACIÓN
        ActualizarVisualizacionLimitacionesHorarias();

        btnProcesarCorreos.Text = "Procesado ✅";
        await Task.Delay(2000);
        btnProcesarCorreos.Text = "Procesar Correos";
    }
    catch (Exception ex)
    {
        MessageBox.Show($"❌ Error procesando correos: {ex.Message}");
        btnProcesarCorreos.Text = "Procesar Correos";
    }
    finally
    {
        btnProcesarCorreos.Enabled = true;
    }
}
```

### 3.6 BOTONES DEL MODO AUTOMÁTICO

**btnActivarModoAuto_Click:**
```csharp
// Líneas 3625-3666
private async void btnActivarModoAuto_Click(object sender, EventArgs e)
{
    // VERIFICAR CONECTIVIDAD
    if (!VerificarConectividadSistemas())
    {
        var (mqtt, modbus, correo) = ObtenerEstadoSistemas();
        string sistemasDesconectados = "";
        if (!mqtt) sistemasDesconectados += "• MQTT\n";
        if (!modbus) sistemasDesconectados += "• Modbus\n";
        if (!correo) sistemasDesconectados += "• Email\n";

        MessageBox.Show($"❌ SISTEMAS DESCONECTADOS:\n\n{sistemasDesconectados}");
        return;
    }

    // ACTIVAR MODO AUTOMÁTICO
    modoAutomaticoActivo = true;
    btnActivarModoAuto.Enabled = false;
    btnDesactivarModoAuto.Enabled = true;

    // CALCULAR Y ENVIAR VALOR INICIAL
    await CalcularYEnviarValorModoAutomatico();

    rtbLog.AppendText($"🤖 MODO AUTOMÁTICO: Activado\n");
}
```

**btnDesactivarModoAuto_Click:**
```csharp
// Líneas 3669-3694
private void btnDesactivarModoAuto_Click(object sender, EventArgs e)
{
    modoAutomaticoActivo = false;
    btnActivarModoAuto.Enabled = true;
    btnDesactivarModoAuto.Enabled = false;

    prioridadActiva = "NINGUNA";
    valorModoAutomaticoCalculado = 0;

    rtbLog.AppendText($"🤖 MODO AUTOMÁTICO: Desactivado\n");
}
```

---

## 4. SISTEMA MQTT {#sistema-mqtt}

### 4.1 ARQUITECTURA MQTT

**Clases principales:**
- `ConexionMQTT.cs` - Cliente MQTT base
- `MqttUIManager.cs` - Gestor de UI y eventos
- `ConfiguracionMQTT.cs` - Configuración persistente

**Topics hardcodeados:**
```csharp
// Líneas 89-96
private readonly string[] topicsEnagas = {
    "DD/ENAGAS/ALMENDRALEJO/CONSIGNAPA",
    "DD/ENAGAS/ALMENDRALEJO/MOTIVOPA",
    "DD/ENAGAS/ALMENDRALEJO/PARADAR",
    "DD/ENAGAS/ALMENDRALEJO/PARADAM",
    "DD/ENAGAS/ALMENDRALEJO/PARADAL",
    "DD/ENAGAS/ALMENDRALEJO/PRESELECCION"
    // NO incluye topics FEEDBACK para evitar bucles infinitos
};
```

### 4.2 FLUJO DE CONEXIÓN MQTT

**Paso 1: Configuración inicial**
```csharp
// En InicializarMqttUIManager()
mqttUIManager = new MqttUIManager(
    rtbLog,                    // Control de log
    lblEstadoMqtt,            // Label de estado
    dgvTopicsMqtt,            // DataGridView de topics
    topicsEnagas              // Array de topics hardcodeados
);
```

**Paso 2: Eventos de conexión**
```csharp
// En MqttUIManager.cs
mqttClient.ConnectedAsync += async e =>
{
    // Actualizar UI
    ActualizarEstadoUI(true, "MQTT: Conectado ✅");

    // Suscribirse a topics
    foreach (string topic in topicsEnagas)
    {
        await mqttClient.SubscribeAsync(topic);
    }

    // Notificar conexión exitosa
    OnConexionCambiada?.Invoke(true);
};
```

**Paso 3: Procesamiento de mensajes**
```csharp
// En MqttUIManager.cs
mqttClient.ApplicationMessageReceivedAsync += async e =>
{
    string topic = e.ApplicationMessage.Topic;
    string mensaje = Encoding.UTF8.GetString(e.ApplicationMessage.Payload);

    // Procesar según tipo de topic
    if (topic.Contains("CONSIGNAPA"))
    {
        ProcesarConsignaPa(mensaje);
    }
    else if (topic.Contains("MOTIVOPA"))
    {
        ProcesarMotivoPa(mensaje);
    }
    else if (topic.Contains("PARADAR"))
    {
        ProcesarParadaSRAP("PARADAR", mensaje);
    }
    // ... otros topics
};
```

### 4.3 SISTEMA DE FEEDBACK AUTOMÁTICO

**Principio fundamental:**
- Solo ENVIAR feedback, nunca LEER topics FEEDBACK
- Feedback automático en cada mensaje recibido
- Sin verificación de confirmación para evitar bucles

**Método PublicarFeedback:**
```csharp
// Líneas 1310-1340
private void PublicarFeedback(string topicOriginal, string topicFeedback, string valor)
{
    try
    {
        if (mqtt != null && mqtt.EstaConectado())
        {
            mqtt.PublicarMensaje(topicFeedback, valor);
            LogSafe($"📤 Feedback enviado: {topicFeedback} = {valor}");
            // Feedback enviado - no verificar confirmación para evitar bucles
        }
    }
    catch (Exception ex)
    {
        LogSafe($"❌ Error enviando feedback {topicFeedback}: {ex.Message}");
    }
}
```

**Mapeo de feedback:**
- CONSIGNAPA → FEEDBACKPA
- MOTIVOPA → FEEDBACKMOTIVOPA
- PARADAR → FEEDBACKPARADAR
- PARADAM → FEEDBACKPARADAM
- PARADAL → FEEDBACKPARADAL

### 4.4 PROCESAMIENTO DE TOPICS ESPECÍFICOS

**ProcesarConsignaPa:**
```csharp
// Líneas 1082-1090
if (int.TryParse(valorConsignapa, out int consignaRecibida))
{
    valorConsignaPa = consignaRecibida;
    LogSafe($"📊 CONSIGNAPA: Valor recibido y actualizado: {consignaRecibida}");

    // Enviar feedback automático
    PublicarFeedback("DD/ENAGAS/ALMENDRALEJO/CONSIGNAPA",
                    "DD/ENAGAS/ALMENDRALEJO/FEEDBACKPA",
                    valorConsignapa);
}
```

**ProcesarParadaSRAP:**
```csharp
// Líneas 1158-1195
private async void ProcesarParadaSRAP(string tipoParada, string valor)
{
    // Actualizar estado interno
    switch (tipoParada)
    {
        case "PARADAR": paradarActiva = (valor == "1"); break;
        case "PARADAM": paradamActiva = (valor == "1"); break;
        case "PARADAL": paradalActiva = (valor == "1"); break;
    }

    // Actualizar UI (botones informativos)
    ActualizarBotonesSRAP();

    // Enviar feedback automático
    string topicFeedback = $"DD/ENAGAS/ALMENDRALEJO/FEEDBACK{tipoParada}";
    PublicarFeedback(topicControl, topicFeedback, valor);

    // Recalcular modo automático si está activo
    if (modoAutomaticoActivo)
    {
        await CalcularYEnviarValorModoAutomatico();
    }
}
```

---

## 5. SISTEMA MODBUS {#sistema-modbus}

### 5.1 ARQUITECTURA MODBUS

**Librería utilizada:** EasyModbusTCP.NET v5.6.0
**Clase principal:** `ModbusTcpClient`
**Variable global:** `ModbusTcpClient modbusClient`

**Configuración por defecto:**
```csharp
// En btnModbusConectar_Click
modbusClient = new ModbusTcpClient(txtModbusIp.Text.Trim(), (int)nudModbusPuerto.Value);
modbusClient.UnitIdentifier = (byte)nudModbusDeviceId.Value;
modbusClient.ConnectionTimeout = 5000; // 5 segundos
```

### 5.2 OPERACIONES MODBUS BÁSICAS

**Lectura de registros:**
```csharp
// ReadHoldingRegisters(dirección, cantidad)
int[] valores = modbusClient.ReadHoldingRegisters(direccion, 1);
int valorLeido = valores[0];
```

**Escritura de registros:**
```csharp
// WriteSingleRegister(dirección, valor)
modbusClient.WriteSingleRegister(direccion, valor);

// Verificación inmediata
int[] verificacion = modbusClient.ReadHoldingRegisters(direccion, 1);
bool escrituraExitosa = (verificacion[0] == valor);
```

### 5.3 SISTEMA DE RECONEXIÓN AUTOMÁTICA MODBUS

**Método VerificarYReconectarModbus:**
```csharp
// Líneas 3270-3350
private async void VerificarYReconectarModbus()
{
    if (!reconexionModbusHabilitada || modbusClient == null) return;

    try
    {
        // Verificar estado real de conexión
        if (!modbusClient.Connected)
        {
            LogSafe("🔄 MODBUS: Conexión perdida, intentando reconectar...");

            // Intentar reconexión
            await Task.Run(() => modbusClient.Connect());

            if (modbusClient.Connected)
            {
                ModbusConectado = true;
                LogSafe("✅ MODBUS: Reconexión exitosa");
            }
        }
    }
    catch (EasyModbus.Exceptions.ConnectionException ex)
    {
        ModbusConectado = false;
        LogSafe($"❌ MODBUS: Error de reconexión: {ex.Message}");
    }
    catch (Exception ex)
    {
        LogSafe($"❌ MODBUS: Error inesperado en reconexión: {ex.Message}");
    }
}
```

### 5.4 INTEGRACIÓN CON MODO AUTOMÁTICO

**Método EnviarValorPorModbus:**
```csharp
// Líneas 3450-3520
private async Task EnviarValorPorModbus(int valor)
{
    if (modbusClient == null || !modbusClient.Connected)
    {
        LogSafe("❌ MODBUS: No hay conexión para enviar valor");
        return;
    }

    try
    {
        // Obtener dirección de registro desde configuración
        int direccion = (int)nudModbusDireccionRegistro.Value;

        // Escribir valor
        await Task.Run(() => modbusClient.WriteSingleRegister(direccion, valor));

        // Verificar escritura
        int[] verificacion = await Task.Run(() =>
            modbusClient.ReadHoldingRegisters(direccion, 1));

        if (verificacion[0] == valor)
        {
            LogSafe($"✅ MODBUS: Valor {valor} enviado y verificado en registro {direccion}");
        }
        else
        {
            LogSafe($"⚠️ MODBUS: Valor enviado pero verificación falló");
        }
    }
    catch (Exception ex)
    {
        LogSafe($"❌ MODBUS: Error enviando valor: {ex.Message}");
        ModbusConectado = false; // Marcar como desconectado para reconexión
    }
}
```

---

## 6. SISTEMA EMAIL {#sistema-email}

### 6.1 ARQUITECTURA EMAIL

**Clases principales:**
- `ConfiguracionCorreo.cs` - Configuración EWS
- `GestorDeCorreo.cs` - Lógica de procesamiento
- `ServicioEWS.cs` - Cliente Exchange Web Services

**Configuración por defecto:**
```csharp
// En config_email_default.json
{
    "UseEws": true,
    "EwsServerUrl": "https://outlook.office365.com/EWS/Exchange.asmx",
    "EwsUsuario": "<EMAIL>",
    "EwsPassword": "",
    "EwsDominio": "",
    "IntervaloProcesamiento": 300000
}
```

### 6.2 FLUJO DE PROCESAMIENTO DE CORREOS

**Paso 1: Conexión EWS**
```csharp
// En GestorDeCorreo.ConectarAsync()
ExchangeService service = new ExchangeService(ExchangeVersion.Exchange2013_SP1);
service.Url = new Uri(configuracion.EwsServerUrl);
service.Credentials = new WebCredentials(
    configuracion.EwsUsuario,
    configuracion.EwsPassword,
    configuracion.EwsDominio
);

// Verificar conexión
service.ResolveName("<EMAIL>");
```

**Paso 2: Búsqueda de correos**
```csharp
// Buscar correos con asunto específico
SearchFilter.ContainsSubstring filtroAsunto =
    new SearchFilter.ContainsSubstring(ItemSchema.Subject, "LIMITACION");

FindItemsResults<Item> resultados = service.FindItems(
    WellKnownFolderName.Inbox,
    filtroAsunto,
    new ItemView(50)
);
```

**Paso 3: Procesamiento de limitaciones**
```csharp
// Extraer información de limitaciones horarias
foreach (EmailMessage email in resultados)
{
    // Parsear contenido del email
    string contenido = email.Body.Text;

    // Extraer horas de limitación
    var limitaciones = ExtraerLimitacionesDeEmail(contenido);

    // Actualizar estado interno
    ActualizarLimitacionesHorarias(limitaciones);
}
```

### 6.3 SISTEMA DE LIMITACIONES HORARIAS

**Variables de estado:**
```csharp
// Variables globales para limitaciones
private Dictionary<int, bool> limitacionesManana = new Dictionary<int, bool>();
private Dictionary<int, bool> limitacionesHoy = new Dictionary<int, bool>();
private DateTime ultimaActualizacionLimitaciones = DateTime.MinValue;
```

**Método ActualizarLimitacionesHorarias:**
```csharp
// Líneas 3150-3220
private void ActualizarLimitacionesHorarias()
{
    DateTime ahora = DateTime.Now;

    // Verificar si cambió el día
    if (ahora.Date > ultimaActualizacionLimitaciones.Date)
    {
        // Pasar limitaciones de MAÑANA a HOY
        limitacionesHoy.Clear();
        foreach (var kvp in limitacionesManana)
        {
            limitacionesHoy[kvp.Key] = kvp.Value;
        }

        // Limpiar limitaciones de MAÑANA
        limitacionesManana.Clear();

        ultimaActualizacionLimitaciones = ahora;
        LogSafe("🕐 LIMITACIONES: Cambio de día - limitaciones actualizadas");
    }

    // Verificar limitación actual
    int horaActual = ahora.Hour;
    bool hayLimitacionActual = limitacionesHoy.ContainsKey(horaActual) &&
                              limitacionesHoy[horaActual];

    // Actualizar estado global
    hayLimitacionesEmailActivas = hayLimitacionActual;

    // Recalcular modo automático si está activo
    if (modoAutomaticoActivo)
    {
        Task.Run(async () => await CalcularYEnviarValorModoAutomatico());
    }
}
```

### 6.4 BOTÓN DE DEPURACIÓN

**BtnForzarCambioHora_Click:**
```csharp
// Líneas 3082-3149
private void BtnForzarCambioHora_Click(object sender, EventArgs e)
{
    if (gestorCorreo == null)
    {
        MessageBox.Show("El gestor de correo no está inicializado");
        return;
    }

    // Forzar paso de limitaciones MAÑANA → HOY
    limitacionesHoy.Clear();
    foreach (var kvp in limitacionesManana)
    {
        limitacionesHoy[kvp.Key] = kvp.Value;
    }
    limitacionesManana.Clear();

    // Actualizar timestamp
    ultimaActualizacionLimitaciones = DateTime.Now;

    // Actualizar visualización
    ActualizarVisualizacionLimitacionesHorarias();

    // Recalcular modo automático
    if (modoAutomaticoActivo)
    {
        Task.Run(async () => await CalcularYEnviarValorModoAutomatico());
    }

    rtbLog.AppendText($"🐛 DEBUG: Limitaciones forzadas de MAÑANA a HOY\n");
    MessageBox.Show("✅ Limitaciones forzadas correctamente", "Debug",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
}
```

---

## 7. SISTEMA SRAP {#sistema-srap}

### 7.1 CONCEPTO SRAP

**SRAP:** Sistema de Regulación Automática de Presión
**Tipos de parada:**
- **PARADAR** - Parada por alta presión
- **PARADAM** - Parada por media presión
- **PARADAL** - Parada por baja presión

### 7.2 VARIABLES DE ESTADO SRAP

```csharp
// Variables globales SRAP
private bool paradarActiva = false;    // Estado PARADAR
private bool paradamActiva = false;    // Estado PARADAM
private bool paradalActiva = false;    // Estado PARADAL
private bool hayParadasSRAPActivas = false; // Estado consolidado
```

### 7.3 BOTONES SRAP (SOLO INFORMATIVOS)

**Importante:** Los botones SRAP NO tienen eventos Click
- Son solo informativos para mostrar el estado
- Se actualizan automáticamente desde MQTT
- NO envían comandos al sistema

**Método ActualizarBotonesSRAP:**
```csharp
// Líneas 1200-1250
private void ActualizarBotonesSRAP()
{
    // Actualizar colores según estado (thread-safe)
    if (InvokeRequired)
    {
        Invoke(new Action(ActualizarBotonesSRAP));
        return;
    }

    // PARADAR
    if (paradarActiva)
    {
        btnParadar.BackColor = Color.Red;
        btnParadar.ForeColor = Color.White;
        btnParadar.Text = "PARADAR ACTIVA";
    }
    else
    {
        btnParadar.BackColor = Color.Gray;
        btnParadar.ForeColor = Color.Black;
        btnParadar.Text = "PARADAR";
    }

    // PARADAM y PARADAL - mismo patrón
    // ...

    // Actualizar estado consolidado
    hayParadasSRAPActivas = paradarActiva || paradamActiva || paradalActiva;
}
```

### 7.4 INTEGRACIÓN SRAP CON MODO AUTOMÁTICO

**En CalcularYEnviarValorModoAutomatico:**
```csharp
// Líneas 3400-3449
private async Task CalcularYEnviarValorModoAutomatico()
{
    if (!modoAutomaticoActivo) return;

    int valorCalculado = 0;
    string prioridad = "NINGUNA";

    // PRIORIDAD 1: Paradas SRAP
    if (paradasSRAPHabilitadas && hayParadasSRAPActivas)
    {
        valorCalculado = -200; // Valor específico para paradas SRAP
        prioridad = "PARADAS SRAP";
    }
    // PRIORIDAD 2: Limitaciones Email
    else if (limitacionesEmailHabilitadas && hayLimitacionesEmailActivas)
    {
        valorCalculado = -100; // Valor específico para limitaciones email
        prioridad = "LIMITACIONES EMAIL";
    }
    // PRIORIDAD 3: Consigna normal
    else
    {
        valorCalculado = valorConsignaPa; // Valor recibido de CONSIGNAPA
        prioridad = "CONSIGNA NORMAL";
    }

    // Actualizar variables globales
    prioridadActiva = prioridad;
    valorModoAutomaticoCalculado = valorCalculado;

    // Enviar valor por Modbus
    await EnviarValorPorModbus(valorCalculado);

    LogSafe($"🤖 MODO AUTO: {prioridad} → Valor: {valorCalculado}");
}
```

---

## 8. TIMERS Y PROCESOS AUTOMÁTICOS {#timers-procesos}

### 8.1 TIMER PRINCIPAL (timer1)

**Configuración:**
- Intervalo: 1000ms (1 segundo)
- Habilitado después de inicialización completa

**Método timer1_Tick:**
```csharp
// Líneas 436-449
private void timer1_Tick(object sender, EventArgs e)
{
    if (DesignMode) return; // No ejecutar en diseñador

    // NO actualizar labels de topics MQTT aquí - solo por suscripción
    // ActualizarLabelsTopicsMQTT(); // ELIMINADO - causaba bucle infinito

    ActualizarEstadoMQTT();           // Actualizar estado conexión MQTT
    VerificarYReconectarServicios();  // Sistema reconexión automática
}
```

**Funciones ejecutadas cada segundo:**
1. **ActualizarEstadoMQTT()** - Verifica estado de conexión MQTT
2. **VerificarYReconectarServicios()** - Reconexión automática de servicios

### 8.2 SISTEMA DE RECONEXIÓN AUTOMÁTICA

**Método VerificarYReconectarServicios:**
```csharp
// Líneas 3250-3350
private async void VerificarYReconectarServicios()
{
    // Reconexión MQTT
    if (reconexionMQTTHabilitada && !MQTTConectado)
    {
        await VerificarYReconectarMQTT();
    }

    // Reconexión Modbus
    if (reconexionModbusHabilitada && !ModbusConectado)
    {
        await VerificarYReconectarModbus();
    }

    // Email no tiene reconexión automática (por diseño)
}
```

**Características de la reconexión:**
- Solo se activa después de conexión exitosa inicial
- Se deshabilita al desconectar manualmente
- Intervalos de reintento: cada segundo
- Logging detallado de intentos

### 8.3 TIMER DE REVISIÓN DE CORREOS

**En GestorDeCorreo.cs:**
```csharp
// Timer automático para revisar correos
private System.Timers.Timer timerRevision;

public void IniciarRevisionAutomatica()
{
    timerRevision = new System.Timers.Timer(configuracion.IntervaloProcesamiento);
    timerRevision.Elapsed += async (s, e) => await ProcesarCorreosAsync();
    timerRevision.Start();
}
```

**Configuración:**
- Intervalo: 300000ms (5 minutos) por defecto
- Procesamiento automático de correos nuevos
- Actualización automática de limitaciones horarias

---

## 9. MANEJO DE EXCEPCIONES {#manejo-excepciones}

### 9.1 EXCEPCIONES MODBUS

**ConnectionException:**
```csharp
catch (EasyModbus.Exceptions.ConnectionException ex)
{
    ModbusConectado = false; // Marcar como desconectado
    LogSafe($"❌ MODBUS: Error de conexión: {ex.Message}");

    // La reconexión automática se encargará del reintento
}
```

**Timeout Exception:**
```csharp
catch (System.TimeoutException ex)
{
    LogSafe($"⏱️ MODBUS: Timeout en operación: {ex.Message}");
    // No cambiar estado de conexión por timeout
}
```

### 9.2 EXCEPCIONES MQTT

**MqttCommunicationException:**
```csharp
catch (MQTTnet.Exceptions.MqttCommunicationException ex)
{
    MQTTConectado = false;
    LogSafe($"❌ MQTT: Error de comunicación: {ex.Message}");
}
```

**MqttProtocolViolationException:**
```csharp
catch (MQTTnet.Exceptions.MqttProtocolViolationException ex)
{
    LogSafe($"❌ MQTT: Violación de protocolo: {ex.Message}");
    // Reiniciar conexión
    await mqttUIManager.DesconectarAsync();
}
```

### 9.3 EXCEPCIONES EMAIL

**ServiceRequestException:**
```csharp
catch (Microsoft.Exchange.WebServices.Data.ServiceRequestException ex)
{
    correoConectado = false;
    LogSafe($"❌ EWS: Error de servicio: {ex.Message}");
}
```

**ServiceValidationException:**
```csharp
catch (Microsoft.Exchange.WebServices.Data.ServiceValidationException ex)
{
    LogSafe($"❌ EWS: Error de validación: {ex.Message}");
    // No cambiar estado de conexión
}
```

### 9.4 LOGGING THREAD-SAFE

**Método LogSafe:**
```csharp
// Líneas 3700-3720
private void LogSafe(string mensaje)
{
    try
    {
        if (InvokeRequired)
        {
            Invoke(new Action<string>(LogSafe), mensaje);
            return;
        }

        string timestamp = DateTime.Now.ToString("HH:mm:ss");
        rtbLog.AppendText($"{timestamp} {mensaje}\n");
        rtbLog.ScrollToCaret();
    }
    catch (Exception ex)
    {
        // Logging falló - no hacer nada para evitar bucles
        System.Diagnostics.Debug.WriteLine($"LogSafe failed: {ex.Message}");
    }
}
```

---

## 10. DIAGNÓSTICO DE AVERÍAS {#diagnostico-averias}

### 10.1 PROBLEMAS COMUNES MQTT

**Síntoma:** "MQTT conectado pero no recibe mensajes"
**Diagnóstico:**
1. Verificar suscripción a topics: `dgvTopicsMqtt` debe mostrar topics
2. Verificar logs: buscar "📤 Suscrito a topic"
3. Verificar firewall/proxy
4. Verificar credenciales

**Código de verificación:**
```csharp
// En MqttUIManager.cs - verificar suscripciones
foreach (string topic in topicsEnagas)
{
    LogSafe($"🔍 Verificando suscripción: {topic}");
    // Verificar en dgvTopicsMqtt si aparece el topic
}
```

**Síntoma:** "Bucle infinito de feedback"
**Diagnóstico:**
1. Verificar que NO se esté suscrito a topics FEEDBACK
2. Verificar que NO se esté leyendo topics FEEDBACK con LeerTopic()
3. Verificar logs: buscar "📤 Feedback enviado" repetitivo

**Solución aplicada:**
- Eliminado método `VerificarConfirmacionFeedback()`
- Topics FEEDBACK excluidos de array `topicsEnagas`
- Feedback unidireccional solamente

### 10.2 PROBLEMAS COMUNES MODBUS

**Síntoma:** "Connection timed out"
**Diagnóstico:**
1. Verificar IP y puerto: `ping {IP}`
2. Verificar Device ID
3. Verificar firewall
4. Verificar que el dispositivo esté encendido

**Código de diagnóstico:**
```csharp
// Verificación de conectividad básica
try
{
    using (var tcpClient = new System.Net.Sockets.TcpClient())
    {
        await tcpClient.ConnectAsync(txtModbusIp.Text, (int)nudModbusPuerto.Value);
        LogSafe("✅ TCP: Puerto accesible");
    }
}
catch (Exception ex)
{
    LogSafe($"❌ TCP: Puerto inaccesible: {ex.Message}");
}
```

**Síntoma:** "Modbus conectado pero no puede leer/escribir"
**Diagnóstico:**
1. Verificar Device ID (UnitIdentifier)
2. Verificar dirección de registro
3. Verificar permisos de escritura en el dispositivo

### 10.3 PROBLEMAS COMUNES EMAIL

**Síntoma:** "Error conectando al servidor EWS"
**Diagnóstico:**
1. Verificar URL del servidor
2. Verificar credenciales
3. Verificar conectividad a Internet
4. Verificar configuración de proxy corporativo

**Código de diagnóstico:**
```csharp
// Verificar conectividad EWS
try
{
    var service = new ExchangeService(ExchangeVersion.Exchange2013_SP1);
    service.Url = new Uri(txtEwsServerUrl.Text);
    service.Credentials = new WebCredentials(txtEwsUsuario.Text, txtEwsPassword.Text);

    // Test básico
    service.ResolveName("test");
    LogSafe("✅ EWS: Servidor accesible");
}
catch (Exception ex)
{
    LogSafe($"❌ EWS: Error de conexión: {ex.Message}");
}
```

### 10.4 PROBLEMAS DEL MODO AUTOMÁTICO

**Síntoma:** "Modo automático no envía valores"
**Diagnóstico:**
1. Verificar conectividad de sistemas: MQTT + Modbus + Email
2. Verificar que `modoAutomaticoActivo = true`
3. Verificar cálculo de prioridades
4. Verificar logs de `CalcularYEnviarValorModoAutomatico()`

**Código de diagnóstico:**
```csharp
// Verificar estado del modo automático
LogSafe($"🔍 DEBUG MODO AUTO:");
LogSafe($"  - Activo: {modoAutomaticoActivo}");
LogSafe($"  - Prioridad: {prioridadActiva}");
LogSafe($"  - Valor calculado: {valorModoAutomaticoCalculado}");
LogSafe($"  - SRAP activas: {hayParadasSRAPActivas}");
LogSafe($"  - Email activas: {hayLimitacionesEmailActivas}");
LogSafe($"  - CONSIGNAPA: {valorConsignaPa}");
```

### 10.5 HERRAMIENTAS DE DIAGNÓSTICO

**Logs del sistema:**
- Todos los eventos se registran en `rtbLog`
- Timestamps automáticos
- Códigos de emoji para fácil identificación:
  - ✅ Éxito
  - ❌ Error
  - 🔄 Proceso en curso
  - 📤 Envío de datos
  - 📊 Recepción de datos
  - 🔌 Conexión/desconexión
  - 🤖 Modo automático

**Estados del sistema:**
- `lblEstadoMqtt` - Estado MQTT
- `lblEstadoModbus` - Estado Modbus
- `lblEstadoCorreo` - Estado Email
- `lblEstadoSistemas` - Estado consolidado
- `lblPrioridadActiva` - Prioridad actual
- `lblValorEnviado` - Último valor enviado

**Verificación manual:**
- Botón "Refrescar MQTT" para actualización manual
- Botón "Procesar Correos" para procesamiento manual
- Botón "Forzar Cambio Hora" para debug de limitaciones
- Botones de lectura/escritura Modbus para pruebas

---

## CONCLUSIÓN

Este manual técnico documenta exhaustivamente todos los componentes, flujos y procedimientos del sistema Control de Producción ENAGAS. Cada sección incluye código específico, números de línea, y procedimientos de diagnóstico para facilitar el mantenimiento y resolución de problemas.

**Puntos críticos a recordar:**
1. **MQTT:** Feedback unidireccional, sin lectura de topics FEEDBACK
2. **Modbus:** Reconexión automática solo después de conexión inicial exitosa
3. **Email:** Limitaciones MAÑANA → HOY automáticamente a las 00:00
4. **SRAP:** Botones solo informativos, sin eventos Click
5. **Modo Automático:** Prioridades: SRAP > Email > Consigna normal
6. **Logging:** Thread-safe con timestamps automáticos
7. **Reconexión:** Automática para MQTT y Modbus, manual para Email

Para cualquier avería, seguir el flujo: **Verificar logs → Identificar síntoma → Aplicar diagnóstico → Verificar solución**.
```

