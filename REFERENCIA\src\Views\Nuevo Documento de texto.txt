using prueba.Clases;
using prueba.Models;
using prueba.Services;
using prueba.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel; // Added for PropertyChangedEventArgs
using System.Drawing;
using System.Globalization; // Added for DateTimeStyles
using System.IO; // Added for File and Path operations
using System.IO.Ports;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using WinFormsTimer = System.Windows.Forms.Timer;

namespace prueba
{
    public partial class Form1 : Form
    {
        private ProfileUIManager profileUIManager;
        public MqttService mqttService;
        private ConnectionProfileManager profileManager;
        public int? currentLoadedProfileIndex = null; // Para saber qué perfil de conexión está activo
        public ConnectionProfile currentActiveProfile; // Perfil activo, incluye ajustes MQTT y de Correo
        private WinFormsTimer checkTimer;
        private Button btnStartTimer;
        private Label lblTimerStatus;
        private WinFormsTimer modbusReadTimer;
        private TopicReadingManager _topicReadingManager;
        private TopicWritingManager _topicWritingManager; // Timer para lectura automática de Modbus
        private WinFormsTimer connectionRetryTimer; // Timer for retrying failed connections

        // EWS Periodic Verification System
        private WinFormsTimer ewsVerificationTimer;
        private int ewsRetryIntervalMinutes = 2; // Start with 2 minutes
        private bool ewsConnectionState = false; // Track EWS connection status
        private DateTime ewsLastVerificationTime = DateTime.MinValue;
        private readonly int[] ewsRetryIntervals = { 2, 5, 10, 15, 30 }; // Exponential backoff intervals in minutes
        private int ewsCurrentRetryIndex = 0; // Current index in retry intervals array

        private HourlyState todayStates;
        private HourlyState tomorrowStates;
        private DateTime lastUpdateDate = DateTime.MinValue; // Track last date when states were updated
        public ModbusViewModel _modbusViewModel;

        // Añadir al inicio de la clase Form1
        private GestorDeCorreo gestorDeCorreo;
        private FlowLayoutPanel panelHoyIconos;
        private FlowLayoutPanel panelMañanaIconos;
        private Label lblHoy;
        private Label lblMañana;
        private List<Panel> iconosHoy = new List<Panel>();
        private List<Panel> iconosMañana = new List<Panel>();

        // Delegado para actualizar el estado visual
        public delegate void UpdateStatusDelegate(Color color);
        private UpdateStatusDelegate updateStatusHandler;

        // Declarar una sola vez los controles
        private System.Windows.Forms.Label lblActiveSubscriptions;
        private System.Windows.Forms.ListBox listActiveSubscriptions;
        private System.Windows.Forms.Button btnUnsubscribe;

        private RichTextBox consoleTextBox;

        // Nuevos controles para la publicación
        private CheckedListBox lstTodosTopicos;
        private TextBox txtFiltroTopicos;
        private DataGridView gridTopicosLectura;
        private DataGridView gridTopicosEscritura;

        private DataGridView dgvTodosTopicos;

        private PublishConfigManager _publishConfigManager;
        private NavigationManager _navigationManager; // Added for navigation management
        public MqttConnectionUIManager _mqttConnectionUIManager;

        public List<TopicPublishConfig> GetCurrentPublishConfigurations()
        {
            // Inicializa la lista para almacenar las configuraciones.
            var configs = new List<TopicPublishConfig>();

            // Verifica si los DataGridViews necesarios están disponibles.
            // Si alguno no lo está, registra una advertencia y devuelve una lista vacía o null.
            if (dgvTodosTopicos == null || gridTopicosLectura == null || gridTopicosEscritura == null)
            {
                Logger.Log("No se pueden obtener las configuraciones de publicación actuales: los DataGridViews no están inicializados.", LogLevel.Warning);
                return configs; // Devuelve lista vacía, o podrías lanzar una excepción si es un estado inválido.
            }

            // Ensure any pending edits are committed before reading values
            try
            {
                if (!dgvTodosTopicos.IsDisposed)
                {
                    dgvTodosTopicos.CommitEdit(DataGridViewDataErrorContexts.Commit);
                    dgvTodosTopicos.EndEdit();
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Warning: Could not commit edits in dgvTodosTopicos: {ex.Message}", LogLevel.Warning);
            }

            // Collect checkbox states from dgvTodosTopicos
            foreach (DataGridViewRow row in dgvTodosTopicos.Rows)
            {
                if (row.IsNewRow) continue;

                string topicName = row.Cells["Topico"].Value?.ToString();
                if (string.IsNullOrEmpty(topicName)) continue;

                var config = configs.FirstOrDefault(c => c.TopicName == topicName);
                if (config == null)
                {
                    config = new TopicPublishConfig(topicName);
                    configs.Add(config);
                }

                config.IsForRead = ConvertToBoolean(row.Cells["Leer"].Value);
                config.IsForWrite = ConvertToBoolean(row.Cells["Escribir"].Value);


            }

            // Collect data from read topics table (gridTopicosLectura)
            foreach (DataGridViewRow row in gridTopicosLectura.Rows)
            {
                if (row.IsNewRow) continue;

                string topicName = row.Cells["Topico"].Value?.ToString();
                if (string.IsNullOrEmpty(topicName)) continue;

                var config = configs.FirstOrDefault(c => c.TopicName == topicName);
                if (config == null)
                {
                    config = new TopicPublishConfig(topicName);
                    config.IsForRead = true;
                    configs.Add(config);
                }
                config.Read_IdDispositivo = row.Cells["IdDispositivo"].Value?.ToString() ?? "";
                config.Read_DireccionPLC = row.Cells["DireccionPLC"].Value?.ToString() ?? "";
                config.Read_TasaRefresco = row.Cells["TasaRefresco"].Value?.ToString() ?? "10s";
            }

            // Collect data from write topics table (gridTopicosEscritura)
            foreach (DataGridViewRow row in gridTopicosEscritura.Rows)
            {
                if (row.IsNewRow) continue;

                string topicName = row.Cells["Topico"].Value?.ToString();
                if (string.IsNullOrEmpty(topicName)) continue;

                var config = configs.FirstOrDefault(c => c.TopicName == topicName);
                if (config == null)
                {
                    config = new TopicPublishConfig(topicName);
                    config.IsForWrite = true;
                    configs.Add(config);
                }
                config.Write_IdDispositivo = row.Cells["IdDispositivo"].Value?.ToString() ?? "";
                config.Write_DireccionPLC = row.Cells["DireccionPLC"].Value?.ToString() ?? "";
                config.Write_TasaRefresco = row.Cells["TasaRefresco"].Value?.ToString() ?? "Realtime";


            }

            return configs;
        }

        /// <summary>
        /// Converts various value types to boolean, handling null values and different data types
        /// that might be stored in DataGridView checkbox cells
        /// </summary>
        /// <param name="value">The value to convert</param>
        /// <returns>Boolean representation of the value</returns>
        private bool ConvertToBoolean(object value)
        {
            if (value == null || value == DBNull.Value)
                return false;

            if (value is bool boolValue)
                return boolValue;

            if (value is string stringValue)
            {
                if (bool.TryParse(stringValue, out bool parsedBool))
                    return parsedBool;
                // Handle common string representations
                return stringValue.Equals("1") || stringValue.Equals("true", StringComparison.OrdinalIgnoreCase) ||
                       stringValue.Equals("yes", StringComparison.OrdinalIgnoreCase) || stringValue.Equals("on", StringComparison.OrdinalIgnoreCase);
            }

            if (value is int intValue)
                return intValue != 0;

            // Try to convert using Convert.ToBoolean as fallback
            try
            {
                return Convert.ToBoolean(value);
            }
            catch
            {
                return false;
            }
        }

        public Form1()
        {
            InitializeComponent();

            // Set up static layout properties
            SetupStaticLayout();

            // Inicializar la consola primero
            InitializeConsole();

            // Después inicializar el logger
            Logger.Initialize(consoleTextBox);
            Logger.Log("Aplicación iniciada", LogLevel.Info);

            // Initialize status indicators with default disconnected state
            UpdateMqttStatus(false, "MQTT: Desconectado");
            UpdateEmailStatus(false, "Correo: Desconectado");
            UpdateModbusStatus(false, "Modbus: Desconectado");

            _modbusViewModel = new ModbusViewModel();
            Logger.Log("ModbusViewModel inicializado", LogLevel.Info);
            _modbusViewModel.PropertyChanged += ModbusViewModel_PropertyChanged;




            _publishConfigManager = new PublishConfigManager();

            InitializeEmailChecking(); // Añadir esta llamada

            // Initialize hourly state tracking
            InitializeHourlyStateTracking();

            // Initialize EWS periodic verification timer (but don't start it yet)
            InitializeEwsPeriodicVerification();

            // Inicializar servicios
            profileManager = new ConnectionProfileManager();
            currentActiveProfile = new ConnectionProfile();

            profileUIManager = new ProfileUIManager(this, profileManager, panelProfiles);

            // Old subscription management event handlers removed - using modern dgvSinopticoData table

            // Inicializar gestor de correo con la configuración por defecto
            InitializeEmailManager();

            // Configurar el delegado para actualizar el estado
            updateStatusHandler = (color) =>
            {
                if (InvokeRequired)
                {
                    Invoke(new Action<Color>(updateStatusHandler), color);
                    return;
                }
                circleStatus.BackColor = color;

                // Actualizar el texto del estado en español
                if (color == Color.Green)
                {
                    lblStatus.Text = "Conectado";
                    lblStatus.ForeColor = Color.LightGreen;
                }
                else if (color == Color.Red)
                {
                    lblStatus.Text = "En error";
                    lblStatus.ForeColor = Color.LightCoral;
                }
                else if (color == Color.Blue)
                {
                    lblStatus.Text = "Conectando...";
                    lblStatus.ForeColor = Color.LightBlue;
                }
                else // Gray or other colors
                {
                    lblStatus.Text = "Desconectado";
                    lblStatus.ForeColor = Color.Silver;
                }
            };

            // Inicializar el servicio MQTT después de crear la UI
            mqttService = new MqttService(new Action<Color>(color => updateStatusHandler(color)));
            mqttService.MessagesUpdated += MqttService_MessagesUpdated;
            mqttService.MessagesUpdated += OnMqttTopicValueChanged;

            // Asignar un ClientID por defecto si es necesario
            if (string.IsNullOrEmpty(txtClientID.Text))
            {
                txtClientID.Text = "mqtt_client_" + Guid.NewGuid().ToString().Substring(0, 8);
            }

            // Cargar los perfiles existentes
            profileUIManager.CreateProfileCards();

            // Inicializar controles de publicación
            InitializePublishControls();

            _navigationManager = new NavigationManager(
                btnNavConnection, btnNavConfig, btnNavSinoptico, btnNavMail, btnNavModbus,
                panelConnectionContent, panelConfigContent, panelSinopticoContent, panelMailContent, panelModbusContent
            );
            _mqttConnectionUIManager = new MqttConnectionUIManager(this, mqttService,
                cmbHost, txtHostAddress, txtPort, txtClientID, txtUsername, txtPassword,
                chkSSL_TLS, rdoCASigned); // Initialize MqttConnectionUIManager with all required controls

            // Wire up MQTT connection button event handlers
            if (btnConnect != null)
                btnConnect.Click += async (s, e) => await _mqttConnectionUIManager.ConnectToBroker();

            if (btnDisconnect != null)
                btnDisconnect.Click += async (s, e) =>
                {
                    try
                    {
                        Logger.Log("MQTT Disconnect button clicked", LogLevel.Info);
                        await mqttService.DisconnectAsync();
                        Logger.Log("MQTT disconnection completed", LogLevel.Info);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log($"Error during MQTT disconnection: {ex.Message}", LogLevel.Error);
                        MessageBox.Show($"Error al desconectar MQTT: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                };

            // Initialize Modbus UI and Data Bindings
            InitializeModbusControls();

            // Initialize Modbus read timer
            InitializeModbusReadTimer();

            // Initialize Topic Reading Manager for selective Modbus reading
            _topicReadingManager = new TopicReadingManager(ReadFromModbusAndPublish);

            // Initialize Topic Writing Manager for automatic Modbus writing
            _topicWritingManager = new TopicWritingManager(WriteToModbusAsyncWrapper, () => mqttService?.GetLastMessages());
            Logger.Log("TopicWritingManager initialized", LogLevel.Info);

            // Set up email control event handlers
            if (btnProbar != null)
                btnProbar.Click += BtnProbar_Click;
            if (btnProcesarEmails != null)
                btnProcesarEmails.Click += BtnProcesarEmails_Click;

            if (chkUseEws != null)
                chkUseEws.CheckedChanged += ChkUseEws_CheckedChanged;

            // Initialize email controls state
            UpdateEmailControlsState();

            // Initialize connection retry timer
            InitializeConnectionRetryTimer();

            // Initialize EWS periodic verification timer
            InitializeEwsPeriodicVerification();

        }

        private void SetupStaticLayout()
        {
            // Set form to always match screen size
            this.WindowState = FormWindowState.Maximized;
            this.FormBorderStyle = FormBorderStyle.None;

            // Get screen dimensions and set form size accordingly
            Rectangle screenBounds = Screen.PrimaryScreen.Bounds;
            this.Size = new Size(screenBounds.Width, screenBounds.Height);
            this.Location = new Point(0, 0);

            // Disable auto-scaling to maintain static layout
            this.AutoScaleMode = AutoScaleMode.None;

            // Add key handler to allow exit with Alt+F4 or Escape
            this.KeyPreview = true;
            this.KeyDown += Form1_KeyDown;
        }

        private void Form1_KeyDown(object sender, KeyEventArgs e)
        {
            // Only allow exit with Alt+F4, not Escape key
            if (e.Alt && e.KeyCode == Keys.F4)
            {
                this.Close();
            }
            // Escape key no longer closes the application
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            // Close the application when the close button is clicked
            this.Close();
        }

        private void InitializeModbusControls()
        {
            // Wire up event handlers for Left Panel Configuration Controls
            if (this.btnModbusSaveConfigLeft != null)
                this.btnModbusSaveConfigLeft.Click += (s, e) => { UpdateModbusViewModelFromUI(isLeft: true); _modbusViewModel.SaveConfigCommand.Execute(null); };
            if (this.btnModbusNewConfigLeft != null)
                this.btnModbusNewConfigLeft.Click += (s, e) => _modbusViewModel.NewConfigCommand.Execute(null);
            if (this.btnModbusDeleteConfigLeft != null)
                this.btnModbusDeleteConfigLeft.Click += (s, e) => { UpdateModbusViewModelFromUI(isLeft: true); _modbusViewModel.DeleteConfigCommand.Execute(null); };
            if (this.cmbModbusConfigLeft != null)
                this.cmbModbusConfigLeft.SelectedIndexChanged += CmbModbusConfigLeft_SelectedIndexChanged;
            if (this.txtModbusConfigNameLeft != null)
                this.txtModbusConfigNameLeft.TextChanged += (s, e) => { if (_modbusViewModel.SelectedConfigurationLeft != null) { _modbusViewModel.SelectedConfigurationLeft.Name = txtModbusConfigNameLeft.Text; } };

            // Wire up event handlers for Right Panel Configuration Controls
            if (this.btnModbusSaveConfigRight != null)
                this.btnModbusSaveConfigRight.Click += (s, e) => { UpdateModbusViewModelFromUI(isLeft: false); _modbusViewModel.SaveConfigCommand.Execute(null); };
            if (this.btnModbusNewConfigRight != null)
                this.btnModbusNewConfigRight.Click += (s, e) => _modbusViewModel.NewConfigCommand.Execute(null);
            if (this.btnModbusDeleteConfigRight != null)
                this.btnModbusDeleteConfigRight.Click += (s, e) => { UpdateModbusViewModelFromUI(isLeft: false); _modbusViewModel.DeleteConfigCommand.Execute(null); };
            if (this.cmbModbusConfigRight != null)
                this.cmbModbusConfigRight.SelectedIndexChanged += CmbModbusConfigRight_SelectedIndexChanged;
            if (this.txtModbusConfigNameRight != null)
                this.txtModbusConfigNameRight.TextChanged += (s, e) => { if (_modbusViewModel.SelectedConfigurationRight != null) { _modbusViewModel.SelectedConfigurationRight.Name = txtModbusConfigNameRight.Text; } };

            // Wire up event handlers for Left Panel (Connection Settings and Operations)
            if (this.btnModbusConnectLeft != null)
                this.btnModbusConnectLeft.Click += (s, e) => { UpdateModbusViewModelFromUI(isLeft: true); _modbusViewModel.ConnectCommandLeft.Execute(null); };
            if (this.btnModbusDisconnectLeft != null)
                this.btnModbusDisconnectLeft.Click += (s, e) => _modbusViewModel.DisconnectCommandLeft.Execute(null);
            if (this.rdoModbusTCPLeft != null)
                this.rdoModbusTCPLeft.CheckedChanged += (s,e) => UpdateModbusControlsState(isLeft: true);
            if (this.rdoModbusRTULeft != null)
                this.rdoModbusRTULeft.CheckedChanged += (s,e) => UpdateModbusControlsState(isLeft: true);

            // Left panel operations
            if (this.btnModbusReadHoldingRegistersLeft != null)
                this.btnModbusReadHoldingRegistersLeft.Click += (s, e) => { UpdateModbusOperationParametersFromUI(isLeft: true); _modbusViewModel.ReadHoldingRegistersCommandLeft.Execute(null); };
            if (this.btnModbusReadInputRegistersLeft != null)
                this.btnModbusReadInputRegistersLeft.Click += (s, e) => { UpdateModbusOperationParametersFromUI(isLeft: true); _modbusViewModel.ReadInputRegistersCommandLeft.Execute(null); };
            if (this.btnModbusReadCoilsLeft != null)
                this.btnModbusReadCoilsLeft.Click += (s, e) => { UpdateModbusOperationParametersFromUI(isLeft: true); _modbusViewModel.ReadCoilsCommandLeft.Execute(null); };
            if (this.btnModbusReadDiscreteInputsLeft != null)
                this.btnModbusReadDiscreteInputsLeft.Click += (s, e) => { UpdateModbusOperationParametersFromUI(isLeft: true); _modbusViewModel.ReadDiscreteInputsCommandLeft.Execute(null); };
            if (this.btnModbusWriteSingleRegisterLeft != null)
                this.btnModbusWriteSingleRegisterLeft.Click += (s, e) => { UpdateModbusOperationParametersFromUI(isLeft: true); _modbusViewModel.WriteSingleRegisterCommandLeft.Execute(null); };
            if (this.btnModbusWriteSingleCoilLeft != null)
                this.btnModbusWriteSingleCoilLeft.Click += (s, e) => { UpdateModbusOperationParametersFromUI(isLeft: true); _modbusViewModel.WriteSingleCoilCommandLeft.Execute(null); };

            // Wire up event handlers for Right Panel (Independent Connection Settings and Operations)
            if (this.btnModbusConnectRight != null)
                this.btnModbusConnectRight.Click += (s, e) => { UpdateModbusViewModelFromUI(isLeft: false); _modbusViewModel.ConnectCommandRight.Execute(null); };
            if (this.btnModbusDisconnectRight != null)
                this.btnModbusDisconnectRight.Click += (s, e) => _modbusViewModel.DisconnectCommandRight.Execute(null);
            if (this.rdoModbusTCPRight != null)
                this.rdoModbusTCPRight.CheckedChanged += (s,e) => UpdateModbusControlsState(isLeft: false);
            if (this.rdoModbusRTURight != null)
                this.rdoModbusRTURight.CheckedChanged += (s,e) => UpdateModbusControlsState(isLeft: false);

            if (this.btnModbusReadHoldingRegistersRight != null)
                this.btnModbusReadHoldingRegistersRight.Click += (s, e) => { UpdateModbusOperationParametersFromUI(isLeft: false); _modbusViewModel.ReadHoldingRegistersCommandRight.Execute(null); };
            if (this.btnModbusReadInputRegistersRight != null)
                this.btnModbusReadInputRegistersRight.Click += (s, e) => { UpdateModbusOperationParametersFromUI(isLeft: false); _modbusViewModel.ReadInputRegistersCommandRight.Execute(null); };
            if (this.btnModbusReadCoilsRight != null)
                this.btnModbusReadCoilsRight.Click += (s, e) => { UpdateModbusOperationParametersFromUI(isLeft: false); _modbusViewModel.ReadCoilsCommandRight.Execute(null); };
            if (this.btnModbusReadDiscreteInputsRight != null)
                this.btnModbusReadDiscreteInputsRight.Click += (s, e) => { UpdateModbusOperationParametersFromUI(isLeft: false); _modbusViewModel.ReadDiscreteInputsCommandRight.Execute(null); };
            if (this.btnModbusWriteSingleRegisterRight != null)
                this.btnModbusWriteSingleRegisterRight.Click += (s, e) => { UpdateModbusOperationParametersFromUI(isLeft: false); _modbusViewModel.WriteSingleRegisterCommandRight.Execute(null); };
            if (this.btnModbusWriteSingleCoilRight != null)
                this.btnModbusWriteSingleCoilRight.Click += (s, e) => { UpdateModbusOperationParametersFromUI(isLeft: false); _modbusViewModel.WriteSingleCoilCommandRight.Execute(null); };

            // Populate ComboBoxes for RTU settings for both panels
            if (this.cmbModbusComPortLeft != null && this.cmbModbusBaudRateLeft != null && this.cmbModbusParityLeft != null && this.cmbModbusDataBitsLeft != null && this.cmbModbusStopBitsLeft != null)
                PopulateModbusRtuComboBoxes(this.cmbModbusComPortLeft, this.cmbModbusBaudRateLeft, this.cmbModbusParityLeft, this.cmbModbusDataBitsLeft, this.cmbModbusStopBitsLeft);
            if (this.cmbModbusComPortRight != null && this.cmbModbusBaudRateRight != null && this.cmbModbusParityRight != null && this.cmbModbusDataBitsRight != null && this.cmbModbusStopBitsRight != null)
                PopulateModbusRtuComboBoxes(this.cmbModbusComPortRight, this.cmbModbusBaudRateRight, this.cmbModbusParityRight, this.cmbModbusDataBitsRight, this.cmbModbusStopBitsRight);

            // Initial UI state update from ViewModel
            UpdateUIFromModbusViewModel(isLeft: true);
            UpdateUIFromModbusViewModel(isLeft: false);
            UpdateModbusControlsState(isLeft: true);
            UpdateModbusControlsState(isLeft: false);
        }

        /// <summary>
        /// Initializes the legacy Modbus read timer (deprecated - use selective reading instead)
        /// </summary>
        private void InitializeModbusReadTimer()
        {
            modbusReadTimer = new WinFormsTimer();
            modbusReadTimer.Interval = 10000; // 10 seconds default interval
            modbusReadTimer.Tick += async (s, e) =>
            {
                try
                {
                    await PerformAutomaticModbusReading();
                }
                catch (Exception ex)
                {
                    Logger.Log($"Error en lectura automática de Modbus: {ex.Message}", LogLevel.Error);
                }
            };

            // Don't start the timer automatically - use selective reading instead
            Logger.Log("Legacy Modbus read timer initialized (not started) - use selective reading instead", LogLevel.Info);
        }

        /// <summary>
        /// Initializes the connection retry timer for failed devices
        /// </summary>
        private void InitializeConnectionRetryTimer()
        {
            connectionRetryTimer = new WinFormsTimer();
            connectionRetryTimer.Interval = 30000; // 30 seconds retry interval
            connectionRetryTimer.Tick += async (s, e) =>
            {
                try
                {
                    await RetryFailedConnections();
                }
                catch (Exception ex)
                {
                    Logger.Log($"Error during connection retry: {ex.Message}", LogLevel.Error);
                }
            };

            connectionRetryTimer.Start();
            Logger.Log("Connection retry timer initialized and started (30 second interval)", LogLevel.Info);
        }

        /// <summary>
        /// Initializes the EWS periodic verification timer with adaptive retry intervals
        /// Timer is created but NOT started automatically - must be started explicitly
        /// </summary>
        private void InitializeEwsPeriodicVerification()
        {
            if (ewsVerificationTimer != null)
            {
                ewsVerificationTimer.Stop();
                ewsVerificationTimer.Dispose();
            }

            ewsVerificationTimer = new WinFormsTimer();
            ewsVerificationTimer.Interval = ewsRetryIntervalMinutes * 60 * 1000; // Convert minutes to milliseconds
            ewsVerificationTimer.Tick += async (s, e) =>
            {
                try
                {
                    await PerformEwsPeriodicVerification();
                }
                catch (Exception ex)
                {
                    Logger.Log($"Error during EWS periodic verification: {ex.Message}", LogLevel.Error);
                }
            };

            // DO NOT start the timer automatically - it will be started by StartEmailProcessingSequentially
            Logger.Log("EWS periodic verification timer initialized (not started)", LogLevel.Info);
        }

        /// <summary>
        /// Performs EWS periodic verification with adaptive retry intervals
        /// </summary>
        private async Task PerformEwsPeriodicVerification()
        {
            // Only perform verification if EWS is configured
            if (currentActiveProfile?.UseEws != true)
            {
                Logger.Log("EWS periodic verification skipped - EWS not configured", LogLevel.Debug);
                return;
            }

            Logger.Log($"Starting EWS periodic verification (attempt interval: {ewsRetryIntervalMinutes} minutes)", LogLevel.Info);
            ewsLastVerificationTime = DateTime.Now;

            try
            {
                // Apply current EWS UI values to profile before testing
                ApplyEwsUIToProfile();

                // Initialize email manager for EWS
                InitializeEmailManager();

                // Perform EWS connection test with the established 20-second timeout
                bool connectionSuccess = false;
                if (gestorDeCorreo != null)
                {
                    connectionSuccess = await gestorDeCorreo.ProbarConexionAsync().ConfigureAwait(false);
                }

                // Update connection state and UI
                ewsConnectionState = connectionSuccess;

                // Update UI on the UI thread
                this.Invoke(new Action(() =>
                {
                    if (connectionSuccess)
                    {
                        if (panelEwsEstado != null)
                        {
                            panelEwsEstado.BackColor = Color.Green;
                        }

                        Logger.Log("EWS periodic verification successful", LogLevel.Info);
                    }
                    else
                    {
                        if (panelEwsEstado != null)
                        {
                            panelEwsEstado.BackColor = Color.Red;
                        }

                        Logger.Log("EWS periodic verification failed", LogLevel.Warning);
                    }
                }));

                // Handle retry interval adjustment
                if (connectionSuccess)
                {
                    ResetEwsRetryInterval();
                }
                else
                {
                    IncreaseEwsRetryInterval();
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error during EWS periodic verification: {ex.Message}", LogLevel.Error);
                ewsConnectionState = false;

                // Update UI to show error state
                this.Invoke(new Action(() =>
                {
                    if (panelEwsEstado != null)
                    {
                        panelEwsEstado.BackColor = Color.Red;
                    }

                }));

                // Increase retry interval on error
                IncreaseEwsRetryInterval();
            }
        }

        /// <summary>
        /// Resets EWS retry interval to the initial value (2 minutes) after successful connection
        /// </summary>
        private void ResetEwsRetryInterval()
        {
            if (ewsCurrentRetryIndex > 0)
            {
                ewsCurrentRetryIndex = 0;
                ewsRetryIntervalMinutes = ewsRetryIntervals[ewsCurrentRetryIndex];
                UpdateEwsVerificationTimer();
                Logger.Log($"EWS connection successful - retry interval reset to {ewsRetryIntervalMinutes} minutes", LogLevel.Info);
            }
        }

        /// <summary>
        /// Increases EWS retry interval using exponential backoff after failed connection
        /// </summary>
        private void IncreaseEwsRetryInterval()
        {
            if (ewsCurrentRetryIndex < ewsRetryIntervals.Length - 1)
            {
                ewsCurrentRetryIndex++;
                ewsRetryIntervalMinutes = ewsRetryIntervals[ewsCurrentRetryIndex];
                UpdateEwsVerificationTimer();
                Logger.Log($"EWS connection failed - retry interval increased to {ewsRetryIntervalMinutes} minutes", LogLevel.Warning);
            }
            else
            {
                Logger.Log($"EWS connection failed - retry interval remains at maximum {ewsRetryIntervalMinutes} minutes", LogLevel.Warning);
            }
        }

        /// <summary>
        /// Updates the EWS verification timer interval
        /// </summary>
        private void UpdateEwsVerificationTimer()
        {
            if (ewsVerificationTimer != null)
            {
                ewsVerificationTimer.Stop();
                ewsVerificationTimer.Interval = ewsRetryIntervalMinutes * 60 * 1000; // Convert minutes to milliseconds

                // Only restart if EWS is configured
                if (currentActiveProfile?.UseEws == true)
                {
                    ewsVerificationTimer.Start();
                }
            }
        }

        /// <summary>
        /// Starts EWS periodic verification when EWS is enabled
        /// </summary>
        public void StartEwsPeriodicVerification()
        {
            if (ewsVerificationTimer != null && currentActiveProfile?.UseEws == true)
            {
                ewsVerificationTimer.Start();



                Logger.Log($"EWS periodic verification started ({ewsRetryIntervalMinutes} minute interval)", LogLevel.Info);
            }
        }

        /// <summary>
        /// Stops EWS periodic verification
        /// </summary>
        public void StopEwsPeriodicVerification()
        {
            if (ewsVerificationTimer != null)
            {
                ewsVerificationTimer.Stop();



                Logger.Log("EWS periodic verification stopped", LogLevel.Info);
            }
        }

        /// <summary>
        /// Determines if a Modbus configuration is using default/placeholder values
        /// </summary>
        /// <param name="config">The configuration to check</param>
        /// <returns>True if configuration appears to be a default/placeholder configuration</returns>
        private bool IsDefaultModbusConfiguration(ModbusConfiguration config)
        {
            if (config == null)
                return true;

            // Check for default names
            var defaultNames = new[] {
                "Default Configuration",
                "Default TCP",
                "Default RTU",
                "Nueva Configuración",
                "New Configuration"
            };

            if (defaultNames.Any(name => config.Name.Equals(name, StringComparison.OrdinalIgnoreCase)))
            {
                return true;
            }

            // Check for default TCP configuration values
            if (config.ConnectionType == ModbusService.ModbusConnectionType.TCP)
            {
                return (config.IpAddress == "127.0.0.1" || config.IpAddress == "localhost") &&
                       config.Port == 502 &&
                       config.DeviceId == 1;
            }
            else // RTU
            {
                return config.ComPort == "COM1" &&
                       config.BaudRate == 9600 &&
                       config.DeviceId == 1;
            }
        }

        /// <summary>
        /// Attempts to retry failed connections for Modbus and MQTT devices (EWS excluded to prevent hanging)
        /// </summary>
        private async Task RetryFailedConnections()
        {
            try
            {
                // Check and retry left Modbus connection if configured but not connected
                if (_modbusViewModel?.SelectedConfigurationLeft != null && !_modbusViewModel.IsConnectedLeft)
                {
                    // Skip retry for default configurations to prevent unwanted connection attempts
                    if (IsDefaultModbusConfiguration(_modbusViewModel.SelectedConfigurationLeft))
                    {
                        //Logger.Log($"Skipping retry for left Modbus: '{_modbusViewModel.SelectedConfigurationLeft.Name}' is a default configuration", LogLevel.Info);
                    }
                    else
                    {
                        Logger.Log("Attempting to retry left Modbus connection...", LogLevel.Info);
                        _modbusViewModel.ConnectCommandLeft.Execute(null);
                    }
                }

                // Check and retry right Modbus connection if configured but not connected
                if (_modbusViewModel?.SelectedConfigurationRight != null && !_modbusViewModel.IsConnectedRight)
                {
                    // Skip retry for default configurations to prevent unwanted connection attempts
                    if (IsDefaultModbusConfiguration(_modbusViewModel.SelectedConfigurationRight))
                    {
                        //Logger.Log($"Skipping retry for right Modbus: '{_modbusViewModel.SelectedConfigurationRight.Name}' is a default configuration", LogLevel.Info);
                    }
                    else
                    {
                        Logger.Log("Attempting to retry right Modbus connection...", LogLevel.Info);
                        _modbusViewModel.ConnectCommandRight.Execute(null);
                    }
                }

                // Check and retry MQTT connection if configured but not connected
                if (mqttService != null && !mqttService.IsConnected && currentActiveProfile != null)
                {
                    Logger.Log("Attempting to retry MQTT connection...", LogLevel.Info);
                    await _mqttConnectionUIManager.ConnectToBroker();
                }

                // NOTE: EWS connections are NOT retried automatically to prevent hanging issues
                // EWS connections should be tested manually by the user when needed
            }
            catch (Exception ex)
            {
                Logger.Log($"Error in RetryFailedConnections: {ex.Message}", LogLevel.Warning);
            }
        }

        private void PopulateModbusRtuComboBoxes(ComboBox cmbComPort, ComboBox cmbBaudRate, ComboBox cmbParity, ComboBox cmbDataBits, ComboBox cmbStopBits)
        {
            // COM Ports
            cmbComPort.Items.AddRange(SerialPort.GetPortNames());
            if (cmbComPort.Items.Count > 0) cmbComPort.SelectedIndex = 0;
            else Logger.Log("No COM ports found on this system.", LogLevel.Warning);

            // Baud Rates
            cmbBaudRate.Items.AddRange(new object[] { 9600, 19200, 38400, 57600, 115200 });
            cmbBaudRate.SelectedItem = 9600;

            // Parity
            cmbParity.DataSource = Enum.GetValues(typeof(System.IO.Ports.Parity));
            cmbParity.SelectedItem = System.IO.Ports.Parity.None;

            // Data Bits
            cmbDataBits.Items.AddRange(new object[] { 7, 8 });
            cmbDataBits.SelectedItem = 8;

            // Stop Bits
            cmbStopBits.DataSource = Enum.GetValues(typeof(System.IO.Ports.StopBits));
            cmbStopBits.SelectedItem = System.IO.Ports.StopBits.One;
        }

        private void ModbusViewModel_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            // Determine if the property is for Left or Right instance
            bool isLeftProperty = e.PropertyName.EndsWith("Left");
            bool isRightProperty = e.PropertyName.EndsWith("Right");

            // More robustly check specific properties to avoid acting on unrelated changes
            if (e.PropertyName == nameof(_modbusViewModel.IsConnectedLeft) ||
                e.PropertyName == nameof(_modbusViewModel.ConnectionStatusLeft) ||
                e.PropertyName == nameof(_modbusViewModel.LastErrorLeft) ||
                e.PropertyName == nameof(_modbusViewModel.AvailableConfigurations) || // Shared, update both
                e.PropertyName == nameof(_modbusViewModel.SelectedConfigurationLeft) ||
                e.PropertyName == nameof(_modbusViewModel.ReadResultsLeft))
            {
                 BeginInvoke(new Action(() => UpdateUIFromModbusViewModel(isLeft: true)));
            }

            if (e.PropertyName == nameof(_modbusViewModel.IsConnectedRight) ||
                e.PropertyName == nameof(_modbusViewModel.ConnectionStatusRight) ||
                e.PropertyName == nameof(_modbusViewModel.LastErrorRight) ||
                e.PropertyName == nameof(_modbusViewModel.AvailableConfigurations) || // Shared, update both
                e.PropertyName == nameof(_modbusViewModel.SelectedConfigurationRight) ||
                e.PropertyName == nameof(_modbusViewModel.ReadResultsRight))
            {
                 BeginInvoke(new Action(() => UpdateUIFromModbusViewModel(isLeft: false)));
            }

            // For shared properties like AvailableConfigurations, update both sides if necessary
            if (e.PropertyName == nameof(_modbusViewModel.AvailableConfigurations))
            {
                BeginInvoke(new Action(() => {
                    UpdateUIFromModbusViewModel(isLeft: true);
                    UpdateUIFromModbusViewModel(isLeft: false);
                        }));
                    }

            // Update device dropdowns when connection status changes
            if (e.PropertyName == nameof(_modbusViewModel.IsConnectedLeft) ||
                e.PropertyName == nameof(_modbusViewModel.IsConnectedRight))
            {
                BeginInvoke(new Action(PopulateDeviceIdDropdowns));
            }
        }

        private bool _isUpdatingModbusUI = false; // Flag to prevent recursive calls

        public void UpdateUIFromModbusViewModel(bool isLeft)
        {
            // Prevent recursive calls that cause infinite loops
            if (_isUpdatingModbusUI)
            {
                return;
            }

            try
            {
                _isUpdatingModbusUI = true;

                if (isLeft)
                {
                    // Update left panel status
                    if (lblModbusStatusLeft != null)
                    {
                        lblModbusStatusLeft.Text = $"Estado (Izq): {_modbusViewModel.ConnectionStatusLeft}";
                        lblModbusStatusLeft.ForeColor = _modbusViewModel.IsConnectedLeft ? Color.Green : Color.Red;
                    }

                    // Update left panel configuration ComboBox
                    if (cmbModbusConfigLeft != null)
                    {

                        // Completely remove all event handlers to prevent any recursive calls
                        cmbModbusConfigLeft.SelectedIndexChanged -= CmbModbusConfigLeft_SelectedIndexChanged;

                        cmbModbusConfigLeft.DataSource = null;
                        cmbModbusConfigLeft.DataSource = _modbusViewModel.AvailableConfigurations.ToList();
                        cmbModbusConfigLeft.DisplayMember = "Name";
                        cmbModbusConfigLeft.ValueMember = "Name";

                        // Set selected item without triggering events
                        if (_modbusViewModel.SelectedConfigurationLeft != null)
                        {
                            cmbModbusConfigLeft.SelectedItem = _modbusViewModel.SelectedConfigurationLeft;
                        }

                        // Re-attach event handler only once
                        cmbModbusConfigLeft.SelectedIndexChanged += CmbModbusConfigLeft_SelectedIndexChanged;
                    }

                    // Update left panel configuration name TextBox
                    if (txtModbusConfigNameLeft != null)
                    {
                        txtModbusConfigNameLeft.Text = _modbusViewModel.SelectedConfigurationLeft?.Name ?? "";
                    }

                    // Update left panel connection settings
                    if (_modbusViewModel.SelectedConfigurationLeft != null)
                    {
                        if (rdoModbusTCPLeft != null)
                            rdoModbusTCPLeft.Checked = _modbusViewModel.SelectedConfigurationLeft.ConnectionType == ModbusService.ModbusConnectionType.TCP;
                        if (rdoModbusRTULeft != null)
                            rdoModbusRTULeft.Checked = _modbusViewModel.SelectedConfigurationLeft.ConnectionType == ModbusService.ModbusConnectionType.RTU;
                        if (txtModbusIPLeft != null)
                            txtModbusIPLeft.Text = _modbusViewModel.SelectedConfigurationLeft.IpAddress;
                        if (nudModbusPortLeft != null)
                            nudModbusPortLeft.Value = _modbusViewModel.SelectedConfigurationLeft.Port;
                        if (nudModbusDeviceIdLeft != null)
                            nudModbusDeviceIdLeft.Value = _modbusViewModel.SelectedConfigurationLeft.DeviceId;

                        if (cmbModbusComPortLeft != null)
                            cmbModbusComPortLeft.SelectedItem = _modbusViewModel.SelectedConfigurationLeft.ComPort;
                        if (cmbModbusBaudRateLeft != null)
                            cmbModbusBaudRateLeft.SelectedItem = _modbusViewModel.SelectedConfigurationLeft.BaudRate;
                        if (cmbModbusParityLeft != null)
                            cmbModbusParityLeft.SelectedItem = _modbusViewModel.SelectedConfigurationLeft.Parity;
                        if (cmbModbusDataBitsLeft != null)
                            cmbModbusDataBitsLeft.SelectedItem = _modbusViewModel.SelectedConfigurationLeft.DataBits;
                        if (cmbModbusStopBitsLeft != null)
                            cmbModbusStopBitsLeft.SelectedItem = _modbusViewModel.SelectedConfigurationLeft.StopBits;
                    }

                    // Update left panel read results
                    if (dgvModbusReadResultsLeft != null)
                    {
                        dgvModbusReadResultsLeft.DataSource = null;
                        dgvModbusReadResultsLeft.DataSource = _modbusViewModel.ReadResultsLeft.ToList();
                        if (dgvModbusReadResultsLeft.ColumnCount == 0 && _modbusViewModel.ReadResultsLeft.Any())
                            dgvModbusReadResultsLeft.AutoGenerateColumns = true;
                    }

                    UpdateModbusControlsState(isLeft: true);
                }
                else // Right panel (Independent instance)
                {
                    // Update right panel status
                    if (lblModbusStatusRight != null)
                    {
                        lblModbusStatusRight.Text = $"Estado (Der): {_modbusViewModel.ConnectionStatusRight}";
                        lblModbusStatusRight.ForeColor = _modbusViewModel.IsConnectedRight ? Color.Green : Color.Red;
                    }

                    // Update right panel configuration ComboBox
                    if (cmbModbusConfigRight != null)
                    {

                        // Completely remove all event handlers to prevent any recursive calls
                        cmbModbusConfigRight.SelectedIndexChanged -= CmbModbusConfigRight_SelectedIndexChanged;

                        cmbModbusConfigRight.DataSource = null;
                        cmbModbusConfigRight.DataSource = _modbusViewModel.AvailableConfigurations.ToList();
                        cmbModbusConfigRight.DisplayMember = "Name";
                        cmbModbusConfigRight.ValueMember = "Name";

                        // Set selected item without triggering events
                        if (_modbusViewModel.SelectedConfigurationRight != null)
                        {
                            cmbModbusConfigRight.SelectedItem = _modbusViewModel.SelectedConfigurationRight;
                        }

                        // Re-attach event handler only once
                        cmbModbusConfigRight.SelectedIndexChanged += CmbModbusConfigRight_SelectedIndexChanged;
                    }

                    // Update right panel configuration name TextBox
                    if (txtModbusConfigNameRight != null)
                    {
                        txtModbusConfigNameRight.Text = _modbusViewModel.SelectedConfigurationRight?.Name ?? "";
                    }

                    if (lblModbusLastErrorRight != null)
                        lblModbusLastErrorRight.Text = _modbusViewModel.LastErrorRight;

                    // Update right panel connection settings (independent from left)
                    if (_modbusViewModel.SelectedConfigurationRight != null)
                    {
                        if (rdoModbusTCPRight != null)
                            rdoModbusTCPRight.Checked = _modbusViewModel.SelectedConfigurationRight.ConnectionType == ModbusService.ModbusConnectionType.TCP;
                        if (rdoModbusRTURight != null)
                            rdoModbusRTURight.Checked = _modbusViewModel.SelectedConfigurationRight.ConnectionType == ModbusService.ModbusConnectionType.RTU;
                        if (txtModbusIPRight != null)
                            txtModbusIPRight.Text = _modbusViewModel.SelectedConfigurationRight.IpAddress;
                        if (nudModbusPortRight != null)
                            nudModbusPortRight.Value = _modbusViewModel.SelectedConfigurationRight.Port;
                        if (nudModbusDeviceIdRight != null)
                            nudModbusDeviceIdRight.Value = _modbusViewModel.SelectedConfigurationRight.DeviceId;

                        if (cmbModbusComPortRight != null)
                            cmbModbusComPortRight.SelectedItem = _modbusViewModel.SelectedConfigurationRight.ComPort;
                        if (cmbModbusBaudRateRight != null)
                            cmbModbusBaudRateRight.SelectedItem = _modbusViewModel.SelectedConfigurationRight.BaudRate;
                        if (cmbModbusParityRight != null)
                            cmbModbusParityRight.SelectedItem = _modbusViewModel.SelectedConfigurationRight.Parity;
                        if (cmbModbusDataBitsRight != null)
                            cmbModbusDataBitsRight.SelectedItem = _modbusViewModel.SelectedConfigurationRight.DataBits;
                        if (cmbModbusStopBitsRight != null)
                            cmbModbusStopBitsRight.SelectedItem = _modbusViewModel.SelectedConfigurationRight.StopBits;
                    }

                    // Update right panel read results
                    if (dgvModbusReadResultsRight != null)
                    {
                        dgvModbusReadResultsRight.DataSource = null;
                        dgvModbusReadResultsRight.DataSource = _modbusViewModel.ReadResultsRight.ToList();
                        if (dgvModbusReadResultsRight.ColumnCount == 0 && _modbusViewModel.ReadResultsRight.Any())
                            dgvModbusReadResultsRight.AutoGenerateColumns = true;
                    }

                    UpdateModbusControlsState(isLeft: false);
                }
            }
            finally
            {
                _isUpdatingModbusUI = false;
            }
        }

        // Dedicated event handlers to prevent recursive calls
        private void CmbModbusConfigLeft_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_isUpdatingModbusUI) return; // Prevent recursion

            Logger.Log($"Left Modbus config selection changed. Selected item: {cmbModbusConfigLeft.SelectedItem?.ToString() ?? "null"}", LogLevel.Debug);
            if (cmbModbusConfigLeft.SelectedItem is ModbusConfiguration selected)
            {
                Logger.Log($"Setting SelectedConfigurationLeft to: {selected.Name}", LogLevel.Debug);
                _modbusViewModel.SelectedConfigurationLeft = selected;
                // Don't call UpdateUIFromModbusViewModel here to prevent recursion
                // The PropertyChanged event will handle UI updates
            }
            else
            {
                Logger.Log("Selected item is not a ModbusConfiguration or is null", LogLevel.Warning);
            }
        }

        private void CmbModbusConfigRight_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_isUpdatingModbusUI) return; // Prevent recursion

            Logger.Log($"Right Modbus config selection changed. Selected item: {cmbModbusConfigRight.SelectedItem?.ToString() ?? "null"}", LogLevel.Debug);
            if (cmbModbusConfigRight.SelectedItem is ModbusConfiguration selected)
            {
                Logger.Log($"Setting SelectedConfigurationRight to: {selected.Name}", LogLevel.Debug);
                _modbusViewModel.SelectedConfigurationRight = selected;
                // Don't call UpdateUIFromModbusViewModel here to prevent recursion
                // The PropertyChanged event will handle UI updates
            }
            else
            {
                Logger.Log("Selected item is not a ModbusConfiguration or is null", LogLevel.Warning);
            }
        }

        private void UpdateModbusControlsState(bool isLeft)
        {
            if (isLeft)
            {
                // Update left panel control states
                bool isTcp = rdoModbusTCPLeft?.Checked ?? true;
                bool hasConfig = _modbusViewModel.SelectedConfigurationLeft != null;

                if (txtModbusIPLeft != null)
                    txtModbusIPLeft.Enabled = isTcp && hasConfig;
                if (nudModbusPortLeft != null)
                    nudModbusPortLeft.Enabled = isTcp && hasConfig;
                if (lblModbusIPLeft != null)
                    lblModbusIPLeft.Enabled = isTcp;
                if (lblModbusPortLeft != null)
                    lblModbusPortLeft.Enabled = isTcp;

                if (cmbModbusComPortLeft != null)
                    cmbModbusComPortLeft.Enabled = !isTcp && hasConfig;
                if (cmbModbusBaudRateLeft != null)
                    cmbModbusBaudRateLeft.Enabled = !isTcp && hasConfig;
                if (cmbModbusParityLeft != null)
                    cmbModbusParityLeft.Enabled = !isTcp && hasConfig;
                if (cmbModbusDataBitsLeft != null)
                    cmbModbusDataBitsLeft.Enabled = !isTcp && hasConfig;
                if (cmbModbusStopBitsLeft != null)
                    cmbModbusStopBitsLeft.Enabled = !isTcp && hasConfig;
                if (lblModbusComPortLeft != null)
                    lblModbusComPortLeft.Enabled = !isTcp;
                if (lblModbusBaudRateLeft != null)
                    lblModbusBaudRateLeft.Enabled = !isTcp;
                if (lblModbusParityLeft != null)
                    lblModbusParityLeft.Enabled = !isTcp;
                if (lblModbusDataBitsLeft != null)
                    lblModbusDataBitsLeft.Enabled = !isTcp;
                if (lblModbusStopBitsLeft != null)
                    lblModbusStopBitsLeft.Enabled = !isTcp;

                // Left panel operations controls
                if (txtModbusRegisterAddressLeft != null)
                    txtModbusRegisterAddressLeft.Enabled = hasConfig;
                if (nudModbusRegisterCountLeft != null)
                    nudModbusRegisterCountLeft.Enabled = hasConfig;
                if (txtModbusWriteValueLeft != null)
                    txtModbusWriteValueLeft.Enabled = hasConfig;
                if (chkModbusWriteCoilValueLeft != null)
                    chkModbusWriteCoilValueLeft.Enabled = hasConfig;
            }
            else // Right panel (Independent instance)
            {
                // Update right panel control states
                bool isTcp = rdoModbusTCPRight?.Checked ?? true;
                bool hasConfig = _modbusViewModel.SelectedConfigurationRight != null;

                if (txtModbusIPRight != null)
                    txtModbusIPRight.Enabled = isTcp && hasConfig;
                if (nudModbusPortRight != null)
                    nudModbusPortRight.Enabled = isTcp && hasConfig;
                if (lblModbusIPRight != null)
                    lblModbusIPRight.Enabled = isTcp;
                if (lblModbusPortRight != null)
                    lblModbusPortRight.Enabled = isTcp;

                if (cmbModbusComPortRight != null)
                    cmbModbusComPortRight.Enabled = !isTcp && hasConfig;
                if (cmbModbusBaudRateRight != null)
                    cmbModbusBaudRateRight.Enabled = !isTcp && hasConfig;
                if (cmbModbusParityRight != null)
                    cmbModbusParityRight.Enabled = !isTcp && hasConfig;
                if (cmbModbusDataBitsRight != null)
                    cmbModbusDataBitsRight.Enabled = !isTcp && hasConfig;
                if (cmbModbusStopBitsRight != null)
                    cmbModbusStopBitsRight.Enabled = !isTcp && hasConfig;
                if (lblModbusComPortRight != null)
                    lblModbusComPortRight.Enabled = !isTcp;
                if (lblModbusBaudRateRight != null)
                    lblModbusBaudRateRight.Enabled = !isTcp;
                if (lblModbusParityRight != null)
                    lblModbusParityRight.Enabled = !isTcp;
                if (lblModbusDataBitsRight != null)
                    lblModbusDataBitsRight.Enabled = !isTcp;
                if (lblModbusStopBitsRight != null)
                    lblModbusStopBitsRight.Enabled = !isTcp;

                // Right panel operations controls
                if (txtModbusRegisterAddressRight != null)
                    txtModbusRegisterAddressRight.Enabled = hasConfig;
                if (nudModbusRegisterCountRight != null)
                    nudModbusRegisterCountRight.Enabled = hasConfig;
                if (txtModbusWriteValueRight != null)
                    txtModbusWriteValueRight.Enabled = hasConfig;
                if (chkModbusWriteCoilValueRight != null)
                    chkModbusWriteCoilValueRight.Enabled = hasConfig;
            }
        }

        private void UpdateModbusViewModelFromUI(bool isLeft)
        {
            if (isLeft && _modbusViewModel.SelectedConfigurationLeft != null)
            {
                // Update from left panel
                if (txtModbusConfigNameLeft != null && !string.IsNullOrWhiteSpace(txtModbusConfigNameLeft.Text))
                    _modbusViewModel.SelectedConfigurationLeft.Name = txtModbusConfigNameLeft.Text.Trim();

                if (rdoModbusTCPLeft != null && rdoModbusRTULeft != null)
                    _modbusViewModel.SelectedConfigurationLeft.ConnectionType = rdoModbusTCPLeft.Checked ? ModbusService.ModbusConnectionType.TCP : ModbusService.ModbusConnectionType.RTU;

                if (txtModbusIPLeft != null)
                    _modbusViewModel.SelectedConfigurationLeft.IpAddress = txtModbusIPLeft.Text;
                if (nudModbusPortLeft != null)
                    _modbusViewModel.SelectedConfigurationLeft.Port = (int)nudModbusPortLeft.Value;
                if (nudModbusDeviceIdLeft != null)
                    _modbusViewModel.SelectedConfigurationLeft.DeviceId = (byte)nudModbusDeviceIdLeft.Value;

                if (_modbusViewModel.SelectedConfigurationLeft.ConnectionType == ModbusService.ModbusConnectionType.RTU)
                {
                    if (cmbModbusComPortLeft != null)
                        _modbusViewModel.SelectedConfigurationLeft.ComPort = cmbModbusComPortLeft.SelectedItem?.ToString();
                    if (cmbModbusBaudRateLeft != null)
                        _modbusViewModel.SelectedConfigurationLeft.BaudRate = Convert.ToInt32(cmbModbusBaudRateLeft.SelectedItem ?? 9600);
                    if (cmbModbusParityLeft != null)
                        _modbusViewModel.SelectedConfigurationLeft.Parity = (System.IO.Ports.Parity)(cmbModbusParityLeft.SelectedItem ?? System.IO.Ports.Parity.None);
                    if (cmbModbusDataBitsLeft != null)
                        _modbusViewModel.SelectedConfigurationLeft.DataBits = Convert.ToInt32(cmbModbusDataBitsLeft.SelectedItem ?? 8);
                    if (cmbModbusStopBitsLeft != null)
                        _modbusViewModel.SelectedConfigurationLeft.StopBits = (System.IO.Ports.StopBits)(cmbModbusStopBitsLeft.SelectedItem ?? System.IO.Ports.StopBits.One);
                }
            }
            else if (!isLeft && _modbusViewModel.SelectedConfigurationRight != null)
            {
                // Update from right panel (independent instance)
                if (txtModbusConfigNameRight != null && !string.IsNullOrWhiteSpace(txtModbusConfigNameRight.Text))
                    _modbusViewModel.SelectedConfigurationRight.Name = txtModbusConfigNameRight.Text.Trim();

                if (rdoModbusTCPRight != null && rdoModbusRTURight != null)
                    _modbusViewModel.SelectedConfigurationRight.ConnectionType = rdoModbusTCPRight.Checked ? ModbusService.ModbusConnectionType.TCP : ModbusService.ModbusConnectionType.RTU;

                if (txtModbusIPRight != null)
                    _modbusViewModel.SelectedConfigurationRight.IpAddress = txtModbusIPRight.Text;
                if (nudModbusPortRight != null)
                    _modbusViewModel.SelectedConfigurationRight.Port = (int)nudModbusPortRight.Value;
                if (nudModbusDeviceIdRight != null)
                    _modbusViewModel.SelectedConfigurationRight.DeviceId = (byte)nudModbusDeviceIdRight.Value;

                if (_modbusViewModel.SelectedConfigurationRight.ConnectionType == ModbusService.ModbusConnectionType.RTU)
                {
                    if (cmbModbusComPortRight != null)
                        _modbusViewModel.SelectedConfigurationRight.ComPort = cmbModbusComPortRight.SelectedItem?.ToString();
                    if (cmbModbusBaudRateRight != null)
                        _modbusViewModel.SelectedConfigurationRight.BaudRate = Convert.ToInt32(cmbModbusBaudRateRight.SelectedItem ?? 9600);
                    if (cmbModbusParityRight != null)
                        _modbusViewModel.SelectedConfigurationRight.Parity = (System.IO.Ports.Parity)(cmbModbusParityRight.SelectedItem ?? System.IO.Ports.Parity.None);
                    if (cmbModbusDataBitsRight != null)
                        _modbusViewModel.SelectedConfigurationRight.DataBits = Convert.ToInt32(cmbModbusDataBitsRight.SelectedItem ?? 8);
                    if (cmbModbusStopBitsRight != null)
                        _modbusViewModel.SelectedConfigurationRight.StopBits = (System.IO.Ports.StopBits)(cmbModbusStopBitsRight.SelectedItem ?? System.IO.Ports.StopBits.One);
                }
            }
        }

        private void UpdateModbusOperationParametersFromUI(bool isLeft)
        {
            if (isLeft) // Left panel operations
            {
                if (txtModbusRegisterAddressLeft != null && !string.IsNullOrWhiteSpace(txtModbusRegisterAddressLeft.Text))
                {
                    if (ModbusService.ParseAddress(txtModbusRegisterAddressLeft.Text, out ushort address, out bool isFloatFormat))
                    {
                        _modbusViewModel.RegisterAddressLeft = address;
                        _modbusViewModel.IsFloatFormatLeft = isFloatFormat;
                        _modbusViewModel.OriginalAddressStringLeft = txtModbusRegisterAddressLeft.Text;
                    }
                }
                if (nudModbusRegisterCountLeft != null)
                    _modbusViewModel.RegisterCountLeft = (ushort)nudModbusRegisterCountLeft.Value;
                if (txtModbusWriteValueLeft != null)
                {
                    if (ushort.TryParse(txtModbusWriteValueLeft.Text, out ushort regValLeft))
                        _modbusViewModel.RegisterValueLeft = regValLeft;
                    else
                        _modbusViewModel.RegisterValueLeft = 0; // Default or error
                }
                if (chkModbusWriteCoilValueLeft != null)
                    _modbusViewModel.CoilValueLeft = chkModbusWriteCoilValueLeft.Checked;
            }
            else // Right panel operations
            {
                if (txtModbusRegisterAddressRight != null && !string.IsNullOrWhiteSpace(txtModbusRegisterAddressRight.Text))
                {
                    if (ModbusService.ParseAddress(txtModbusRegisterAddressRight.Text, out ushort address, out bool isFloatFormat))
                    {
                        _modbusViewModel.RegisterAddressRight = address;
                        _modbusViewModel.IsFloatFormatRight = isFloatFormat;
                        _modbusViewModel.OriginalAddressStringRight = txtModbusRegisterAddressRight.Text;
                    }
                }
                if (nudModbusRegisterCountRight != null)
                    _modbusViewModel.RegisterCountRight = (ushort)nudModbusRegisterCountRight.Value;
                if (txtModbusWriteValueRight != null)
                {
                    if (ushort.TryParse(txtModbusWriteValueRight.Text, out ushort regValRight))
                        _modbusViewModel.RegisterValueRight = regValRight;
                    else
                        _modbusViewModel.RegisterValueRight = 0; // Default or error
                }
                if (chkModbusWriteCoilValueRight != null)
                    _modbusViewModel.CoilValueRight = chkModbusWriteCoilValueRight.Checked;
            }
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            //Inicializar tabla de Sinóptico
            InitializeSinopticoTabSimplified();

            // Mostrar el panel de conexión por defecto
            btnNavConnection.PerformClick();

            ApplyLoadedPublishConfigurations();

            // Auto-load last used profile after all UI components are initialized
            // This includes automatic Modbus configuration restoration and connection attempts
            // Use a small delay to ensure all UI components are fully initialized
            WinFormsTimer autoLoadTimer = new WinFormsTimer();
            autoLoadTimer.Interval = 1000; // Increased to 1000ms delay to ensure full initialization
            autoLoadTimer.Tick += async (s, args) =>
            {
                autoLoadTimer.Stop();
                autoLoadTimer.Dispose();

                try
                {
                    // Get the last loaded profile index, fallback to Profile 1 (index 1) if none saved
                    int? lastProfileIndex = profileManager.GetLastLoadedProfile();
                    int profileToLoad = lastProfileIndex ?? 1; // Default to Profile 1 (index 1) if no last profile saved

                    Logger.Log($"Auto-loading profile {profileToLoad} on application start (delayed)...", LogLevel.Info);
                    if (lastProfileIndex.HasValue)
                    {
                        Logger.Log($"Using last loaded profile index: {lastProfileIndex.Value}", LogLevel.Info);
                    }
                    else
                    {
                        Logger.Log("No last loaded profile found, defaulting to Profile 1 (index 1)", LogLevel.Info);
                    }

                    // Verify ModbusViewModel is properly initialized
                    if (_modbusViewModel == null)
                    {
                        Logger.Log("Auto-load failed: ModbusViewModel is null", LogLevel.Error);
                        return;
                    }

                    Logger.Log($"Auto-load: ModbusViewModel available configurations count: {_modbusViewModel.AvailableConfigurations?.Count ?? 0}", LogLevel.Info);
                    if (_modbusViewModel.AvailableConfigurations?.Any() == true)
                    {
                        Logger.Log($"Auto-load: Available configurations: {string.Join(", ", _modbusViewModel.AvailableConfigurations.Select(c => c.Name))}", LogLevel.Info);
                    }

                    // Additional delay to ensure UI is ready
                    await Task.Delay(100); // Replace Application.DoEvents() with proper async delay

                    // Load profile which will automatically restore and connect Modbus configurations
                    // Note: EnsureModbusConfigurationsSynchronized is now called within LoadProfile with proper sequencing
                    if (profileUIManager != null)
                    {
                        await profileUIManager.LoadProfile(profileToLoad);
                    }

                    Logger.Log($"Profile {profileToLoad} auto-load completed with Modbus configuration restoration", LogLevel.Info);
                }
                catch (Exception ex)
                {
                    Logger.Log($"Error auto-loading profile: {ex.Message}", LogLevel.Warning);
                    Logger.Log($"Auto-load exception stack trace: {ex.StackTrace}", LogLevel.Debug);
                    // Don't show message box to user, just log the error
                }
            };
            autoLoadTimer.Start();
        }

        private async void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            // Form closing event handler
            _modbusViewModel?.DisconnectCommandLeft?.Execute(null);
            _modbusViewModel?.DisconnectCommandRight?.Execute(null);

            // Disconnect MQTT service cleanly
            if (mqttService != null)
            {
                try
                {
                    Logger.Log("Disconnecting MQTT service on application close", LogLevel.Info);
                    await mqttService.DisconnectAsync();
                    mqttService.SaveTopicsOnExit();
                    Logger.Log("MQTT service disconnected and topics saved", LogLevel.Info);
                }
                catch (Exception ex)
                {
                    Logger.Log($"Error during MQTT disconnection on close: {ex.Message}", LogLevel.Warning);
                }
            }

            SavePublishConfigurations();

            // Dispose TopicReadingManager
            if (_topicReadingManager != null)
            {
                _topicReadingManager.Dispose();
                Logger.Log("TopicReadingManager disposed", LogLevel.Info);
            }

            // Dispose TopicWritingManager
            if (_topicWritingManager != null)
            {
                _topicWritingManager.Dispose();
                Logger.Log("TopicWritingManager disposed", LogLevel.Info);
            }
        }



        private void BtnNavConnection_Click(object sender, EventArgs e)
        {
            _navigationManager.SwitchToPanel("Connection");
        }

        private void BtnNavPublish_Click(object sender, EventArgs e) // Renamed from BtnNavConfig_Click in source, targetting "Config" panel
        {
            _navigationManager.SwitchToPanel("Config");

            // Auto-refresh "Todos los tópicos" table when switching to MQTT Configuration
            try
            {
                Logger.Log("Refreshing 'Todos los tópicos' table on MQTT Configuration panel switch...", LogLevel.Info);
                TxtFiltroTopicos_TextChanged(null, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                Logger.Log($"Error refreshing topics table: {ex.Message}", LogLevel.Warning);
            }
        }

        private void BtnNavSubscribe_Click(object sender, EventArgs e) // Renamed from BtnNavSinoptico_Click in source, targetting "Sinoptico" panel
        {
            _navigationManager.SwitchToPanel("Sinoptico");
        }

        private void BtnNavMail_Click(object sender, EventArgs e)
        {
            _navigationManager.SwitchToPanel("Mail");
        }

        private void BtnNavModbus_Click(object sender, EventArgs e)
        {
            _navigationManager.SwitchToPanel("Modbus");
        }

        private T ParseEnum<T>(string value, T defaultValue) where T : struct
        {
            if (string.IsNullOrEmpty(value))
            {
                Logger.Log($"Form1: Cannot parse enum {typeof(T).Name} from null or empty value. Returning default '{defaultValue}'.", LogLevel.Debug);
                return defaultValue;
            }
            if (Enum.TryParse<T>(value, true, out T result)) // true for ignore case
            {
                return result;
            }
            Logger.Log($"Form1: Failed to parse enum {typeof(T).Name} from value '{value}'. Returning default '{defaultValue}'.", LogLevel.Warning);
            return defaultValue;
        }



        // Old subscription management methods removed - using modern dgvSinopticoData table


        private void BtnProbar_Click(object sender, EventArgs e)
        {
            ApplyMailUIToProfile();

            btnProbar.Enabled = false;
            panelEstado.BackColor = Color.Blue;
            Application.DoEvents();

            Probar_Correo();

        }

        public async void Probar_Correo()
        {
            btnProbar.Enabled = false;
            panelEstado.BackColor = Color.Blue; // Indicate testing in progress
            Application.DoEvents(); // Update UI immediately

            try
            {
                // Apply current UI values to profile before testing
                ApplyMailUIToProfile();

                // Initialize email manager based on current configuration
                InitializeEmailManager();

                string connectionType = currentActiveProfile.UseEws ? "EWS" : "POP3";
                Logger.Log($"Iniciando prueba de conexión {connectionType}...", LogLevel.Info);

                // Use the async version for better UI responsiveness
                bool exito = await gestorDeCorreo.ProbarConexionAsync().ConfigureAwait(false);

                // Update UI on the UI thread
                this.Invoke(new Action(() =>
                {
                    if (exito)
                    {
                        panelEstado.BackColor = Color.Green;
                        btnProcesarEmails.Enabled = true;
                        Logger.Log($"Conexión de correo exitosa usando {connectionType}", LogLevel.Info);
                    }
                    else
                    {
                        panelEstado.BackColor = Color.Red;
                        btnProcesarEmails.Enabled = false;
                        Logger.Log($"Error en conexión de correo usando {connectionType}", LogLevel.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                string connectionType = currentActiveProfile.UseEws ? "EWS" : "POP3";
                Logger.Log($"Error al probar conexión de correo usando {connectionType}: {ex.Message}", LogLevel.Error);

                this.Invoke(new Action(() =>
                {
                    panelEstado.BackColor = Color.Red;
                    btnProcesarEmails.Enabled = false;
                }));
            }
            finally
            {
                this.Invoke(new Action(() =>
                {
                    btnProbar.Enabled = true;
                }));
            }
        }

        private async void BtnProcesarEmails_Click(object sender, EventArgs e)
        {
            ApplyMailUIToProfile();

            btnProcesarEmails.Enabled = false;
            panelEstado.BackColor = Color.Blue;
            Application.DoEvents();

            // Ejecutar el procesamiento de correos de forma asíncrona
            await Task.Run(() => Procesar_Correo());

            btnProcesarEmails.Enabled = true;
        }

        public void Procesar_Correo()
        {
            btnProcesarEmails.Enabled = false;
            try
            {
                gestorDeCorreo.ConectarYProcesarCorreos();
                var states = gestorDeCorreo.GetCurrentStates();
                UpdateStates(states);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error procesando correos: {ex.Message}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Logger.Log($"Error procesando correos: {ex.Message}", LogLevel.Error);
            }
            finally
            {
                btnProcesarEmails.Enabled = true;
            }
        }

        /// <summary>
        /// EWS-specific email processing that handles its own status panel updates
        /// </summary>
        public void Procesar_Correo_EWS()
        {
            btnEwsProcesarEmails.Enabled = false;
            try
            {
                gestorDeCorreo.ConectarYProcesarCorreosEWS();
                var states = gestorDeCorreo.GetCurrentStates();
                UpdateStates(states);

                // Update EWS status panel on success
                UpdatePanelEwsEstadoColor(Color.Green);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error procesando correos EWS: {ex.Message}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Logger.Log($"Error procesando correos EWS: {ex.Message}", LogLevel.Error);

                // Update EWS status panel on error
                UpdatePanelEwsEstadoColor(Color.Red);
            }
            finally
            {
                btnEwsProcesarEmails.Enabled = true;
            }
        }

        public void ApplyMailUIToProfile()
        {
            if (currentActiveProfile == null) return;

            // Email mode selection
            currentActiveProfile.UseEws = chkUseEws.Checked;

            // POP3 settings (only update if not using EWS)
            if (!currentActiveProfile.UseEws)
            {
                currentActiveProfile.Pop3Server = txtServidor.Text.Trim();
                if (int.TryParse(txtPuerto.Text.Trim(), out int port))
                {
                    currentActiveProfile.Pop3Port = port;
                }
                else
                {
                    MessageBox.Show("El puerto POP3 debe ser un número válido.",
                        "Error de Entrada", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Logger.Log("El puerto POP3 debe ser un número válido.", LogLevel.Warning);
                    txtPuerto.Focus();
                    return;
                }
                currentActiveProfile.Pop3Username = txtUsuario.Text;
                currentActiveProfile.Pop3Password = txtContraseña.Text;
                currentActiveProfile.Pop3UseSsl = chkPop3Ssl.Checked;
            }



            // EWS settings (always update to preserve configuration)
            currentActiveProfile.EwsServerUrl = txtEwsServerUrl.Text.Trim();
            currentActiveProfile.EwsUsername = txtEwsUsername.Text.Trim();
            currentActiveProfile.EwsPassword = txtEwsPassword.Text;
            currentActiveProfile.EwsDomain = txtEwsDomain.Text.Trim();
            currentActiveProfile.EwsUseOAuth = chkEwsUseOAuth.Checked;

            // Email notification addresses
            currentActiveProfile.EmailNotification1 = txtEmailNotif1.Text.Trim();
            currentActiveProfile.EmailNotification2 = txtEmailNotif2.Text.Trim();
            currentActiveProfile.EmailNotification3 = txtEmailNotif3.Text.Trim();
            currentActiveProfile.EmailNotification4 = txtEmailNotif4.Text.Trim();
            currentActiveProfile.EmailNotification5 = txtEmailNotif5.Text.Trim();

            // Update email checking settings
            if (nudCheckInterval != null)
            {
                currentActiveProfile.EmailCheckIntervalMinutes = (int)nudCheckInterval.Value;
            }
            currentActiveProfile.AutoStartEmailChecking = checkTimer?.Enabled ?? false;
        }

        private void ListActiveSubscriptions_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnUnsubscribe.Enabled = listActiveSubscriptions.SelectedItem != null;
        }

        // Old subscription management methods removed - using modern dgvSinopticoData table

        private void InitializeEmailChecking()
        {
            if (panelMailContent == null)
            {
                Logger.Log("panelMailContent no está inicializado", LogLevel.Error);
                return;
            }

            checkTimer = new WinFormsTimer();
            checkTimer.Tick += CheckTimer_Tick;

            // Static positioning for email checking layout
            int rightMargin = 1200; // Fixed position for right side elements - moved further right

            // Configurar labels con posicionamiento fijo
            lblHoy = new Label
            {
                Text = "Hoy:",
                AutoSize = true,
                Location = new Point(rightMargin, 40),
                Font = new Font("Segoe UI", 9.75F)
            };

            lblMañana = new Label
            {
                Text = "Mañana:",
                AutoSize = true,
                Location = new Point(rightMargin, 90),
                Font = new Font("Segoe UI", 9.75F)
            };



            // Mejorar paneles de flujo con más espacio
            panelHoyIconos = new FlowLayoutPanel
            {
                Location = new Point(rightMargin + 100, 35),
                Size = new Size(600, 40), // Ampliado para mejor visualización
                FlowDirection = FlowDirection.LeftToRight,
                WrapContents = false,
                AutoScroll = true
            };

            panelMañanaIconos = new FlowLayoutPanel
            {
                Location = new Point(rightMargin + 100, 85),
                Size = new Size(600, 40), // Ampliado para mejor visualización
                FlowDirection = FlowDirection.LeftToRight,
                WrapContents = false,
                AutoScroll = true
            };

            // Crear los 24 iconos para cada día
            for (int i = 0; i < 24; i++)
            {
                var iconoHoy = CrearIconoHora(i);
                var iconoMañana = CrearIconoHora(i);

                iconosHoy.Add(iconoHoy);
                iconosMañana.Add(iconoMañana);

                panelHoyIconos.Controls.Add(iconoHoy);
                panelMañanaIconos.Controls.Add(iconoMañana);
            }

            // Reposicionar los controles de estado e intervalo
            // Label para el estado actual con mejor posición
            Label lblEstadoActual = new Label
            {
                Text = "Estado hora actual:",
                AutoSize = true,
                Location = new Point(rightMargin, 140),
                Font = new Font("Segoe UI", 9.75F)
            };

            // Panel indicador de estado actual mejorado
            panelEstadoHora = new Panel();
            panelEstadoHora.Size = new Size(25, 25); // Tamaño aumentado
            panelEstadoHora.BackColor = Color.Gray;
            panelEstadoHora.Location = new Point(rightMargin + 160, 140);

            // Añadir label para el intervalo con mejor posición
            Label lblIntervalo = new Label
            {
                Text = "Intervalo de comprobación (minutos):",
                AutoSize = true,
                Location = new Point(rightMargin, 180),
                Font = new Font("Segoe UI", 9.75F)
            };

            // Configurar controles de intervalo con mejor posicionamiento
            nudCheckInterval = new NumericUpDown();
            nudCheckInterval.Minimum = 1;
            nudCheckInterval.Maximum = 60;
            nudCheckInterval.Value = 5;
            nudCheckInterval.Size = new Size(70, 25); // Tamaño aumentado
            nudCheckInterval.Location = new Point(rightMargin + 300, 178);

            nudCheckInterval.ValueChanged += (s, e) =>
            {
                // Actualizar el intervalo del timer
                checkTimer.Interval = (int)nudCheckInterval.Value * 60 * 1000;
                Logger.Log($"Intervalo de verificación de correo actualizado a {nudCheckInterval.Value} minutos", LogLevel.Info);
            };

            // Configurar el intervalo inicial y iniciar el timer
            checkTimer.Interval = (int)nudCheckInterval.Value * 60 * 1000; // 5 minutos por defecto

            // Añadir label para mostrar el estado del timer
            lblTimerStatus = new Label
            {
                Text = "Estado: Detenido",
                AutoSize = true,
                Location = new Point(rightMargin, 340),
                Font = new Font("Segoe UI", 9.75F),
                ForeColor = Color.White
            };

            // Añadir botones para controlar el timer
            btnStartTimer = new Button
            {
                Text = "Iniciar Verificación Automática",
                Size = new Size(200, 30),
                Location = new Point(rightMargin, 300),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnStartTimer.Click += (s, e) =>
            {
                if (!checkTimer.Enabled)
                {
                    checkTimer.Start();
                    btnStartTimer.Text = "Detener Verificación Automática";
                    btnStartTimer.BackColor = Color.FromArgb(209, 17, 65);
                    lblTimerStatus.Text = $"Estado: Activo (cada {nudCheckInterval.Value} min)";
                    lblTimerStatus.ForeColor = Color.LightGreen;
                    Logger.Log("Verificación automática de correo iniciada", LogLevel.Info);
                }
                else
                {
                    checkTimer.Stop();
                    btnStartTimer.Text = "Iniciar Verificación Automática";
                    btnStartTimer.BackColor = Color.FromArgb(0, 122, 204);
                    lblTimerStatus.Text = "Estado: Detenido";
                    lblTimerStatus.ForeColor = Color.White;
                    Logger.Log("Verificación automática de correo detenida", LogLevel.Info);
                }
            };

            // Nuevo TextBox para mostrar la hora actual
            TextBox txtHoraActual = new TextBox
            {
                Location = new Point(rightMargin + 130, 220),
                Size = new Size(150, 25),
                ReadOnly = true,
                Font = new Font("Segoe UI", 9.75F),
                Text = DateTime.Now.ToString("HH:mm:ss")
            };

            // Label para el TextBox de hora actual
            Label lblHoraActual = new Label
            {
                Text = "Hora actual:",
                AutoSize = true,
                Location = new Point(rightMargin, 223),
                Font = new Font("Segoe UI", 9.75F)
            };

            // Actualizar la hora cada segundo
            WinFormsTimer timerHoraActual = new WinFormsTimer();
            timerHoraActual.Interval = 1000; // 1 segundo
            timerHoraActual.Tick += (s, e) =>
            {
                txtHoraActual.Text = DateTime.Now.ToString("HH:mm:ss");
            };
            timerHoraActual.Start();

            // Añadir controles al panel de correo
            panelMailContent.Controls.Add(lblHoy);
            panelMailContent.Controls.Add(lblMañana);
            panelMailContent.Controls.Add(panelHoyIconos);
            panelMailContent.Controls.Add(panelMañanaIconos);
            panelMailContent.Controls.Add(lblEstadoActual);
            panelMailContent.Controls.Add(panelEstadoHora);
            panelMailContent.Controls.Add(lblIntervalo);
            panelMailContent.Controls.Add(nudCheckInterval);
            panelMailContent.Controls.Add(lblHoraActual);
            panelMailContent.Controls.Add(txtHoraActual);

            panelMailContent.Controls.Add(btnStartTimer); // Añadir el botón de control del timer
            panelMailContent.Controls.Add(lblTimerStatus); // Añadir el label de estado del timer

            // Static positioning - no anchoring needed
        }

        public void InitializeEmailManager()
        {
            if (currentActiveProfile.UseEws)
            {
                // Initialize EWS email manager
                gestorDeCorreo = new GestorDeCorreo(
                    this,
                    currentActiveProfile.EwsServerUrl,
                    currentActiveProfile.EwsUsername,
                    currentActiveProfile.EwsPassword,
                    currentActiveProfile.EwsDomain,
                    currentActiveProfile.EwsUseOAuth
                );
            }
            else
            {
                // Initialize POP3 email manager
                gestorDeCorreo = new GestorDeCorreo(
                    this,
                    currentActiveProfile.Pop3Server,
                    currentActiveProfile.Pop3Port,
                    currentActiveProfile.Pop3Username,
                    currentActiveProfile.Pop3Password
                );
            }
        }





        private void ChkUseEws_CheckedChanged(object sender, EventArgs e)
        {
            UpdateEmailControlsState();

            // Actualizar el perfil actual
            if (currentActiveProfile != null)
            {
                currentActiveProfile.UseEws = chkUseEws.Checked;
            }

            // Start or stop EWS periodic verification based on checkbox state
            if (chkUseEws.Checked)
            {
                StartEwsPeriodicVerification();
                Logger.Log("EWS periodic verification started due to checkbox change", LogLevel.Info);
            }
            else
            {
                StopEwsPeriodicVerification();
                Logger.Log("EWS periodic verification stopped due to checkbox change", LogLevel.Info);
            }
        }

        private async void BtnEwsProbar_Click(object sender, EventArgs e)
        {
            await Probar_Correo_EWS();
        }

        public async Task Probar_Correo_EWS()
        {
            btnEwsProbar.Enabled = false;
            panelEwsEstado.BackColor = Color.Blue; // Indicate testing in progress
            Application.DoEvents(); // Update UI immediately

            try
            {
                // Apply current EWS UI values to profile before testing
                ApplyEwsUIToProfile();

                // Initialize email manager for EWS
                InitializeEmailManager();

                Logger.Log("Iniciando prueba de conexión EWS...", LogLevel.Info);

                // Use the async version for better UI responsiveness
                bool exito = await gestorDeCorreo.ProbarConexionAsync().ConfigureAwait(false);

                // Update UI on the UI thread
                this.Invoke(new Action(() =>
                {
                    if (exito)
                    {
                        panelEwsEstado.BackColor = Color.Green;
                        btnEwsProcesarEmails.Enabled = true;
                        Logger.Log("Conexión de correo EWS exitosa", LogLevel.Info);
                    }
                    else
                    {
                        panelEwsEstado.BackColor = Color.Red;
                        btnEwsProcesarEmails.Enabled = false;
                        Logger.Log("Error en conexión de correo EWS", LogLevel.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                Logger.Log($"Error durante la prueba de conexión EWS: {ex.Message}", LogLevel.Error);
                this.Invoke(new Action(() =>
                {
                    panelEwsEstado.BackColor = Color.Red;
                    btnEwsProcesarEmails.Enabled = false;
                }));
            }
            finally
            {
                this.Invoke(new Action(() =>
                {
                    btnEwsProbar.Enabled = true;
                }));
            }
        }

        /// <summary>
        /// Calls the appropriate connection test function based on profile configuration
        /// </summary>
        public async Task Probar_Correo_Automatico()
        {
            if (currentActiveProfile?.UseEws == true)
            {
                Logger.Log("Auto-testing EWS connection...", LogLevel.Info);
                await Probar_Correo_EWS();
            }
            else
            {
                Logger.Log("Auto-testing POP3 connection...", LogLevel.Info);
                Probar_Correo();
            }
        }

        private async void BtnEwsProcesarEmails_Click(object sender, EventArgs e)
        {
            // Verificar que la conexión EWS esté establecida antes de procesar
            if (panelEwsEstado.BackColor != Color.Green)
            {
                MessageBox.Show("Debe probar y establecer una conexión EWS exitosa antes de procesar correos.",
                    "Conexión requerida", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                Logger.Log("Intento de procesar correos EWS sin conexión establecida.", LogLevel.Warning);
                return;
            }

            ApplyEwsUIToProfile();

            btnEwsProcesarEmails.Enabled = false;
            panelEwsEstado.BackColor = Color.Blue;
            Application.DoEvents();

            try
            {
                // Ejecutar el procesamiento de correos EWS de forma asíncrona con timeout
                using (var cts = new CancellationTokenSource(TimeSpan.FromMinutes(10))) // 10 minute timeout
                {
                    await Task.Run(() => Procesar_Correo_EWS(), cts.Token);
                }

                Logger.Log("Procesamiento de correos EWS completado exitosamente.", LogLevel.Info);
            }
            catch (OperationCanceledException)
            {
                Logger.Log("Procesamiento de correos EWS cancelado por timeout.", LogLevel.Error);
                MessageBox.Show("El procesamiento de correos fue cancelado por exceder el tiempo límite.",
                    "Timeout", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                panelEwsEstado.BackColor = Color.Red;
            }
            catch (Exception ex)
            {
                Logger.Log($"Error durante el procesamiento de correos EWS: {ex.Message}", LogLevel.Error);
                MessageBox.Show($"Error procesando correos EWS: {ex.Message}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                panelEwsEstado.BackColor = Color.Red;
            }
            finally
            {
                btnEwsProcesarEmails.Enabled = true;
            }
        }

        private void ApplyEwsUIToProfile()
        {
            if (currentActiveProfile == null) return;

            // EWS settings
            currentActiveProfile.EwsServerUrl = txtEwsServerUrl.Text.Trim();
            currentActiveProfile.EwsUsername = txtEwsUsername.Text.Trim();
            currentActiveProfile.EwsPassword = txtEwsPassword.Text;
            currentActiveProfile.EwsDomain = txtEwsDomain.Text.Trim();
            currentActiveProfile.EwsUseOAuth = chkEwsUseOAuth.Checked;

            // Email notification addresses
            currentActiveProfile.EmailNotification1 = txtEmailNotif1.Text.Trim();
            currentActiveProfile.EmailNotification2 = txtEmailNotif2.Text.Trim();
            currentActiveProfile.EmailNotification3 = txtEmailNotif3.Text.Trim();
            currentActiveProfile.EmailNotification4 = txtEmailNotif4.Text.Trim();
            currentActiveProfile.EmailNotification5 = txtEmailNotif5.Text.Trim();
        }

        public void UpdateEmailControlsState()
        {
            bool isEws = chkUseEws.Checked;
            bool isPop3 = !isEws;

            // POP3 controls
            txtServidor.Enabled = isPop3;
            txtPuerto.Enabled = isPop3;
            txtUsuario.Enabled = isPop3;
            txtContraseña.Enabled = isPop3;
            chkPop3Ssl.Enabled = isPop3;
            btnProbar.Enabled = isPop3;
            btnProcesarEmails.Enabled = isPop3;

            // EWS controls
            txtEwsServerUrl.Enabled = isEws;
            txtEwsUsername.Enabled = isEws;
            txtEwsPassword.Enabled = isEws;
            txtEwsDomain.Enabled = isEws;
            chkEwsUseOAuth.Enabled = false; // OAuth not implemented yet
            btnEwsProbar.Enabled = isEws;
            btnEwsProcesarEmails.Enabled = isEws;

            // Email notification controls
            txtEmailNotif1.Enabled = isEws;
            txtEmailNotif2.Enabled = isEws;
            txtEmailNotif3.Enabled = isEws;
            txtEmailNotif4.Enabled = isEws;
            txtEmailNotif5.Enabled = isEws;

            // Visual feedback for POP3
            if (isPop3)
            {
                lblServerPop3.ForeColor = Color.White;
                lblPuertoPop3.ForeColor = Color.White;
                lblUsuarioPop3.ForeColor = Color.White;
                lblContraseñaPop3.ForeColor = Color.White;
            }
            else
            {
                lblServerPop3.ForeColor = Color.Gray;
                lblPuertoPop3.ForeColor = Color.Gray;
                lblUsuarioPop3.ForeColor = Color.Gray;
                lblContraseñaPop3.ForeColor = Color.Gray;
            }

            // Visual feedback for EWS
            if (isEws)
            {
                lblEwsServerUrl.ForeColor = Color.White;
                lblEwsUsername.ForeColor = Color.White;
                lblEwsPassword.ForeColor = Color.White;
                lblEwsDomain.ForeColor = Color.White;
            }
            else
            {
                lblEwsServerUrl.ForeColor = Color.Gray;
                lblEwsUsername.ForeColor = Color.Gray;
                lblEwsPassword.ForeColor = Color.Gray;
                lblEwsDomain.ForeColor = Color.Gray;
            }
        }

        private async void CheckTimer_Tick(object sender, EventArgs e)
        {
            await CheckAndProcessEmails();
            UpdateHourlyState();
        }

        private async Task CheckAndProcessEmails()
        {
            try
            {
                // Ejecutar el procesamiento de correos en un hilo de fondo para evitar bloquear la UI
                await Task.Run(() =>
                {
                    gestorDeCorreo.ConectarYProcesarCorreos();
                });

                // Actualizar estados después de procesar (en el hilo principal)
                var states = gestorDeCorreo.GetCurrentStates();
                UpdateStates(states);

                // Update email status indicator on successful processing
                UpdateEmailStatus(true, "Correo: Conectado");

                Logger.Log("Verificación automática de correo completada exitosamente", LogLevel.Info);
            }
            catch (Exception ex)
            {
                // Mejorar el manejo de errores - no mostrar MessageBox en verificación automática
                Logger.Log($"Error en la verificación automática de correo: {ex.Message}", LogLevel.Error);

                // Update email status indicator on error
                UpdateEmailStatus(false, "Correo: Error", true);

                // Actualizar el indicador visual de error apropiado según el protocolo de correo
                if (currentActiveProfile?.UseEws == true)
                {
                    if (panelEwsEstado != null)
                    {
                        if (InvokeRequired)
                        {
                            Invoke(new Action(() => panelEwsEstado.BackColor = Color.Red));
                        }
                        else
                        {
                            panelEwsEstado.BackColor = Color.Red;
                        }
                    }
                }
                else
                {
                    if (panelEstado != null)
                    {
                        if (InvokeRequired)
                        {
                            Invoke(new Action(() => panelEstado.BackColor = Color.Red));
                        }
                        else
                        {
                            panelEstado.BackColor = Color.Red;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Updates hourly state with robust day change detection that works regardless of application runtime
        /// </summary>
        private void UpdateHourlyState()
        {
            var now = DateTime.Now;
            var currentDate = now.Date;
            int currentHour = now.Hour;

            // Check for day changes by comparing dates instead of exact time
            if (lastUpdateDate != DateTime.MinValue && currentDate > lastUpdateDate)
            {
                // Calculate how many days have passed
                int daysPassed = (int)(currentDate - lastUpdateDate).TotalDays;
                Logger.Log($"Day change detected: {daysPassed} day(s) passed since last update (from {lastUpdateDate:yyyy-MM-dd} to {currentDate:yyyy-MM-dd})", LogLevel.Info);

                // Handle multiple missed days by rotating states for each day
                for (int i = 0; i < daysPassed; i++)
                {
                    DateTime rotationDate = lastUpdateDate.AddDays(i + 1);
                    Logger.Log($"Rotating states for day {i + 1}/{daysPassed}: {rotationDate:yyyy-MM-dd}", LogLevel.Info);

                    // Rotate states: today becomes yesterday (discarded), tomorrow becomes today, create new tomorrow
                    todayStates = tomorrowStates ?? new HourlyState { Date = rotationDate };
                    tomorrowStates = new HourlyState { Date = rotationDate.AddDays(1) };

                    // Ensure dates are properly set
                    if (todayStates != null)
                        todayStates.Date = rotationDate;
                }

                // Update the last update date and persist it
                lastUpdateDate = currentDate;
                SaveLastUpdateDate();
                Logger.Log($"Day rotation completed. New lastUpdateDate: {lastUpdateDate:yyyy-MM-dd}", LogLevel.Info);
            }
            else if (lastUpdateDate == DateTime.MinValue)
            {
                // First run - initialize lastUpdateDate
                lastUpdateDate = currentDate;
                SaveLastUpdateDate();
                Logger.Log($"First run detected. Initialized lastUpdateDate: {lastUpdateDate:yyyy-MM-dd}", LogLevel.Info);

                // Initialize states if they don't exist
                if (todayStates == null)
                {
                    todayStates = new HourlyState { Date = currentDate };
                    Logger.Log("Initialized todayStates for first run", LogLevel.Info);
                }
                if (tomorrowStates == null)
                {
                    tomorrowStates = new HourlyState { Date = currentDate.AddDays(1) };
                    Logger.Log("Initialized tomorrowStates for first run", LogLevel.Info);
                }
            }

            // Update visual indicators (existing logic)
            UpdateVisualIndicators(currentHour);

            // Log current state for debugging (reduced frequency)
            Logger.Log($"Current date: {currentDate:yyyy-MM-dd}, Hour: {currentHour}, Today states: {todayStates?.Date:yyyy-MM-dd}, Tomorrow states: {tomorrowStates?.Date:yyyy-MM-dd}", LogLevel.Debug);
        }

        /// <summary>
        /// Updates the visual indicators for hourly states
        /// </summary>
        /// <param name="currentHour">Current hour (0-23)</param>
        private void UpdateVisualIndicators(int currentHour)
        {
            // Actualizar iconos de hoy
            if (todayStates?.HourStates != null)
            {
                for (int i = 0; i < Math.Min(24, todayStates.HourStates.Length); i++)
                {
                    if (i < iconosHoy.Count)
                        iconosHoy[i].BackColor = todayStates.HourStates[i] ? Color.Red : Color.Gray;
                }
            }

            // Actualizar iconos de mañana
            if (tomorrowStates?.HourStates != null)
            {
                for (int i = 0; i < Math.Min(24, tomorrowStates.HourStates.Length); i++)
                {
                    if (i < iconosMañana.Count)
                        iconosMañana[i].BackColor = tomorrowStates.HourStates[i] ? Color.Red : Color.Gray;
                }
            }

            // Actualizar indicador de hora actual
            if (todayStates?.HourStates != null && currentHour < todayStates.HourStates.Length && panelEstadoHora != null)
            {
                panelEstadoHora.BackColor = todayStates.HourStates[currentHour] ? Color.Red : Color.Gray;
            }
        }

        private void UpdateStates((HourlyState Today, HourlyState Tomorrow) states)
        {
            todayStates = states.Today;
            tomorrowStates = states.Tomorrow;
            lastUpdateDate = DateTime.Now.Date; // Update the last update date
            SaveLastUpdateDate(); // Persist the date
            UpdateHourlyState(); // Actualizar el indicador visual inmediatamente
        }

        /// <summary>
        /// Saves the last update date to a file for persistence across application restarts
        /// </summary>
        private void SaveLastUpdateDate()
        {
            try
            {
                string lastUpdatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "LastUpdateDate.txt");
                File.WriteAllText(lastUpdatePath, lastUpdateDate.ToString("yyyy-MM-dd"));
                Logger.Log($"Last update date saved: {lastUpdateDate:yyyy-MM-dd}", LogLevel.Debug);
            }
            catch (Exception ex)
            {
                Logger.Log($"Error saving last update date: {ex.Message}", LogLevel.Error);
            }
        }

        /// <summary>
        /// Loads the last update date from file, returns DateTime.MinValue if file doesn't exist or is invalid
        /// </summary>
        private DateTime LoadLastUpdateDate()
        {
            try
            {
                string lastUpdatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "LastUpdateDate.txt");
                if (File.Exists(lastUpdatePath))
                {
                    string dateText = File.ReadAllText(lastUpdatePath).Trim();
                    if (DateTime.TryParseExact(dateText, "yyyy-MM-dd", null, DateTimeStyles.None, out DateTime savedDate))
                    {
                        Logger.Log($"Last update date loaded: {savedDate:yyyy-MM-dd}", LogLevel.Debug);
                        return savedDate;
                    }
                    else
                    {
                        Logger.Log($"Invalid date format in LastUpdateDate.txt: {dateText}", LogLevel.Warning);
                    }
                }
                else
                {
                    Logger.Log("LastUpdateDate.txt not found - first run or file deleted", LogLevel.Info);
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error loading last update date: {ex.Message}", LogLevel.Error);
            }
            return DateTime.MinValue;
        }

        /// <summary>
        /// Initializes hourly state tracking by loading the last update date and checking for missed day changes
        /// </summary>
        private void InitializeHourlyStateTracking()
        {
            try
            {
                // Load the last update date from persistence
                lastUpdateDate = LoadLastUpdateDate();
                Logger.Log($"Hourly state tracking initialized. Last update date: {(lastUpdateDate == DateTime.MinValue ? "None (first run)" : lastUpdateDate.ToString("yyyy-MM-dd"))}", LogLevel.Info);

                // Load existing states from CSV if available
                if (gestorDeCorreo != null)
                {
                    var states = gestorDeCorreo.GetCurrentStates();
                    if (states.Today != null || states.Tomorrow != null)
                    {
                        todayStates = states.Today;
                        tomorrowStates = states.Tomorrow;
                        Logger.Log("Estados horarios cargados desde CSV", LogLevel.Info);
                    }
                }

                // Perform initial day change check to handle any missed days
                UpdateHourlyState();
            }
            catch (Exception ex)
            {
                Logger.Log($"Error inicializando seguimiento de estados horarios: {ex.Message}", LogLevel.Error);
                // Initialize with current date as fallback
                lastUpdateDate = DateTime.Now.Date;
                SaveLastUpdateDate();
            }
        }

        private Panel CrearIconoHora(int hora)
        {
            int hora_mas_uno = hora + 1;
            if (hora_mas_uno == 24) hora_mas_uno = 0;
            var panel = new Panel
            {
                Size = new Size(18, 18),
                Margin = new Padding(1),
                BackColor = Color.Gray,
                Tag = hora
            };

            var tooltip = new ToolTip();
            tooltip.SetToolTip(panel, $"{hora:D2}:00 - {hora_mas_uno:D2}:00");

            return panel;
        }

        private void InitializeConsole()
        {
            // Crear el RichTextBox para el log
            consoleTextBox = new RichTextBox
            {
                Multiline = true,
                ReadOnly = true,
                ScrollBars = RichTextBoxScrollBars.Vertical,
                Dock = DockStyle.Bottom,
                Height = 150,
                BackColor = Color.Black,
                ForeColor = Color.LightGreen,
                Font = new Font("Consolas", 9F),
            };

            // Añadir al formulario
            this.Controls.Add(consoleTextBox);
            consoleTextBox.BringToFront();
        }

        private void InitializePublishControls()
        {
            // Limpiar controles previos
            panelConfigContent.Controls.Clear();

            // Set minimum size for the content to enable horizontal scrolling
            // Total width needed: 610 (todos) + 580 (lectura) + 580 (escritura) + spacing = ~1820px
            int totalContentWidth = 1820;
            int totalContentHeight = 920;

            // Ensure the panel content area is large enough to contain all tables
            panelConfigContent.AutoScrollMinSize = new Size(totalContentWidth, totalContentHeight);

            // Crear TableLayoutPanel principal con 3 columnas - Static positioning (removed for direct panel approach)
            // Using direct panel positioning instead of TableLayoutPanel for better control

            // Crear panel para "Todos los tópicos" - Static positioning
            var panelTodosTopicos = new Panel
            {
                Size = new Size(610, 890), // Fixed size for static layout
                Location = new Point(10, 10),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(60, 63, 65),
                Padding = new Padding(5)
            };

            // Crear label título - Static positioning
            var lblTodosTopicos = new Label
            {
                Text = "Todos los tópicos",
                Font = new Font("Segoe UI", 14F, FontStyle.Regular),
                ForeColor = Color.White,
                Size = new Size(600, 30),
                Location = new Point(5, 5),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // Filtro de tópicos - Static positioning
            txtFiltroTopicos = new TextBox
            {
                Size = new Size(600, 25),
                Location = new Point(5, 40),
                Margin = new Padding(0, 5, 0, 5),
                BackColor = Color.FromArgb(60, 63, 65),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                //PlaceholderText = "Filtrar tópicos (desde /Almendralejo/)"
            };
            txtFiltroTopicos.TextChanged += TxtFiltroTopicos_TextChanged;

            // DataGridView para "Todos los Tópicos" - Static positioning
            dgvTodosTopicos = new DataGridView
            {
                Size = new Size(600, 820),
                Location = new Point(5, 70),
                BackgroundColor = Color.FromArgb(45, 45, 48), // Fondo general del control
                GridColor = Color.FromArgb(80, 80, 80), // Color de las líneas de la cuadrícula
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle { BackColor = Color.FromArgb(0, 122, 204), ForeColor = Color.White, Font = new Font("Segoe UI", 9.75F, FontStyle.Bold), Alignment = DataGridViewContentAlignment.MiddleLeft },
                DefaultCellStyle = new DataGridViewCellStyle // Estilo por defecto para todas las celdas de datos
                {
                    BackColor = Color.FromArgb(60, 63, 65), // Fondo de las celdas
                    ForeColor = Color.White,              // Color del texto en las celdas
                    SelectionBackColor = Color.FromArgb(0, 122, 204), // Fondo de la celda seleccionada
                    SelectionForeColor = Color.White,             // Texto de la celda seleccionada
                    Font = new Font("Segoe UI", 9F),
                    Padding = new Padding(2)
                },
                EnableHeadersVisualStyles = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false // Ocultar la columna de encabezado de fila
            };
            dgvTodosTopicos.CellContentClick += DgvTodosTopicos_CellContentClick; // Agregamos el evento

            // Columnas para el DataGridView - Adjust column widths for better layout
            dgvTodosTopicos.Columns.Add(new DataGridViewTextBoxColumn { Name = "Topico", HeaderText = "Tópico", FillWeight = 70 }); // Increased width for topic names

            // Configurar checkboxes con estilo apropiado para tema oscuro - Smaller checkbox columns
            var leerCheckboxColumn = new DataGridViewCheckBoxColumn
            {
                Name = "Leer",
                HeaderText = "Leer",
                TrueValue = true,
                FalseValue = false,
                IndeterminateValue = false,
                FillWeight = 15 // Smaller width for checkbox
            };
            leerCheckboxColumn.DefaultCellStyle.BackColor = Color.FromArgb(60, 63, 65);
            leerCheckboxColumn.DefaultCellStyle.ForeColor = Color.White;
            leerCheckboxColumn.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 122, 204);
            dgvTodosTopicos.Columns.Add(leerCheckboxColumn);

            var escribirCheckboxColumn = new DataGridViewCheckBoxColumn
            {
                Name = "Escribir",
                HeaderText = "Escribir",
                TrueValue = true,
                FalseValue = false,
                IndeterminateValue = false,
                FillWeight = 15 // Smaller width for checkbox
            };
            escribirCheckboxColumn.DefaultCellStyle.BackColor = Color.FromArgb(60, 63, 65);
            escribirCheckboxColumn.DefaultCellStyle.ForeColor = Color.White;
            escribirCheckboxColumn.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 122, 204);
            dgvTodosTopicos.Columns.Add(escribirCheckboxColumn);

            // Añadir controles al panel
            panelTodosTopicos.Controls.Add(dgvTodosTopicos);
            panelTodosTopicos.Controls.Add(txtFiltroTopicos);
            panelTodosTopicos.Controls.Add(lblTodosTopicos);

            // Crear panel para "Tópicos lectura" - Repositioned for better visibility
            var panelLectura = new Panel
            {
                Size = new Size(580, 890), // Reduced width to fit better in available space
                Location = new Point(630, 10),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(60, 63, 65),
                Padding = new Padding(5)
            };

            var lblLectura = new Label
            {
                Text = "Tópicos lectura",
                Font = new Font("Segoe UI", 14F, FontStyle.Regular),
                ForeColor = Color.White,
                Size = new Size(600, 30),
                Location = new Point(5, 5),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // DataGridView lectura - Adjusted size to fit in reduced panel width
            gridTopicosLectura = new DataGridView
            {
                Size = new Size(570, 850), // Reduced width to fit in smaller panel
                Location = new Point(5, 40),
                BackgroundColor = Color.FromArgb(45, 45, 48), // Fondo general del control
                GridColor = Color.FromArgb(80, 80, 80), // Color de las líneas de la cuadrícula
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle { BackColor = Color.FromArgb(0, 122, 204), ForeColor = Color.White, Font = new Font("Segoe UI", 9.75F, FontStyle.Bold), Alignment = DataGridViewContentAlignment.MiddleLeft },
                DefaultCellStyle = new DataGridViewCellStyle // Estilo por defecto para todas las celdas de datos
                {
                    BackColor = Color.FromArgb(60, 63, 65), // Fondo de las celdas
                    ForeColor = Color.White,              // Color del texto en las celdas
                    SelectionBackColor = Color.FromArgb(0, 122, 204), // Fondo de la celda seleccionada
                    SelectionForeColor = Color.White,             // Texto de la celda seleccionada
                    Font = new Font("Segoe UI", 9F),
                    Padding = new Padding(2)
                },
                EnableHeadersVisualStyles = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false // Ocultar la columna de encabezado de fila
            };

            // Columnas lectura - Adjusted column weights for better layout with refresh rate
            gridTopicosLectura.Columns.Add(new DataGridViewTextBoxColumn { Name = "Topico", HeaderText = "Tópico", ReadOnly = true, FillWeight = 30 }); // Reduced to accommodate wider Device ID column

            // Crear dropdown para Device ID en lectura - Increased width for better readability
            var deviceIdComboLectura = new DataGridViewComboBoxColumn
            {
                Name = "IdDispositivo",
                HeaderText = "ID Dispositivo",
                FillWeight = 35, // Increased from 25 to 35 for better readability
                DisplayStyle = DataGridViewComboBoxDisplayStyle.DropDownButton
            };
            gridTopicosLectura.Columns.Add(deviceIdComboLectura);

            gridTopicosLectura.Columns.Add(new DataGridViewTextBoxColumn { Name = "DireccionPLC", HeaderText = "Dirección Modbus", FillWeight = 15 }); // Reduced to accommodate wider Device ID column

            // Add refresh rate dropdown column
            var refreshRateComboLectura = new DataGridViewComboBoxColumn
            {
                Name = "TasaRefresco",
                HeaderText = "Tasa de Refresco",
                FillWeight = 20,
                DisplayStyle = DataGridViewComboBoxDisplayStyle.DropDownButton
            };
            // Populate refresh rate options
            refreshRateComboLectura.Items.AddRange(new string[] { "1s", "5s", "10s", "30s", "1min", "5min" });
            gridTopicosLectura.Columns.Add(refreshRateComboLectura);

            panelLectura.Controls.Add(gridTopicosLectura);

            // Add event handlers for automatic configuration updates
            gridTopicosLectura.CellValueChanged += (s, e) =>
            {
                // Update topic reading configuration when values change
                if (e.RowIndex >= 0 && _topicReadingManager != null)
                {
                    try
                    {
                        _topicReadingManager.UpdateTopicConfigurations(gridTopicosLectura);
                        Logger.Log("TopicReadingManager configuration updated automatically", LogLevel.Debug);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log($"Error auto-updating TopicReadingManager: {ex.Message}", LogLevel.Warning);
                    }
                }
            };
            panelLectura.Controls.Add(lblLectura);

            // Crear panel para "Tópicos escritura" - Repositioned to be visible within window bounds
            var panelEscritura = new Panel
            {
                Size = new Size(580, 890), // Reduced width to fit better
                Location = new Point(1220, 10), // Moved left to be visible within typical window bounds
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(60, 63, 65),
                Padding = new Padding(5)
            };

            var lblEscritura = new Label
            {
                Text = "Tópicos escritura",
                Font = new Font("Segoe UI", 14F, FontStyle.Regular),
                ForeColor = Color.White,
                Size = new Size(600, 30),
                Location = new Point(5, 5),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // DataGridView escritura - Adjusted size to fit in reduced panel width
            gridTopicosEscritura = new DataGridView
            {
                Size = new Size(570, 850), // Reduced width to fit in smaller panel
                Location = new Point(5, 40),
                BackgroundColor = Color.FromArgb(45, 45, 48), // Fondo general del control
                GridColor = Color.FromArgb(80, 80, 80), // Color de las líneas de la cuadrícula
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle { BackColor = Color.FromArgb(0, 122, 204), ForeColor = Color.White, Font = new Font("Segoe UI", 9.75F, FontStyle.Bold), Alignment = DataGridViewContentAlignment.MiddleLeft },
                DefaultCellStyle = new DataGridViewCellStyle // Estilo por defecto para todas las celdas de datos
                {
                    BackColor = Color.FromArgb(60, 63, 65), // Fondo de las celdas
                    ForeColor = Color.White,              // Color del texto en las celdas
                    SelectionBackColor = Color.FromArgb(0, 122, 204), // Fondo de la celda seleccionada
                    SelectionForeColor = Color.White,             // Texto de la celda seleccionada
                    Font = new Font("Segoe UI", 9F),
                    Padding = new Padding(2)
                },
                EnableHeadersVisualStyles = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false // Ocultar la columna de encabezado de fila
            };

            // Columnas escritura - Adjusted column weights for better Device ID readability
            gridTopicosEscritura.Columns.Add(new DataGridViewTextBoxColumn { Name = "Topico", HeaderText = "Tópico", ReadOnly = true, FillWeight = 35 }); // Reduced to accommodate wider Device ID column

            // Crear dropdown para Device ID en escritura - Increased width for better readability
            var deviceIdComboEscritura = new DataGridViewComboBoxColumn
            {
                Name = "IdDispositivo",
                HeaderText = "ID Dispositivo",
                FillWeight = 40, // Increased from 30 to 40 for better readability
                DisplayStyle = DataGridViewComboBoxDisplayStyle.DropDownButton
            };
            gridTopicosEscritura.Columns.Add(deviceIdComboEscritura);

            gridTopicosEscritura.Columns.Add(new DataGridViewTextBoxColumn { Name = "DireccionPLC", HeaderText = "Dirección Modbus", FillWeight = 25 }); // Reduced to accommodate wider Device ID column

            // Add refresh rate column for write topics
            var refreshRateComboEscritura = new DataGridViewComboBoxColumn
            {
                Name = "TasaRefresco",
                HeaderText = "Tasa Refresco",
                FillWeight = 20,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(60, 63, 65),
                    ForeColor = Color.White,
                    SelectionBackColor = Color.FromArgb(0, 122, 204),
                    SelectionForeColor = Color.White,
                    Font = new Font("Segoe UI", 9F)
                }
            };

            // Populate refresh rate options including "Realtime" for write topics
            refreshRateComboEscritura.Items.AddRange(new string[] { "Realtime", "1s", "5s", "10s", "30s", "1min", "5min" });
            gridTopicosEscritura.Columns.Add(refreshRateComboEscritura);

            panelEscritura.Controls.Add(gridTopicosEscritura);
            panelEscritura.Controls.Add(lblEscritura);

            // Add event handlers for automatic configuration updates
            gridTopicosEscritura.CellValueChanged += (s, e) =>
            {
                // Update topic writing configuration when values change
                if (e.RowIndex >= 0 && _topicWritingManager != null)
                {
                    try
                    {
                        _topicWritingManager.UpdateTopicConfigurations(gridTopicosEscritura);
                        Logger.Log("TopicWritingManager configuration updated automatically", LogLevel.Debug);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log($"Error auto-updating TopicWritingManager: {ex.Message}", LogLevel.Warning);
                    }
                }
            };

            // Añadir los 3 paneles directamente al panel principal de publicación - Static positioning
            panelConfigContent.Controls.Add(panelTodosTopicos);
            panelConfigContent.Controls.Add(panelLectura);
            panelConfigContent.Controls.Add(panelEscritura);

            // Inicializar la lista de tópicos filtrada
            TxtFiltroTopicos_TextChanged(null, EventArgs.Empty);

            // Poblar dropdowns de Device ID
            PopulateDeviceIdDropdowns();

            // Add Modbus read timer controls
            AddModbusReadTimerControls();
        }

        /// <summary>
        /// Adds selective Modbus read controls to the MQTT Configuration panel
        /// </summary>
        private void AddModbusReadTimerControls()
        {
            // Create a panel for selective Modbus read controls
            Panel panelModbusTimer = new Panel
            {
                Size = new Size(400, 80),
                Location = new Point(50, 450), // Position below the MQTT configuration tables
                BackColor = Color.FromArgb(60, 63, 65),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Title label
            Label lblModbusTimer = new Label
            {
                Text = "Lectura Selectiva Modbus",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(10, 10)
            };

            // Update configuration button
            Button btnUpdateConfig = new Button
            {
                Text = "Actualizar Configuración",
                Size = new Size(150, 30),
                Location = new Point(10, 30),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            // Start/Stop button
            Button btnModbusTimer = new Button
            {
                Text = "Iniciar Lectura Selectiva",
                Size = new Size(150, 30),
                Location = new Point(170, 30),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            // Status label
            Label lblModbusTimerStatus = new Label
            {
                Text = "Estado: Detenido",
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(10, 65)
            };

            // Event handlers
            btnUpdateConfig.Click += (s, e) =>
            {
                try
                {
                    _topicReadingManager.UpdateTopicConfigurations(gridTopicosLectura);
                    int configuredTopics = _topicReadingManager.GetConfiguredTopicsCount();
                    lblModbusTimerStatus.Text = $"Configuración actualizada: {configuredTopics} tópicos";
                    lblModbusTimerStatus.ForeColor = Color.LightBlue;
                    Logger.Log($"Configuración de lectura selectiva actualizada: {configuredTopics} tópicos configurados", LogLevel.Info);
                }
                catch (Exception ex)
                {
                    Logger.Log($"Error al actualizar configuración de lectura selectiva: {ex.Message}", LogLevel.Error);
                    lblModbusTimerStatus.Text = "Error en configuración";
                    lblModbusTimerStatus.ForeColor = Color.LightCoral;
                }
            };

            btnModbusTimer.Click += (s, e) =>
            {
                try
                {
                    if (!_topicReadingManager.IsReading())
                    {
                        _topicReadingManager.StartReading();
                        btnModbusTimer.Text = "Detener Lectura Selectiva";
                        btnModbusTimer.BackColor = Color.FromArgb(209, 17, 65);
                        int configuredTopics = _topicReadingManager.GetConfiguredTopicsCount();
                        lblModbusTimerStatus.Text = $"Estado: Activo ({configuredTopics} tópicos)";
                        lblModbusTimerStatus.ForeColor = Color.LightGreen;
                        Logger.Log("Lectura selectiva de Modbus iniciada", LogLevel.Info);
                    }
                    else
                    {
                        _topicReadingManager.StopReading();
                        btnModbusTimer.Text = "Iniciar Lectura Selectiva";
                        btnModbusTimer.BackColor = Color.FromArgb(0, 122, 204);
                        lblModbusTimerStatus.Text = "Estado: Detenido";
                        lblModbusTimerStatus.ForeColor = Color.White;
                        Logger.Log("Lectura selectiva de Modbus detenida", LogLevel.Info);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Log($"Error al controlar lectura selectiva: {ex.Message}", LogLevel.Error);
                    lblModbusTimerStatus.Text = "Error en operación";
                    lblModbusTimerStatus.ForeColor = Color.LightCoral;
                }
            };

            // Add controls to panel
            panelModbusTimer.Controls.Add(lblModbusTimer);
            panelModbusTimer.Controls.Add(btnUpdateConfig);
            panelModbusTimer.Controls.Add(btnModbusTimer);
            panelModbusTimer.Controls.Add(lblModbusTimerStatus);

            // Add panel to the main configuration panel
            panelConfigContent.Controls.Add(panelModbusTimer);
        }



        /// <summary>
        /// Populates the Device ID dropdowns with available Modbus device IDs
        /// </summary>
        public void PopulateDeviceIdDropdowns()
        {
            var deviceIds = GetAvailableModbusDeviceIds();

            // Populate read topics dropdown
            if (gridTopicosLectura.Columns["IdDispositivo"] is DataGridViewComboBoxColumn readCombo)
            {
                readCombo.Items.Clear();
                readCombo.Items.AddRange(deviceIds.ToArray());
            }

            // Populate write topics dropdown
            if (gridTopicosEscritura.Columns["IdDispositivo"] is DataGridViewComboBoxColumn writeCombo)
            {
                writeCombo.Items.Clear();
                writeCombo.Items.AddRange(deviceIds.ToArray());
            }
        }

        /// <summary>
        /// Gets available Modbus device IDs from connected instances
        /// </summary>
        /// <returns>List of device ID strings</returns>
        private List<string> GetAvailableModbusDeviceIds()
        {
            var deviceIds = new List<string>();

            try
            {
                // Get device IDs from left Modbus instance (connected or configured) - Using production-ready names
                if (_modbusViewModel?.SelectedConfigurationLeft != null)
                {
                    string leftDeviceId = $"PLC-Principal-{_modbusViewModel.SelectedConfigurationLeft.DeviceId}";
                    if (_modbusViewModel.IsConnectedLeft)
                    {
                        deviceIds.Add($"Conectado - {leftDeviceId} ({_modbusViewModel.SelectedConfigurationLeft.Name})");
                    }
                    else
                    {
                        deviceIds.Add($"No conectado - {leftDeviceId} ({_modbusViewModel.SelectedConfigurationLeft.Name})");
                    }
                }

                // Get device IDs from right Modbus instance (connected or configured) - Using production-ready names
                if (_modbusViewModel?.SelectedConfigurationRight != null)
                {
                    string rightDeviceId = $"PLC-Secundario-{_modbusViewModel.SelectedConfigurationRight.DeviceId}";
                    if (_modbusViewModel.IsConnectedRight)
                    {
                        deviceIds.Add($"Conectado - {rightDeviceId} ({_modbusViewModel.SelectedConfigurationRight.Name})");
                    }
                    else
                    {
                        deviceIds.Add($"No conectado - {rightDeviceId} ({_modbusViewModel.SelectedConfigurationRight.Name})");
                    }
                }

                // If no configurations available, add placeholder options
                if (deviceIds.Count == 0)
                {
                    deviceIds.Add("No hay configuraciones disponibles");
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error al obtener Device IDs de Modbus: {ex.Message}", LogLevel.Error);
                deviceIds.Add("Error al cargar dispositivos");
            }

            return deviceIds;
        }

        /// <summary>
        /// Auto-selects appropriate Device IDs in MQTT Configuration dropdowns when a profile is loaded
        /// </summary>
        public void AutoSelectDeviceIds()
        {
            try
            {
                var availableDeviceIds = GetAvailableModbusDeviceIds();

                // Auto-select if we have any device configurations (connected or not)
                if (availableDeviceIds.Count > 0 && !availableDeviceIds[0].Contains("No hay configuraciones") && !availableDeviceIds[0].Contains("Error"))
                {
                    // Auto-select the first available device ID for read topics
                    if (gridTopicosLectura?.Columns["IdDispositivo"] is DataGridViewComboBoxColumn readCombo)
                    {
                        foreach (DataGridViewRow row in gridTopicosLectura.Rows)
                        {
                            if (!row.IsNewRow && row.Cells["IdDispositivo"].Value == null)
                            {
                                row.Cells["IdDispositivo"].Value = availableDeviceIds[0];
                            }
                        }
                    }

                    // Auto-select the first available device ID for write topics
                    if (gridTopicosEscritura?.Columns["IdDispositivo"] is DataGridViewComboBoxColumn writeCombo)
                    {
                        foreach (DataGridViewRow row in gridTopicosEscritura.Rows)
                        {
                            if (!row.IsNewRow && row.Cells["IdDispositivo"].Value == null)
                            {
                                row.Cells["IdDispositivo"].Value = availableDeviceIds[0];
                            }
                        }
                    }

                    Logger.Log($"Auto-selected Device ID '{availableDeviceIds[0]}' for MQTT Configuration", LogLevel.Info);
                }
                else
                {
                    Logger.Log("No Modbus configurations available for auto-selection", LogLevel.Info);
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error en auto-selección de Device IDs: {ex.Message}", LogLevel.Warning);
            }
        }

        /// <summary>
        /// Performs automatic Modbus reading based on configured read topics and publishes to MQTT
        /// </summary>
        public async Task PerformAutomaticModbusReading()
        {
            if (gridTopicosLectura?.Rows == null || mqttService == null)
                return;

            try
            {
                foreach (DataGridViewRow row in gridTopicosLectura.Rows)
                {
                    if (row.IsNewRow) continue;

                    try
                    {
                        string topic = row.Cells["Topico"].Value?.ToString();
                        string deviceId = row.Cells["IdDispositivo"].Value?.ToString();
                        string modbusAddress = row.Cells["DireccionPLC"].Value?.ToString();

                        // Skip if any required field is empty
                        if (string.IsNullOrEmpty(topic) || string.IsNullOrEmpty(deviceId) || string.IsNullOrEmpty(modbusAddress))
                            continue;

                        // Skip if device ID indicates no configuration or error
                        if (deviceId.Contains("No hay configuraciones") || deviceId.Contains("Error"))
                            continue;

                        // Check if device is connected (extract actual device ID from display text)
                        string actualDeviceId = deviceId;
                        bool isConnected = true;
                        if (deviceId.Contains("No conectado"))
                        {
                            isConnected = false;
                            continue; // Skip reading from disconnected devices
                        }

                        // Skip read operation if device is not connected
                        if (!isConnected)
                        {
                            Logger.Log($"Skipping read from disconnected device: {actualDeviceId}", LogLevel.Debug);
                            continue;
                        }

                        // Parse Modbus address
                        if (!ushort.TryParse(modbusAddress, out ushort address))
                        {
                            Logger.Log($"Invalid Modbus address format: {modbusAddress}", LogLevel.Warning);
                            continue;
                        }

                        // Read from Modbus and publish to MQTT
                        await ReadFromModbusAndPublish(actualDeviceId, address, topic);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log($"Error procesando lectura automática para fila: {ex.Message}", LogLevel.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error en lectura automática de Modbus: {ex.Message}", LogLevel.Error);
            }
        }

        /// <summary>
        /// Reads from a specific Modbus address and publishes the value to MQTT
        /// </summary>
        /// <param name="deviceId">Device ID (e.g., "Left-1" or "Right-1")</param>
        /// <param name="address">Modbus address to read from</param>
        /// <param name="topic">MQTT topic to publish to</param>
        private async Task ReadFromModbusAndPublish(string deviceId, ushort address, string topic)
        {
            try
            {
                // Determine which Modbus service to use based on device ID
                ModbusService modbusService = null;
                if (deviceId.StartsWith("Left-") && _modbusViewModel?.IsConnectedLeft == true)
                {
                    modbusService = _modbusViewModel.GetModbusServiceLeft();
                }
                else if (deviceId.StartsWith("Right-") && _modbusViewModel?.IsConnectedRight == true)
                {
                    modbusService = _modbusViewModel.GetModbusServiceRight();
                }

                if (modbusService == null)
                {
                    Logger.Log($"No connected Modbus service found for device: {deviceId}", LogLevel.Debug);
                    return;
                }

                // Read holding registers (most common case)
                ushort[] values = await modbusService.ReadHoldingRegistersAsync(address, 1);
                if (values != null && values.Length > 0)
                {
                    string value = values[0].ToString();

                    // Publish to MQTT if connected
                    if (mqttService.IsConnected)
                    {
                        await mqttService.PublishAsync(topic, value);
                        Logger.Log($"Published Modbus read value to MQTT: {topic} = {value}", LogLevel.Debug);
                    }
                    else
                    {
                        Logger.Log($"MQTT not connected, cannot publish: {topic} = {value}", LogLevel.Debug);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error reading from Modbus device {deviceId} address {address}: {ex.Message}", LogLevel.Warning);
            }
        }

        /// <summary>
        /// Detects data type based on MQTT topic value
        /// </summary>
        /// <param name="topicValue">The value from the MQTT topic</param>
        /// <returns>Detected data type as string</returns>
        private string DetectDataType(string topicValue)
        {
            if (string.IsNullOrEmpty(topicValue))
                return "unknown";

            // Try to parse as boolean first
            if (bool.TryParse(topicValue, out _) ||
                topicValue.Equals("0", StringComparison.OrdinalIgnoreCase) ||
                topicValue.Equals("1", StringComparison.OrdinalIgnoreCase))
            {
                return "bool";
            }

            // Try to parse as integer
            if (int.TryParse(topicValue, out _))
            {
                return "int";
            }

            // Try to parse as float/real
            if (float.TryParse(topicValue, out _) || double.TryParse(topicValue, out _))
            {
                return "real";
            }

            // Default to string if no specific type detected
            return "string";
        }

        /// <summary>
        /// Wrapper method for TopicWritingManager that converts ushort address to string
        /// </summary>
        /// <param name="deviceId">Device ID string</param>
        /// <param name="modbusAddress">Modbus address as ushort</param>
        /// <param name="value">Value to write</param>
        /// <param name="dataType">Data type of the value</param>
        private async Task WriteToModbusAsyncWrapper(string deviceId, ushort modbusAddress, string value, string dataType)
        {
            await WriteToModbusAsync(deviceId, modbusAddress.ToString(), value, dataType);
        }

        /// <summary>
        /// Writes value to Modbus device based on device ID and address
        /// </summary>
        /// <param name="deviceId">Device ID string (e.g., "Left-1" or "Right-2")</param>
        /// <param name="modbusAddress">Modbus address to write to</param>
        /// <param name="value">Value to write</param>
        /// <param name="dataType">Data type of the value</param>
        private async Task WriteToModbusAsync(string deviceId, string modbusAddress, string value, string dataType)
        {
            try
            {
                if (string.IsNullOrEmpty(deviceId) || deviceId.Contains("No hay") || deviceId.Contains("Error"))
                {
                    Logger.Log("No se puede escribir a Modbus: Device ID no válido", LogLevel.Warning);
                    return;
                }

                if (!ushort.TryParse(modbusAddress, out ushort address))
                {
                    Logger.Log($"Dirección Modbus no válida: {modbusAddress}", LogLevel.Warning);
                    return;
                }

                ModbusService modbusService = null;

                // Determine which Modbus instance to use - Updated for production-ready device names
                if (deviceId.Contains("PLC-Principal-") && _modbusViewModel?.IsConnectedLeft == true)
                {
                    modbusService = _modbusViewModel.GetModbusServiceLeft();
                }
                else if (deviceId.Contains("PLC-Secundario-") && _modbusViewModel?.IsConnectedRight == true)
                {
                    modbusService = _modbusViewModel.GetModbusServiceRight();
                }

                if (modbusService == null)
                {
                    Logger.Log($"Servicio Modbus no disponible para Device ID: {deviceId}", LogLevel.Warning);
                    return;
                }

                // Write based on data type
                bool success = false;
                switch (dataType.ToLower())
                {
                    case "bool":
                        if (bool.TryParse(value, out bool boolValue) ||
                            (value == "1" || value.Equals("true", StringComparison.OrdinalIgnoreCase)))
                        {
                            success = await modbusService.WriteSingleCoilAsync(address, boolValue || value == "1");
                        }
                        break;

                    case "int":
                        if (ushort.TryParse(value, out ushort intValue))
                        {
                            success = await modbusService.WriteSingleRegisterAsync(address, intValue);
                        }
                        break;

                    case "real":
                    case "float":
                        if (float.TryParse(value, out float floatValue))
                        {
                            // Convert float to two 16-bit registers (IEEE 754)
                            byte[] floatBytes = BitConverter.GetBytes(floatValue);
                            ushort[] registers = new ushort[2];
                            registers[0] = BitConverter.ToUInt16(floatBytes, 0);
                            registers[1] = BitConverter.ToUInt16(floatBytes, 2);
                            success = await modbusService.WriteMultipleRegistersAsync(address, registers);
                        }
                        break;

                    default:
                        Logger.Log($"Tipo de dato no soportado para escritura Modbus: {dataType}", LogLevel.Warning);
                        break;
                }

                if (success)
                {
                    Logger.Log($"Escritura Modbus exitosa - Device: {deviceId}, Address: {address}, Value: {value}, Type: {dataType}", LogLevel.Info);
                }
                else
                {
                    Logger.Log($"Error en escritura Modbus - Device: {deviceId}, Address: {address}, Value: {value}", LogLevel.Error);
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Excepción en escritura Modbus: {ex.Message}", LogLevel.Error);
            }
        }

        /// <summary>
        /// Handles MQTT topic value changes and triggers automatic Modbus writes
        /// </summary>
        private async void OnMqttTopicValueChanged()
        {
            try
            {
                // Check write topics for automatic Modbus writing (legacy method)
                await ProcessAutomaticModbusWrites();

                // Handle realtime writes through TopicWritingManager
                if (_topicWritingManager != null && mqttService != null)
                {
                    var messages = mqttService.GetLastMessages();
                    if (messages != null)
                    {
                        // For realtime writes, we need to check which topics changed
                        // Since we don't have change tracking, we'll process all realtime topics
                        var topicConfigs = _topicWritingManager.GetTopicConfigurations();
                        foreach (var config in topicConfigs.Values)
                        {
                            if (config.RefreshRateMs == 0 && messages.TryGetValue(config.Topic, out string value))
                            {
                                await _topicWritingManager.HandleRealtimeWrite(config.Topic, value);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error en procesamiento automático de escritura Modbus: {ex.Message}", LogLevel.Error);
            }
        }

        /// <summary>
        /// Processes automatic Modbus writes for configured write topics
        /// </summary>
        private async Task ProcessAutomaticModbusWrites()
        {
            if (gridTopicosEscritura == null || mqttService == null)
                return;

            var messages = mqttService.GetLastMessages();
            if (messages == null)
                return;

            foreach (DataGridViewRow row in gridTopicosEscritura.Rows)
            {
                if (row.IsNewRow)
                    continue;

                try
                {
                    string topic = row.Cells["Topico"].Value?.ToString();
                    string deviceId = row.Cells["IdDispositivo"].Value?.ToString();
                    string modbusAddress = row.Cells["DireccionPLC"].Value?.ToString();

                    // Skip if any required field is empty
                    if (string.IsNullOrEmpty(topic) || string.IsNullOrEmpty(deviceId) || string.IsNullOrEmpty(modbusAddress))
                        continue;

                    // Skip if device ID indicates no configuration or error
                    if (deviceId.Contains("No hay configuraciones") || deviceId.Contains("Error"))
                        continue;

                    // Check if device is connected (extract actual device ID from display text)
                    string actualDeviceId = deviceId;
                    bool isConnected = true;
                    if (deviceId.Contains("No conectado"))
                    {
                        isConnected = false;
                        // Extract the actual device ID from "No conectado - Left-1 (ConfigName)"
                        var match = System.Text.RegularExpressions.Regex.Match(deviceId, @"No conectado - (.+?) \(");
                        if (match.Success)
                        {
                            actualDeviceId = match.Groups[1].Value;
                        }
                        else
                        {
                            Logger.Log($"Could not extract device ID from: {deviceId}", LogLevel.Warning);
                            continue;
                        }
                    }

                    // Skip write operation if device is not connected
                    if (!isConnected)
                    {
                        Logger.Log($"Skipping write to disconnected device: {actualDeviceId}", LogLevel.Info);
                        continue;
                    }

                    // Check if we have a value for this topic
                    if (messages.TryGetValue(topic, out string topicValue) && !string.IsNullOrEmpty(topicValue))
                    {
                        // Detect data type and write to Modbus
                        string dataType = DetectDataType(topicValue);
                        await WriteToModbusAsync(actualDeviceId, modbusAddress, topicValue, dataType);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Log($"Error procesando escritura automática para fila: {ex.Message}", LogLevel.Error);
                }
            }
        }




        public void TxtFiltroTopicos_TextChanged(object sender, EventArgs e)
        {
            // Comprobaciones de seguridad para los controles
            if (dgvTodosTopicos == null || dgvTodosTopicos.IsDisposed || mqttService == null) return;

            string filtro = (txtFiltroTopicos != null && !txtFiltroTopicos.IsDisposed) ? txtFiltroTopicos.Text.Trim() : "";

            dgvTodosTopicos.SuspendLayout();

            // 1. Guardar el estado actual de los checkboxes de dgvTodosTopicos antes de limpiarlo.
            // Esto preserva las selecciones que el usuario pudo haber hecho antes de filtrar.
            Dictionary<string, (bool isRead, bool isWrite)> currentCheckboxStates = new Dictionary<string, (bool, bool)>(StringComparer.OrdinalIgnoreCase);
            foreach (DataGridViewRow row in dgvTodosTopicos.Rows)
            {
                if (row.IsNewRow) continue;
                string topic = row.Cells["Topico"].Value?.ToString();
                if (!string.IsNullOrEmpty(topic))
                {
                    bool isRead = Convert.ToBoolean(row.Cells["Leer"].Value ?? false);
                    bool isWrite = Convert.ToBoolean(row.Cells["Escribir"].Value ?? false);
                    currentCheckboxStates[topic] = (isRead, isWrite);
                }
            }

            // 2. Also load saved configurations to ensure we have the most up-to-date checkbox states
            // This is important when this method is called after ApplyLoadedPublishConfigurations
            Dictionary<string, (bool isRead, bool isWrite)> savedCheckboxStates = new Dictionary<string, (bool, bool)>(StringComparer.OrdinalIgnoreCase);
            if (_publishConfigManager != null)
            {
                var savedConfigs = _publishConfigManager.LoadConfigurations();
                foreach (var config in savedConfigs)
                {
                    if (!string.IsNullOrEmpty(config.TopicName))
                    {
                        savedCheckboxStates[config.TopicName] = (config.IsForRead, config.IsForWrite);
                    }
                }
                Logger.Log($"TxtFiltroTopicos_TextChanged: Loaded {savedConfigs.Count} saved configurations for checkbox state preservation.", LogLevel.Debug);
            }

            dgvTodosTopicos.Rows.Clear();

            // 2. Obtener tópicos de MQTT que coincidan con el filtro "ALMENDRALEJO" y el filtro del TextBox.
            var topicosFiltered = mqttService.GetLastMessages().Keys
                .Where(t => !string.IsNullOrEmpty(t) && t.IndexOf("ALMENDRALEJO", StringComparison.OrdinalIgnoreCase) >= 0)
                .Where(t => string.IsNullOrEmpty(filtro) ||
                    t.IndexOf(filtro, StringComparison.OrdinalIgnoreCase) >= 0)
                .Distinct(StringComparer.OrdinalIgnoreCase) // Add deduplication to prevent duplicates
                .OrderBy(t => t) // Opcional: ordenar
                .ToList(); // Convert to list for better performance

            // 3. Repoblar dgvTodosTopicos, restaurando el estado de los checkboxes si el tópico ya existía.
            // Use HashSet to ensure no duplicates are added (additional safety check)
            HashSet<string> addedTopics = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            foreach (var topico in topicosFiltered)
            {
                // Skip if topic was already added (additional safety check)
                if (addedTopics.Contains(topico))
                {
                    Logger.Log($"Skipping duplicate topic: {topico}", LogLevel.Debug);
                    continue;
                }

                bool isForRead = false;
                bool isForWrite = false;

                // Priority 1: Use saved configurations (from file) if available
                if (savedCheckboxStates.TryGetValue(topico, out var savedStates))
                {
                    isForRead = savedStates.isRead;
                    isForWrite = savedStates.isWrite;
                    Logger.Log($"TxtFiltroTopicos_TextChanged: Using saved state for topic '{topico}': Read={isForRead}, Write={isForWrite}", LogLevel.Debug);
                }
                // Priority 2: Use current UI state if no saved configuration exists
                else if (currentCheckboxStates.TryGetValue(topico, out var currentStates))
                {
                    isForRead = currentStates.isRead;
                    isForWrite = currentStates.isWrite;
                    Logger.Log($"TxtFiltroTopicos_TextChanged: Using current UI state for topic '{topico}': Read={isForRead}, Write={isForWrite}", LogLevel.Debug);
                }
                // Priority 3: Default to false for new topics
                else
                {
                    Logger.Log($"TxtFiltroTopicos_TextChanged: Using default state for new topic '{topico}': Read=false, Write=false", LogLevel.Debug);
                }

                dgvTodosTopicos.Rows.Add(topico, isForRead, isForWrite);
                addedTopics.Add(topico);
            }

            dgvTodosTopicos.ResumeLayout(true);
            dgvTodosTopicos.Invalidate(); // Forzar repintado completo.
        }

        private void DgvTodosTopicos_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            // Verificar que el clic ocurrió dentro de una celda válida (no en el header)
            if (e.RowIndex < 0 || e.ColumnIndex < 0)
                return;

            DataGridViewColumn clickedColumn = dgvTodosTopicos.Columns[e.ColumnIndex];

            // Solo actuar si es una columna de checkbox ("Leer" o "Escribir")
            if (clickedColumn is DataGridViewCheckBoxColumn)
            {
                // Forzar la confirmación del estado del checkbox inmediatamente.
                // Esto es importante porque CellContentClick se dispara antes de que el valor del checkbox
                // se actualice en la celda de datos subyacente.
                dgvTodosTopicos.CommitEdit(DataGridViewDataErrorContexts.Commit);

                // Obtener el tópico de la fila actual
                string topic = dgvTodosTopicos.Rows[e.RowIndex].Cells["Topico"].Value.ToString();

                // Leer el valor actualizado del checkbox que fue clickeado
                // Este valor refleja el estado del checkbox DESPUÉS del clic del usuario.
                bool isChecked = (bool)dgvTodosTopicos.Rows[e.RowIndex].Cells[e.ColumnIndex].Value;

                try
                {
                    if (clickedColumn.Name == "Leer")
                    {
                        if (isChecked) // Si el checkbox "Leer" AHORA está marcado
                        {
                            // Añadir a la tabla de Tópicos Lectura si no existe ya
                            if (!TopicExistsInGrid(gridTopicosLectura, topic))
                            {
                                gridTopicosLectura.Rows.Add(topic, "", "", "10s"); // Include default refresh rate
                                Logger.Log($"Tópico '{topic}' añadido a la tabla de Lectura.", LogLevel.Info);
                            }
                        }
                        else // Si el checkbox "Leer" AHORA está desmarcado
                        {
                            // Remover de la tabla de Tópicos Lectura
                            RemoveTopicFromGrid(gridTopicosLectura, topic);
                            Logger.Log($"Tópico '{topic}' removido de la tabla de Lectura.", LogLevel.Info);
                        }
                    }
                    else if (clickedColumn.Name == "Escribir")
                    {
                        if (isChecked) // Si el checkbox "Escribir" AHORA está marcado
                        {
                            // Añadir a la tabla de Tópicos Escritura si no existe ya
                            if (!TopicExistsInGrid(gridTopicosEscritura, topic))
                            {
                                gridTopicosEscritura.Rows.Add(topic, "", "", "Realtime"); // Include default refresh rate
                                Logger.Log($"Tópico '{topic}' añadido a la tabla de Escritura.", LogLevel.Info);
                            }
                        }
                        else // Si el checkbox "Escribir" AHORA está desmarcado
                        {
                            // Remover de la tabla de Tópicos Escritura
                            RemoveTopicFromGrid(gridTopicosEscritura, topic);
                            Logger.Log($"Tópico '{topic}' removido de la tabla de Escritura.", LogLevel.Info);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Log($"Error al actualizar tablas de lectura/escritura para el tópico '{topic}': {ex.Message}", LogLevel.Error);
                    MessageBox.Show($"Error al actualizar las tablas para el tópico '{topic}': {ex.Message}",
                                    "Error de Actualización", MessageBoxButtons.OK, MessageBoxIcon.Error);

                    // Opcional: Intentar revertir el estado visual del checkbox en dgvTodosTopicos en caso de error.
                    // Sin embargo, esto puede ser complicado y podría ser mejor solo notificar el error.
                    // Si se desea revertir:
                    // dgvTodosTopicos.Rows[e.RowIndex].Cells[e.ColumnIndex].Value = !isChecked;
                    // dgvTodosTopicos.RefreshEdit(); // Para asegurar que se repinte
                }

                // Auto-save configurations when checkboxes are changed
                try
                {
                    Logger.Log($"DgvTodosTopicos_CellContentClick: Auto-saving configurations after checkbox change for topic '{topic}'", LogLevel.Debug);
                    SavePublishConfigurations();
                    Logger.Log($"DgvTodosTopicos_CellContentClick: Auto-saved configurations successfully", LogLevel.Debug);
                }
                catch (Exception ex)
                {
                    Logger.Log($"DgvTodosTopicos_CellContentClick: Error auto-saving configurations: {ex.Message}", LogLevel.Warning);
                    // Don't show message box for auto-save errors, just log them
                }
            }
        }



        private async void LstTodosTopicos_ItemCheck(object sender, ItemCheckEventArgs e)
        {
            string topic = lstTodosTopicos.Items[e.Index].ToString();
            bool isChecked = e.NewValue == CheckState.Checked;

            if (isChecked)
            {
                // Añadir a la grid de lectura por defecto
                if (!TopicExistsInGrid(gridTopicosLectura, topic))
                {
                    gridTopicosLectura.Rows.Add(topic, "", "");
                }
                if (mqttService != null && mqttService.IsConnected)
                {
                    try
                    {
                        await mqttService.SubscribeToTopic(topic);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log($"Error al suscribirse al tópico {topic}: {ex.Message}", LogLevel.Error);
                        gridTopicosLectura.Rows.RemoveAt(gridTopicosLectura.Rows.Count - 1);
                    }
                }
            }
            else
            {
                // Remover de ambas grids
                if (mqttService != null && mqttService.IsConnected)
                {
                    try
                    {
                        await mqttService.UnsubscribeFromTopic(topic);
                        RemoveTopicFromGrid(gridTopicosLectura, topic);
                        RemoveTopicFromGrid(gridTopicosEscritura, topic);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log($"Error al desuscribirse del tópico {topic}: {ex.Message}", LogLevel.Error);
                        e.NewValue = CheckState.Checked; // Revertir el cambio en el checkbox
                    }
                }
            }
        }

        private bool TopicExistsInGrid(DataGridView grid, string topic)
        {
            foreach (DataGridViewRow row in grid.Rows)
            {
                if (row.Cells["Topico"].Value?.ToString() == topic)
                    return true;
            }
            return false;
        }

        private void RemoveTopicFromGrid(DataGridView grid, string topic)
        {
            foreach (DataGridViewRow row in grid.Rows)
            {
                if (row.Cells["Topico"].Value?.ToString() == topic)
                {
                    grid.Rows.Remove(row);
                    break;
                }
            }
        }

        private void InitializeSinopticoTabSimplified()
        {
            // Configuración del DataGridView (dgvSinopticoData)
            this.dgvSinopticoData.BackgroundColor = Color.FromArgb(45, 45, 48);
            this.dgvSinopticoData.GridColor = Color.FromArgb(80, 80, 80);
            this.dgvSinopticoData.ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Alignment = DataGridViewContentAlignment.MiddleLeft
            };
            this.dgvSinopticoData.DefaultCellStyle = new DataGridViewCellStyle
            {
                BackColor = Color.FromArgb(60, 63, 65),
                ForeColor = Color.White,
                SelectionBackColor = Color.FromArgb(0, 122, 204),
                SelectionForeColor = Color.White,
                Font = new Font("Segoe UI", 11F),
                Padding = new Padding(2)
            };
            this.dgvSinopticoData.EnableHeadersVisualStyles = false;
            this.dgvSinopticoData.AllowUserToAddRows = false;
            this.dgvSinopticoData.AllowUserToDeleteRows = false;
            this.dgvSinopticoData.ReadOnly = true;
            this.dgvSinopticoData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvSinopticoData.RowHeadersVisible = false;

            if (this.dgvSinopticoData.ColumnCount == 0)
            {
                this.dgvSinopticoData.Columns.Add("Topic", "Tópico");
                this.dgvSinopticoData.Columns.Add("Message", "Mensaje");
                this.dgvSinopticoData.Columns["Topic"].FillWeight = 30; // Ajusta el peso según necesites
                this.dgvSinopticoData.Columns["Message"].FillWeight = 70;
            }

            // Configuración del TextBox de filtro (txtSinopticoFilter)
            this.txtSinopticoFilter.BackColor = Color.FromArgb(60, 63, 65);
            this.txtSinopticoFilter.ForeColor = Color.White;
            this.txtSinopticoFilter.BorderStyle = BorderStyle.FixedSingle;
            this.txtSinopticoFilter.Margin = new Padding(0, 0, 0, 5); // Margen inferior para separarlo del DGV
            this.txtSinopticoFilter.TextChanged += TxtSinopticoFilter_TextChanged;

            // Población inicial de datos
            PopulateDgvSinoptico();
        }

        private void TxtSinopticoFilter_TextChanged(object sender, EventArgs e)
        {
            PopulateDgvSinoptico();
        }

        public void PopulateDgvSinoptico() // Hazlo público si necesitas llamarlo desde MqttService u otro lugar
        {

            // Comprobaciones iniciales que no acceden a la UI directamente
            if (mqttService == null || this.dgvSinopticoData == null || this.dgvSinopticoData.IsDisposed) return;

            // Si estamos en un hilo diferente al de la UI, invocar este mismo método en el hilo de la UI.
            if (this.dgvSinopticoData.InvokeRequired)
            {
                // Usamos BeginInvoke para no bloquear el hilo actual de MqttService innecesariamente.
                // Si prefieres bloquear hasta que la UI se actualice, puedes usar Invoke.
                this.dgvSinopticoData.BeginInvoke(new Action(PopulateDgvSinoptico));
                return; // Salir del método actual, ya que se re-invocará en el hilo correcto.
            }

            // Comprobación adicional después de la posible invocación
            if (this.dgvSinopticoData.ColumnCount == 0) return; // Asegura que las columnas estén inicializadas

            this.dgvSinopticoData.SuspendLayout(); // Mejora el rendimiento al actualizar

            try // Añadir un bloque try-finally para asegurar ResumeLayout
            {
                this.dgvSinopticoData.Rows.Clear();

                Dictionary<string, string> messages = mqttService.GetLastMessages();

                if (messages == null)
                {
                    return; // Salir si no hay mensajes
                }

                string filterText = "";
                // txtSinopticoFilter también es un control de UI, su acceso debe ser seguro
                if (this.txtSinopticoFilter != null && !this.txtSinopticoFilter.IsDisposed)
                {
                    filterText = this.txtSinopticoFilter.Text.ToLowerInvariant().Trim();
                }

                foreach (var entry in messages)
                {
                    // First filter: Only show topics that match the ALMENDRALEJO pattern
                    bool matchesAlmendralejo = entry.Key.IndexOf("DD/ENAGAS/ALMENDRALEJO/", StringComparison.OrdinalIgnoreCase) >= 0;

                    // Second filter: Apply user's text filter if provided
                    bool matchesUserFilter = string.IsNullOrEmpty(filterText) ||
                                           entry.Key.ToLowerInvariant().Contains(filterText) ||
                                           (entry.Value?.ToLowerInvariant().Contains(filterText) ?? false);

                    if (matchesAlmendralejo && matchesUserFilter)
                    {
                        // Ya estamos en el hilo de UI, no se necesita Invoke aquí para Add
                        if (!this.dgvSinopticoData.IsDisposed)
                        {
                            this.dgvSinopticoData.Rows.Add(entry.Key, entry.Value);
                        }
                    }
                }
            }
            finally
            {
                if (!this.dgvSinopticoData.IsDisposed) // Comprobar antes de llamar a ResumeLayout
                {
                    this.dgvSinopticoData.ResumeLayout(true);
                }
            }
        }

        private void MqttService_MessagesUpdated()
        {
            PopulateDgvSinoptico();
        }




        public void ApplyLoadedPublishConfigurations()
        {
            // Asegurarse de que los controles y servicios necesarios estén listos
            if (dgvTodosTopicos == null || gridTopicosLectura == null || gridTopicosEscritura == null || mqttService == null || _publishConfigManager == null)
            {
                Logger.Log("ApplyLoadedPublishConfigurations: Controles, servicio MQTT o PublishConfigManager no listos.", LogLevel.Warning);
                return;
            }

            List<TopicPublishConfig> loadedConfigs = _publishConfigManager.LoadConfigurations();
            // Si necesitas depurar qué se carga:
            // LogLoadedConfigurations(loadedConfigs); // Asegúrate de tener este método o similar para ver los datos.

            // Suspender el diseño para optimizar el rendimiento durante la actualización masiva.
            dgvTodosTopicos.SuspendLayout();
            gridTopicosLectura.SuspendLayout();
            gridTopicosEscritura.SuspendLayout();

            try
            {
                // Limpiar las tres grillas antes de repoblarlas.
                dgvTodosTopicos.Rows.Clear();
                gridTopicosLectura.Rows.Clear();
                gridTopicosEscritura.Rows.Clear();

                if (loadedConfigs == null || !loadedConfigs.Any())
                {
                    Logger.Log("No hay configuraciones de publicación cargadas. Poblando dgvTodosTopicos desde MQTT si hay tópicos (todos desmarcados).", LogLevel.Info);
                    // Si no hay configuraciones guardadas, simplemente mostrar los tópicos actuales de MQTT, desmarcados.
                    // Filtrados por "ALMENDRALEJO" como en TxtFiltroTopicos_TextChanged.
                    var topicsFromMqttInitial = mqttService.GetLastMessages().Keys
                        .Where(t => !string.IsNullOrEmpty(t) && t.IndexOf("ALMENDRALEJO", StringComparison.OrdinalIgnoreCase) >= 0)
                        .OrderBy(t => t) // Opcional: ordenar
                        .ToList();
                    foreach (var topicName in topicsFromMqttInitial)
                    {
                        dgvTodosTopicos.Rows.Add(topicName, false, false);
                    }
                    return; // Salir, ya que no hay configuraciones específicas que aplicar.
                }

                // Obtener todos los tópicos únicos: los de la configuración guardada Y los actualmente en mqttService.
                var topicsFromConfig = loadedConfigs.Select(c => c.TopicName);
                var topicsFromMqtt = mqttService.GetLastMessages().Keys; // Puede estar vacío al inicio si no hay conexión o mensajes.

                // Unir y filtrar, asegurando que no haya duplicados y aplicando el filtro "ALMENDRALEJO".
                // Usar StringComparer.OrdinalIgnoreCase para la unión y la comparación previene problemas de mayúsculas/minúsculas.
                var allUniqueTopics = topicsFromConfig.Union(topicsFromMqtt, StringComparer.OrdinalIgnoreCase)
                                                  .Where(t => !string.IsNullOrEmpty(t) && t.IndexOf("ALMENDRALEJO", StringComparison.OrdinalIgnoreCase) >= 0)
                                                  .OrderBy(t => t)
                                                  .ToList();

                if (!allUniqueTopics.Any())
                {
                    Logger.Log("Después de unir tópicos de configuración y MQTT, y aplicar filtro 'ALMENDRALEJO', no hay tópicos para mostrar.", LogLevel.Info);
                    return;
                }

                Logger.Log($"Procesando {allUniqueTopics.Count} tópicos únicos para aplicar configuración a dgvTodosTopicos.", LogLevel.Debug);

                // Poblar dgvTodosTopicos y las grillas de lectura/escritura
                // Use HashSet to track added topics and prevent duplicates
                HashSet<string> addedTopicsInConfig = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

                foreach (string topicName in allUniqueTopics)
                {
                    // Skip if topic was already added (additional safety check)
                    if (addedTopicsInConfig.Contains(topicName))
                    {
                        Logger.Log($"Skipping duplicate topic in ApplyLoadedPublishConfigurations: {topicName}", LogLevel.Debug);
                        continue;
                    }

                    bool isForRead = false;
                    bool isForWrite = false;

                    // Buscar la configuración guardada para este tópico (insensible a mayúsculas/minúsculas).
                    var savedConfig = loadedConfigs.FirstOrDefault(c => string.Equals(c.TopicName, topicName, StringComparison.OrdinalIgnoreCase));

                    if (savedConfig != null)
                    {
                        isForRead = savedConfig.IsForRead;
                        isForWrite = savedConfig.IsForWrite;

                        // Si estaba marcado para Leer en la configuración, añadirlo a gridTopicosLectura
                        if (savedConfig.IsForRead)
                        {
                            gridTopicosLectura.Rows.Add(topicName, savedConfig.Read_IdDispositivo ?? "", savedConfig.Read_DireccionPLC ?? "", savedConfig.Read_TasaRefresco ?? "10s");
                        }

                        // Si estaba marcado para Escribir en la configuración, añadirlo a gridTopicosEscritura
                        if (savedConfig.IsForWrite)
                        {
                            gridTopicosEscritura.Rows.Add(topicName, savedConfig.Write_IdDispositivo ?? "", savedConfig.Write_DireccionPLC ?? "", savedConfig.Write_TasaRefresco ?? "Realtime");
                        }
                    }
                    // Añadir la fila a dgvTodosTopicos CON los estados de checkbox correctos (isForRead, isForWrite).
                    // Si no hay savedConfig para un tópico (vino de MQTT actual pero no de config), los checkboxes estarán false.
                    dgvTodosTopicos.Rows.Add(topicName, isForRead, isForWrite);
                    addedTopicsInConfig.Add(topicName);
                }
            }
            finally
            {
                // Reanudar el diseño y forzar un repintado.
                dgvTodosTopicos.ResumeLayout(true);
                gridTopicosLectura.ResumeLayout(true);
                gridTopicosEscritura.ResumeLayout(true);

                // Invalidate() es una forma robusta de asegurar que el control se redibuje.
                dgvTodosTopicos.Invalidate();
                // gridTopicosLectura.Invalidate(); // Si también necesitaran refresco visual.
                // gridTopicosEscritura.Invalidate();

                // Force refresh of checkbox display by refreshing the entire grid
                dgvTodosTopicos.Refresh();

                // Ensure checkbox columns are properly refreshed
                foreach (DataGridViewColumn column in dgvTodosTopicos.Columns)
                {
                    if (column is DataGridViewCheckBoxColumn)
                    {
                        column.Visible = false;
                        column.Visible = true;
                    }
                }
            }

            Logger.Log("Configuraciones de publicación aplicadas a la UI (incluyendo checkboxes en dgvTodosTopicos).", LogLevel.Info);
        }

        public void SavePublishConfigurations()
        {
            try
            {
                Logger.Log("SavePublishConfigurations: Starting save process...", LogLevel.Debug);

                // Ensure any pending edits are committed before reading values
                if (dgvTodosTopicos != null && !dgvTodosTopicos.IsDisposed)
                {
                    Logger.Log("SavePublishConfigurations: Committing pending edits in dgvTodosTopicos...", LogLevel.Debug);

                    // Force end any current edit operation
                    dgvTodosTopicos.CurrentCell = null;
                    dgvTodosTopicos.CommitEdit(DataGridViewDataErrorContexts.Commit);
                    dgvTodosTopicos.EndEdit();

                    // Additional step to ensure all changes are committed
                    Application.DoEvents();

                    Logger.Log($"SavePublishConfigurations: dgvTodosTopicos has {dgvTodosTopicos.Rows.Count} rows", LogLevel.Debug);
                }

                List<TopicPublishConfig> currentConfigs = GetCurrentPublishConfigurations();

                Logger.Log($"SavePublishConfigurations: Retrieved {currentConfigs.Count} configurations from UI", LogLevel.Debug);

                // Log each configuration for debugging
                foreach (var config in currentConfigs)
                {
                    Logger.Log($"SavePublishConfigurations: Config - Topic: '{config.TopicName}', Read: {config.IsForRead}, Write: {config.IsForWrite}", LogLevel.Debug);
                }

                _publishConfigManager.SaveConfigurations(currentConfigs);

                Logger.Log($"Configuraciones de publicación guardadas exitosamente. Total: {currentConfigs.Count} configuraciones.", LogLevel.Info);
            }
            catch (Exception ex)
            {
                Logger.Log($"Error al guardar las configuraciones de publicación: {ex.Message}", LogLevel.Error);
                Logger.Log($"Stack trace: {ex.StackTrace}", LogLevel.Error);
                MessageBox.Show($"Error al guardar las configuraciones: {ex.Message}",
                              "Error de Guardado", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public void UpdateFormFieldsFromProfile(ConnectionProfile profile)
        {
            if (profile == null) profile = new ConnectionProfile();

            // Email configuration - no MAPI validation needed

            // Email mode selection
            chkUseEws.Checked = profile.UseEws;

            // POP3 settings
            txtServidor.Text = profile.Pop3Server;
            txtPuerto.Text = profile.Pop3Port.ToString();
            txtUsuario.Text = profile.Pop3Username;
            txtContraseña.Text = profile.Pop3Password;
            chkPop3Ssl.Checked = profile.Pop3UseSsl;

            // EWS settings
            txtEwsServerUrl.Text = profile.EwsServerUrl;
            txtEwsUsername.Text = profile.EwsUsername;
            txtEwsPassword.Text = profile.EwsPassword;
            txtEwsDomain.Text = profile.EwsDomain;
            chkEwsUseOAuth.Checked = profile.EwsUseOAuth;

            // Email notification addresses
            txtEmailNotif1.Text = profile.EmailNotification1 ?? "";
            txtEmailNotif2.Text = profile.EmailNotification2 ?? "";
            txtEmailNotif3.Text = profile.EmailNotification3 ?? "";
            txtEmailNotif4.Text = profile.EmailNotification4 ?? "";
            txtEmailNotif5.Text = profile.EmailNotification5 ?? "";

            // Restore email checking settings
            if (nudCheckInterval != null)
            {
                nudCheckInterval.Value = Math.Max(1, Math.Min(60, profile.EmailCheckIntervalMinutes));
                checkTimer.Interval = profile.EmailCheckIntervalMinutes * 60 * 1000;
            }

            // Update email controls state based on selected mode
            UpdateEmailControlsState();

            // Initialize email manager with current configuration
            InitializeEmailManager();

            // Start email processing with proper sequencing to avoid race conditions
            StartEmailProcessingSequentially(profile);
        }

        /// <summary>
        /// Starts email processing with proper sequencing to avoid race conditions
        /// Ensures connection is established before enabling auto-processing
        /// </summary>
        public async void StartEmailProcessingSequentially(ConnectionProfile profile)
        {
            try
            {
                Logger.Log("Starting email processing with sequential initialization...", LogLevel.Info);

                // Step 1: Stop any existing timers to prevent conflicts
                if (checkTimer?.Enabled == true)
                {
                    checkTimer.Stop();
                    Logger.Log("Stopped existing email check timer", LogLevel.Info);
                }
                StopEwsPeriodicVerification();

                // Step 2: Test connection first before enabling any auto-processing
                bool connectionEstablished = false;
                if (gestorDeCorreo != null)
                {
                    Logger.Log("Testing email connection before enabling auto-processing...", LogLevel.Info);
                    connectionEstablished = await gestorDeCorreo.ProbarConexionAsync().ConfigureAwait(false);
                    Logger.Log($"Email connection test result: {connectionEstablished}", LogLevel.Info);
                }

                // Step 3: Only enable auto-processing if connection is successful AND profile allows it
                if (connectionEstablished && profile.AutoStartEmailChecking && checkTimer != null)
                {
                    // Small delay to ensure connection is fully stabilized
                    await Task.Delay(1000);

                    this.Invoke(new Action(() =>
                    {
                        checkTimer.Start();
                        if (btnStartTimer != null)
                        {
                            btnStartTimer.Text = "Detener Verificación Automática";
                            btnStartTimer.BackColor = Color.FromArgb(209, 17, 65);
                        }
                        if (lblTimerStatus != null)
                        {
                            lblTimerStatus.Text = $"Estado: Activo (cada {profile.EmailCheckIntervalMinutes} min)";
                            lblTimerStatus.ForeColor = Color.LightGreen;
                        }
                    }));

                    Logger.Log("Email auto-processing enabled after successful connection test", LogLevel.Info);

                    // Step 4: Start EWS periodic verification AFTER regular email processing is established
                    if (profile.UseEws)
                    {
                        await Task.Delay(2000); // Additional delay for EWS
                        StartEwsPeriodicVerification();
                        Logger.Log("EWS periodic verification started after email processing initialization", LogLevel.Info);
                    }

                    // Step 5: Perform initial email check AFTER all timers are properly set up
                    await Task.Delay(3000); // Ensure all initialization is complete
                    Logger.Log("Performing initial email verification after full initialization...", LogLevel.Info);
                    await CheckAndProcessEmails();
                }
                else
                {
                    Logger.Log($"Email auto-processing not enabled. Connection: {connectionEstablished}, AutoStart: {profile.AutoStartEmailChecking}", LogLevel.Info);

                    // Still start EWS verification if configured, but without auto-processing
                    if (profile.UseEws)
                    {
                        StartEwsPeriodicVerification();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error during sequential email processing startup: {ex.Message}", LogLevel.Error);

                // Ensure UI is updated on error
                this.Invoke(new Action(() =>
                {
                    if (lblTimerStatus != null)
                    {
                        lblTimerStatus.Text = "Estado: Error en inicialización";
                        lblTimerStatus.ForeColor = Color.Red;
                    }
                }));
            }
        }

        /// <summary>
        /// Updates the background color of the panelEstado control (POP3).
        /// Ensures the operation is performed on the UI thread.
        /// </summary>
        /// <param name="color">The color to set as the background.</param>
        public void UpdatePanelEstadoColor(System.Drawing.Color color)
        {
            if (panelEstado == null || panelEstado.IsDisposed)
            {
                Logger.Log("UpdatePanelEstadoColor: panelEstado is null or disposed.", LogLevel.Warning);
                return;
            }

            if (panelEstado.InvokeRequired)
            {
                panelEstado.Invoke(new Action(() => panelEstado.BackColor = color));
            }
            else
            {
                panelEstado.BackColor = color;
            }
        }

        /// <summary>
        /// Updates the background color of the panelEwsEstado control (EWS).
        /// Ensures the operation is performed on the UI thread.
        /// </summary>
        /// <param name="color">The color to set as the background.</param>
        public void UpdatePanelEwsEstadoColor(System.Drawing.Color color)
        {
            if (panelEwsEstado == null || panelEwsEstado.IsDisposed)
            {
                Logger.Log("UpdatePanelEwsEstadoColor: panelEwsEstado is null or disposed.", LogLevel.Warning);
                return;
            }

            if (panelEwsEstado.InvokeRequired)
            {
                panelEwsEstado.Invoke(new Action(() => panelEwsEstado.BackColor = color));
            }
            else
            {
                panelEwsEstado.BackColor = color;
            }
        }

        public void ClearUIAndTopicMemoryState()
        {
            if (dgvSinopticoData != null && !dgvSinopticoData.IsDisposed)
            {
                dgvSinopticoData.Rows.Clear();
            }
            listActiveSubscriptions.Items.Clear(); // Añadir esta línea
            if (mqttService != null)
            {
                mqttService.ClearStoredMessages();
            }
        }

        /// <summary>
        /// Ensures Modbus configurations are properly loaded and synchronized with the UI
        /// This method should be called when loading profiles to ensure Modbus settings are restored
        /// </summary>
        /// <param name="preserveCurrentSelections">If true, preserves current left/right selections when reloading configurations</param>
        public void EnsureModbusConfigurationsSynchronized(bool preserveCurrentSelections = false)
        {
            Logger.Log($"EnsureModbusConfigurationsSynchronized: Called with preserveCurrentSelections={preserveCurrentSelections}", LogLevel.Info);

            if (_modbusViewModel == null)
            {
                Logger.Log("EnsureModbusConfigurationsSynchronized: ModbusViewModel is null.", LogLevel.Warning);
                return;
            }

            Logger.Log($"EnsureModbusConfigurationsSynchronized: Before reload - Available configurations count: {_modbusViewModel.AvailableConfigurations?.Count ?? 0}", LogLevel.Info);
            if (_modbusViewModel.AvailableConfigurations?.Any() == true)
            {
                Logger.Log($"EnsureModbusConfigurationsSynchronized: Before reload - Current selections: Left='{_modbusViewModel.SelectedConfigurationLeft?.Name}', Right='{_modbusViewModel.SelectedConfigurationRight?.Name}'", LogLevel.Info);
            }

            try
            {
                // Reload configurations from files
                _modbusViewModel.LoadConfigurations(preserveCurrentSelections);
                Logger.Log($"EnsureModbusConfigurationsSynchronized: Modbus configurations reloaded. Preserve selections: {preserveCurrentSelections}", LogLevel.Info);
                Logger.Log($"EnsureModbusConfigurationsSynchronized: After reload - Available configurations count: {_modbusViewModel.AvailableConfigurations?.Count ?? 0}", LogLevel.Info);
                if (_modbusViewModel.AvailableConfigurations?.Any() == true)
                {
                    Logger.Log($"EnsureModbusConfigurationsSynchronized: After reload - Final selections: Left='{_modbusViewModel.SelectedConfigurationLeft?.Name}', Right='{_modbusViewModel.SelectedConfigurationRight?.Name}'", LogLevel.Info);
                }

                // Update UI for both panels
                UpdateUIFromModbusViewModel(isLeft: true);
                UpdateUIFromModbusViewModel(isLeft: false);
                Logger.Log("EnsureModbusConfigurationsSynchronized: UI updated for both Modbus panels.", LogLevel.Info);

                // Ensure ComboBox data sources are refreshed
                if (cmbModbusConfigLeft != null)
                {
                    Logger.Log($"EnsureModbusConfigurationsSynchronized: Refreshing left ComboBox with {_modbusViewModel.AvailableConfigurations.Count} configurations", LogLevel.Debug);
                    cmbModbusConfigLeft.DataSource = null;
                    cmbModbusConfigLeft.DataSource = _modbusViewModel.AvailableConfigurations.ToList();
                    cmbModbusConfigLeft.DisplayMember = "Name";
                    cmbModbusConfigLeft.ValueMember = "Name";
                    cmbModbusConfigLeft.SelectedItem = _modbusViewModel.SelectedConfigurationLeft;
                    Logger.Log($"EnsureModbusConfigurationsSynchronized: Left ComboBox selected item set to: {_modbusViewModel.SelectedConfigurationLeft?.Name ?? "null"}", LogLevel.Debug);
                }

                if (cmbModbusConfigRight != null)
                {
                    Logger.Log($"EnsureModbusConfigurationsSynchronized: Refreshing right ComboBox with {_modbusViewModel.AvailableConfigurations.Count} configurations", LogLevel.Debug);
                    cmbModbusConfigRight.DataSource = null;
                    cmbModbusConfigRight.DataSource = _modbusViewModel.AvailableConfigurations.ToList();
                    cmbModbusConfigRight.DisplayMember = "Name";
                    cmbModbusConfigRight.ValueMember = "Name";
                    cmbModbusConfigRight.SelectedItem = _modbusViewModel.SelectedConfigurationRight;
                    Logger.Log($"EnsureModbusConfigurationsSynchronized: Right ComboBox selected item set to: {_modbusViewModel.SelectedConfigurationRight?.Name ?? "null"}", LogLevel.Debug);
                }

                Logger.Log($"EnsureModbusConfigurationsSynchronized: Synchronized {_modbusViewModel.AvailableConfigurations.Count} Modbus configurations.", LogLevel.Info);
            }
            catch (Exception ex)
            {
                Logger.Log($"EnsureModbusConfigurationsSynchronized: Error synchronizing Modbus configurations: {ex.Message}", LogLevel.Error);
            }
        }

        // Status indicator update methods for real-time connection feedback
        public void UpdateMqttStatus(bool isConnected, string statusText = "")
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => UpdateMqttStatus(isConnected, statusText)));
                return;
            }

            if (circleStatus != null && lblStatus != null)
            {
                if (isConnected)
                {
                    circleStatus.BackColor = Color.FromArgb(0, 150, 0); // Green for connected
                    lblStatus.Text = string.IsNullOrEmpty(statusText) ? "MQTT: Conectado" : statusText;
                    lblStatus.ForeColor = Color.LightGreen;
                }
                else
                {
                    circleStatus.BackColor = Color.FromArgb(180, 0, 0); // Red for disconnected
                    lblStatus.Text = string.IsNullOrEmpty(statusText) ? "MQTT: Desconectado" : statusText;
                    lblStatus.ForeColor = Color.LightCoral;
                }
            }
        }

        public void UpdateEmailStatus(bool isConnected, string statusText = "", bool isError = false)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => UpdateEmailStatus(isConnected, statusText, isError)));
                return;
            }

            if (circleStatusEmail != null && lblStatusEmail != null)
            {
                if (isError)
                {
                    circleStatusEmail.BackColor = Color.FromArgb(200, 100, 0); // Orange for error
                    lblStatusEmail.Text = string.IsNullOrEmpty(statusText) ? "Correo: En error" : statusText;
                    lblStatusEmail.ForeColor = Color.Orange;
                }
                else if (isConnected)
                {
                    circleStatusEmail.BackColor = Color.FromArgb(0, 150, 0); // Green for connected
                    lblStatusEmail.Text = string.IsNullOrEmpty(statusText) ? "Correo: Conectado" : statusText;
                    lblStatusEmail.ForeColor = Color.LightGreen;
                }
                else
                {
                    circleStatusEmail.BackColor = Color.FromArgb(180, 0, 0); // Red for disconnected
                    lblStatusEmail.Text = string.IsNullOrEmpty(statusText) ? "Correo: Desconectado" : statusText;
                    lblStatusEmail.ForeColor = Color.LightCoral;
                }
            }
        }

        public void UpdateModbusStatus(bool isConnected, string statusText = "")
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => UpdateModbusStatus(isConnected, statusText)));
                return;
            }

            if (circleStatusModbus != null && lblStatusModbus != null)
            {
                if (isConnected)
                {
                    circleStatusModbus.BackColor = Color.FromArgb(0, 150, 0); // Green for connected
                    lblStatusModbus.Text = string.IsNullOrEmpty(statusText) ? "Modbus: Conectado" : statusText;
                    lblStatusModbus.ForeColor = Color.LightGreen;
                }
                else
                {
                    circleStatusModbus.BackColor = Color.FromArgb(180, 0, 0); // Red for disconnected
                    lblStatusModbus.Text = string.IsNullOrEmpty(statusText) ? "Modbus: Desconectado" : statusText;
                    lblStatusModbus.ForeColor = Color.LightCoral;
                }
            }
        }

        public void UpdateModbusConnectingStatus(string statusText = "")
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => UpdateModbusConnectingStatus(statusText)));
                return;
            }

            if (circleStatusModbus != null && lblStatusModbus != null)
            {
                circleStatusModbus.BackColor = Color.FromArgb(200, 200, 0); // Yellow for connecting
                lblStatusModbus.Text = string.IsNullOrEmpty(statusText) ? "Modbus: Conectando..." : statusText;
                lblStatusModbus.ForeColor = Color.Yellow;
            }
        }
    }
}