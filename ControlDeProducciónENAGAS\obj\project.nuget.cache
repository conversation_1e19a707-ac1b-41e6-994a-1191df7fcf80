{"version": 2, "dgSpecHash": "wcJgxdhKBN8=", "success": true, "projectFilePath": "C:\\ProgramaciónIA\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\easymodbustcp\\5.6.0\\easymodbustcp.5.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mqttnet\\4.3.7.1207\\mqttnet.4.3.7.1207.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-arm.runtime.native.system.io.ports\\8.0.0\\runtime.linux-arm.runtime.native.system.io.ports.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-arm64.runtime.native.system.io.ports\\8.0.0\\runtime.linux-arm64.runtime.native.system.io.ports.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-x64.runtime.native.system.io.ports\\8.0.0\\runtime.linux-x64.runtime.native.system.io.ports.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.io.ports\\8.0.0\\runtime.native.system.io.ports.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx-arm64.runtime.native.system.io.ports\\8.0.0\\runtime.osx-arm64.runtime.native.system.io.ports.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx-x64.runtime.native.system.io.ports\\8.0.0\\runtime.osx-x64.runtime.native.system.io.ports.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.ports\\8.0.0\\system.io.ports.8.0.0.nupkg.sha512"], "logs": [{"code": "NU1603", "level": "Warning", "warningLevel": 1, "message": "ControlDeProducciónENAGAS depende de MQTTnet (>= 4.3.7), pero no se encontró MQTTnet 4.3.7. Se resolvió una mejor coincidencia aproximada de MQTTnet 4.3.7.1207.", "libraryId": "MQTTnet", "targetGraphs": ["net8.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "El paquete \"EasyModbusTCP 5.6.0\" se restauró con \".NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1\" en lugar de la plataforma de destino del proyecto \"net8.0-windows7.0\". Puede que el paquete no sea totalmente compatible con el proyecto.", "libraryId": "EasyModbusTCP", "targetGraphs": ["net8.0-windows7.0"]}]}