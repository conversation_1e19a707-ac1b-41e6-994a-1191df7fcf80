using prueba.Services;
using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Drawing;


namespace prueba
{
    /// <summary>
    /// Manages UI interactions related to MQTT connection.
    /// </summary>
    public class MqttConnectionUIManager
    {
        private readonly Form1 _form;
        private readonly ComboBox _cmbHost;
        private readonly TextBox _txtHostAddress;
        private readonly TextBox _txtPort;
        private readonly TextBox _txtClientID;
        private readonly TextBox _txtUsername;
        private readonly TextBox _txtPassword;
        private readonly CheckBox _chkSslTls;
        private readonly RadioButton _rdoCaSigned;

        /// <summary>
        /// Initializes a new instance of the <see cref="MqttConnectionUIManager"/> class.
        /// </summary>
        /// <param name="form">The main form instance, used to call public methods like ClearUIAndTopicMemoryState and PopulateDgvSinoptico, and access public fields like currentLoadedProfileIndex and currentActiveProfile.</param>
        /// <param name="mqttService">The MQTT service instance.</param>
        /// <param name="cmbHost">ComboBox for MQTT host.</param>
        /// <param name="txtHostAddress">TextBox for MQTT host address.</param>
        /// <param name="txtPort">TextBox for MQTT port.</param>
        /// <param name="txtClientID">TextBox for MQTT client ID.</param>
        /// <param name="txtUsername">TextBox for MQTT username.</param>
        /// <param name="txtPassword">TextBox for MQTT password.</param>
        /// <param name="chkSslTls">CheckBox for SSL/TLS.</param>
        /// <param name="rdoCaSigned">RadioButton for CA signed certificate.</param>
        public MqttConnectionUIManager(
            Form1 form,
            MqttService mqttService,
            ComboBox cmbHost,
            TextBox txtHostAddress,
            TextBox txtPort,
            TextBox txtClientID,
            TextBox txtUsername,
            TextBox txtPassword,
            CheckBox chkSslTls,
            RadioButton rdoCaSigned)
        {
            _form = form ?? throw new ArgumentNullException(nameof(form));
            mqttService = mqttService ?? throw new ArgumentNullException(nameof(mqttService));
            _cmbHost = cmbHost ?? throw new ArgumentNullException(nameof(cmbHost));
            _txtHostAddress = txtHostAddress ?? throw new ArgumentNullException(nameof(txtHostAddress));
            _txtPort = txtPort ?? throw new ArgumentNullException(nameof(txtPort));
            _txtClientID = txtClientID ?? throw new ArgumentNullException(nameof(txtClientID));
            _txtUsername = txtUsername ?? throw new ArgumentNullException(nameof(txtUsername));
            _txtPassword = txtPassword ?? throw new ArgumentNullException(nameof(txtPassword));
            _chkSslTls = chkSslTls ?? throw new ArgumentNullException(nameof(chkSslTls));
            _rdoCaSigned = rdoCaSigned ?? throw new ArgumentNullException(nameof(rdoCaSigned));
        }

        /// <summary>
        /// Connects to the MQTT broker using parameters from the UI and current profile.
        /// </summary>
        public async Task ConnectToBroker()
        {
            if (_form.mqttService == null) return;

            if (!int.TryParse(_txtPort.Text.Trim(), out int port))
            {
                MessageBox.Show("El puerto MQTT debe ser un número válido.", "Puerto Inválido", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Logger.Log("El puerto MQTT debe ser un número válido.", LogLevel.Error);
                return;
            }

            // 1. Limpiar la UI de tópicos y la memoria de tópicos de la sesión anterior
            //_form.ClearUIAndTopicMemoryState();

            // 2. Determinar el identificador del perfil para cargar/guardar tópicos
            string profileIdentifierForTopics = null;
            if (_form.currentLoadedProfileIndex.HasValue && _form.currentActiveProfile != null && !string.IsNullOrWhiteSpace(_form.currentActiveProfile.Name))
            {
                // Usar el nombre del perfil como identificador. Asegurarse que sea un nombre de archivo válido.
                profileIdentifierForTopics = _form.currentActiveProfile.Name;
            }

            // 3. Cargar los tópicos para el perfil actual (si hay identificador) ANTES de conectar
            _form.mqttService.LoadTopicsForProfile(profileIdentifierForTopics);

            // 4. Poblar la UI con los tópicos cargados (si los hubo)
            var initialTopics = _form.mqttService.GetLastMessages();
            _form.PopulateDgvSinoptico();

            // 5. Proceder con la conexión MQTT usando los datos de los campos de Form1
            await _form.mqttService.ConnectAsync(
                _cmbHost.SelectedItem?.ToString() ?? "mqtt://",
                _txtHostAddress.Text.Trim(),
                port,
                _txtClientID.Text.Trim(),
                _txtUsername.Text.Trim(),
                _txtPassword.Text, // No hacer Trim a la contraseña
                _chkSslTls.Checked,
                _rdoCaSigned.Checked,
                false // Valor para useAlpn, puedes cambiarlo según tus necesidades
            );
        }

        
    }
} 