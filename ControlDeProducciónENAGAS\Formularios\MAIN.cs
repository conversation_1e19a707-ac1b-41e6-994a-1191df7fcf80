﻿using ControlDeProducciónENAGAS.Clases;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using EasyModbus;

namespace ControlDeProducciónENAGAS.Formularios
{
    public partial class MAIN : Form
    {
        // Variables para gestión de correo
        private ConfiguracionCorreo configuracionCorreo;
        private GestorDeCorreo gestorCorreo;
        private bool correoConectado = false;

        public MAIN()
        {
            InitializeComponent();

            // Configura el estado inicial del formulario
            ConfigurarEstadoInicial();

            // Cargar configuración MQTT automáticamente
            CargarConfiguracionMQTT();

            // Cargar configuración Modbus automáticamente
            CargarConfiguracionModbus();

            // Inicializar DataGridView de topics MQTT
            InicializarDataGridViewTopics();

            // Inicializar gestor de UI MQTT
            InicializarMqttUIManager();

            // Inicializar botón de refrescar MQTT
            InicializarBotonRefrescarMQTT();

            // Inicializar sistema sinóptico
            InicializarSistemaSinoptico();

            // Inicializar controles de visualización sinóptico
            InicializarVisualizacionSinoptico();

            // Inicializar configuración Modbus
            InicializarModbus();

            // Cargar configuración de correo automáticamente
            CargarConfiguracionCorreo();

            // Inicializar DataGridView de estados horarios
            InicializarDataGridViewEstadosHorarios();

            // Inicializar eventos de correo
            InicializarEventosCorreo();

            // Inicializar sistema de reconexión automática
            InicializarSistemaReconexion();
        }

        private ConexionMQTT? mqtt;
        private bool MQTTConectado = false;

        // EasyModbusTCP.NET client
        private ModbusClient? modbusClient;
        private bool modbusConectado = false;

        // Sistema de reconexión automática
        private ConfiguracionMQTT? ultimaConfigMQTT;
        private ConfiguracionModbus? ultimaConfigModbus;
        private DateTime ultimoIntentoReconexionMQTT = DateTime.MinValue;
        private DateTime ultimoIntentoReconexionModbus = DateTime.MinValue;
        private bool reconexionMQTTHabilitada = true;
        private bool reconexionModbusHabilitada = true;
        private int contadorReconexionesMQTT = 0;
        private int contadorReconexionesModbus = 0;

        // Los controles dgvMqttTopics y btnRefrescarMQTT están declarados en el Designer

        // Gestor de UI MQTT para separar lógica de negocio
        private MqttUIManager? mqttUIManager;

        // Sistema de monitorización bidireccional para sinóptico
        private readonly Dictionary<string, string> topicControlFeedback = new Dictionary<string, string>
        {
            { "DD/ENAGAS/ALMENDRALEJO/CONSIGNAPA", "DD/ENAGAS/ALMENDRALEJO/FEEDBACKPA" },
            { "DD/ENAGAS/ALMENDRALEJO/MOTIVOPA", "DD/ENAGAS/ALMENDRALEJO/FEEDBACKMOTIVOPA" }
        };

        private readonly Dictionary<string, string> ultimosValoresControl = new Dictionary<string, string>();
        private bool sinopticoHabilitado = false;

        // Los controles del sinóptico ahora están en el Designer
        // No necesitamos declaraciones privadas adicionales




        // GESTIÓN DE LA NAVEGACIÓN Y DISEÑO ***************************************************************

        /// <summary>
        /// Método central para cambiar de pestaña y actualizar el estilo de los botones.
        /// </summary>
        /// <param name="paginaDestino">La TabPage que se va a mostrar.</param>
        /// <param name="botonActivo">El botón de navegación que se va a resaltar.</param>
        /// 
        private void ConfigurarEstadoInicial()
        {
            // Establece la pestaña de Correo como la inicial
            NavegarHacia(tabPageCorreo, btnNavCorreo);
        }
        private void NavegarHacia(TabPage paginaDestino, Button botonActivo)
        {
            // Cambia la pestaña activa en el TabControl
            if (tabControlPrincipal.SelectedTab != paginaDestino)
            {
                tabControlPrincipal.SelectedTab = paginaDestino;
            }

            // Actualiza el estilo de los botones
            ResetearEstilosBotones();
            ResaltarBoton(botonActivo);
        }

        /// <summary>
        /// Pone todos los botones de navegación en su estado "inactivo".
        /// </summary>
        private void ResetearEstilosBotones()
        {
            // Resetear todos los botones a su estado inactivo
            btnNavCorreo.BackColor = Color.FromArgb(64, 64, 64);
            btnNavCorreo.ForeColor = Color.White;

            btnNavMqtt.BackColor = Color.FromArgb(64, 64, 64);
            btnNavMqtt.ForeColor = Color.White;

            btnNavModbus.BackColor = Color.FromArgb(64, 64, 64);
            btnNavModbus.ForeColor = Color.White;

            btnNavSinoptico.BackColor = Color.FromArgb(64, 64, 64);
            btnNavSinoptico.ForeColor = Color.White;
        }

        /// <summary>
        /// Resalta un botón de navegación para mostrar que está "activo".
        /// </summary>
        private void ResaltarBoton(Button boton)
        {
            // Resaltar el botón activo con color azul
            boton.BackColor = Color.FromArgb(0, 122, 204);
            boton.ForeColor = Color.White;
        }


        // --- EVENTOS CLICK DE LOS BOTONES DE NAVEGACIÓN ---

        private void btnNavCorreo_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageCorreo, btnNavCorreo);
        }

        private void btnNavMqtt_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageMqtt, btnNavMqtt);
        }

        private void btnNavModbus_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageModbus, btnNavModbus);
        }

        private void btnNavSinoptico_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageSinoptico, btnNavSinoptico);
        }
        private void btnCerrar_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        private void btnLimpiarLog_Click(object sender, EventArgs e)
        {
            rtbLog.Clear();
            rtbLog.AppendText("=== LOG DEL SISTEMA (limpiado) ===\n");
        }




        private async void btnMqttConectar_Click(object sender, EventArgs e)
        {
            try
            {
                // Validar puerto
                if (!int.TryParse(txtMqttPuerto.Text.Trim(), out int port))
                {
                    MessageBox.Show("El puerto MQTT debe ser un número válido.", "Puerto Inválido", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Puerto MQTT inválido\n");
                    return;
                }

                // Usar el MqttUIManager para la conexión
                if (mqttUIManager == null)
                {
                    MessageBox.Show("Error: MqttUIManager no inicializado", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                bool conectado = await mqttUIManager.ConectarAsync(
                    cmbMqttProtocolo.SelectedItem?.ToString() ?? "mqtts://",  // protocol
                    txtMqttHost.Text.Trim(),                                  // hostAddress
                    port,                                                     // port
                    txtMqttClientId.Text.Trim(),                             // clientId
                    txtMqttUsuario.Text.Trim(),                              // username
                    txtMqttPassword.Text,                                     // password (SIN Trim!)
                    chkMqttSslTls.Checked                                    // useTlsFromCheckbox
                );

                if (conectado)
                {
                    lblEstadoMqtt.Text = "MQTT: Conectado ✅";
                    lblEstadoMqtt.ForeColor = Color.LimeGreen;
                    btnMqttConectar.Enabled = false;
                    btnMqttDesconectar.Enabled = true;

                    // Habilitar botón de refrescar MQTT
                    if (btnRefrescarMQTT != null)
                        btnRefrescarMQTT.Enabled = true;

                    // Suscribir topics del CSV
                    SuscribirTopicsDelCSV();

                    // Suscribir topics del sinóptico
                    SuscribirTopicsSinoptico();

                    // Habilitar Lectura de Topics
                    MQTTConectado = true;

                    // Guardar configuración válida para reconexión automática
                    GuardarConfiguracionMQTTValida();

                    // Habilitar reconexión automática
                    reconexionMQTTHabilitada = true;
                    contadorReconexionesMQTT = 0; // Resetear contador

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Conectado a MQTT: {txtMqttHost.Text}:{port}\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 💡 Usa el botón 'Refrescar MQTT' para ver los topics disponibles\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 Reconexión automática MQTT habilitada\n");
                    rtbLog.ScrollToCaret();
                }
                else
                {
                    MessageBox.Show("No se pudo conectar al broker MQTT");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error conectando a MQTT\n");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error al conectar MQTT: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error conectando a MQTT: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }


        private async void SuscribirTopicsDelCSV()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 Iniciando suscripción de topics desde CSV...\n");

                if (!File.Exists("topics.csv"))
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ topics.csv no existe, creando archivo de ejemplo...\n");
                    File.WriteAllText("topics.csv", "enagas/temperatura\nenagas/presion\nenagas/estado\nenagas/caudal");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Archivo topics.csv creado\n");
                }

                string[] topics = File.ReadAllLines("topics.csv");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 Leyendo {topics.Length} líneas de topics.csv\n");

                int topicsSuscritos = 0;
                foreach (string topic in topics)
                {
                    if (!topic.StartsWith("#") && !string.IsNullOrEmpty(topic.Trim()))
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📡 Suscribiendo a: {topic.Trim()}\n");
                        await mqtt.SuscribirTopic(topic.Trim());
                        topicsSuscritos++;
                    }
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Suscripción completada: {topicsSuscritos} topics\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error en suscripción de topics: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            // Timer ya no actualiza automáticamente el DataGridView
            // Solo se actualiza manualmente con el botón btnRefrescarMQTT

            // Procesar cambios en topics de control del sinóptico
            ProcesarCambiosTopicsControl();

            // Actualizar visualización del sinóptico
            ActualizarVisualizacionSinoptico();

            // Sistema de reconexión automática
            VerificarYReconectarServicios();
        }

        private void btnMqttDesconectar_Click(object sender, EventArgs e)
        {
            // Usar el MqttUIManager para la desconexión
            mqttUIManager?.Desconectar();

            lblEstadoMqtt.Text = "MQTT: Desconectado ❌";
            lblEstadoMqtt.ForeColor = Color.Red;
            btnMqttConectar.Enabled = true;
            btnMqttDesconectar.Enabled = false;
            MQTTConectado = false;

            // Deshabilitar reconexión automática temporalmente (se rehabilita al conectar manualmente)
            reconexionMQTTHabilitada = false;

            // Deshabilitar sistema sinóptico
            DeshabilitarSistemaSinoptico();

            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔌 MQTT desconectado\n");
            rtbLog.ScrollToCaret();
        }

        /// <summary>
        /// Carga la configuración MQTT desde el archivo JSON y la aplica al formulario
        /// </summary>
        private void CargarConfiguracionMQTT()
        {
            try
            {
                var config = ConfiguracionMQTT.CargarDesdeArchivo();

                // Aplicar configuración a los controles del formulario
                cmbMqttProtocolo.SelectedItem = config.Protocolo;
                txtMqttHost.Text = config.Host;
                txtMqttPuerto.Text = config.Puerto.ToString();
                txtMqttClientId.Text = config.ClientId;
                txtMqttUsuario.Text = config.Usuario;
                txtMqttPassword.Text = config.Password;
                chkMqttSslTls.Checked = config.UsarSslTls;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚙️ Configuración MQTT cargada: {config.Name}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🌐 Servidor: {config.Host}:{config.Puerto}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔒 SSL/TLS: {(config.UsarSslTls ? "Activado" : "Desactivado")}\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error cargando configuración: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }




        /// <summary>
        /// Inicializa el DataGridView para mostrar los topics MQTT (SOLO verifica, NO modifica Designer)
        /// </summary>
        private void InicializarDataGridViewTopics()
        {
            try
            {
                // Verificar que el control existe (debe ser agregado desde el diseñador)
                if (dgvMqttTopics == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CRITICAL: DataGridView dgvMqttTopics not found - must be added in Designer\n");
                    return;
                }

                // SOLO VERIFICAR - NO MODIFICAR NADA DEL DESIGNER
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 DataGridView found - Columns: {dgvMqttTopics.Columns.Count}\n");

                // Listar columnas configuradas en el Designer
                for (int i = 0; i < dgvMqttTopics.Columns.Count; i++)
                {
                    var col = dgvMqttTopics.Columns[i];
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 Column {i}: Name='{col.Name}', DataPropertyName='{col.DataPropertyName}'\n");
                }

                if (dgvMqttTopics.Columns.Count == 0)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ WARNING: No columns configured in Designer\n");
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ DataGridView configured in Designer with {dgvMqttTopics.Columns.Count} columns\n");
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error checking DataGridView: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Inicializa el gestor de UI MQTT para separar lógica de negocio
        /// </summary>
        private void InicializarMqttUIManager()
        {
            try
            {
                if (dgvMqttTopics == null || btnRefrescarMQTT == null || rtbLog == null)
                {
                    rtbLog?.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error: Controles requeridos no encontrados para MqttUIManager\n");
                    return;
                }

                mqttUIManager = new MqttUIManager(this, dgvMqttTopics, btnRefrescarMQTT, rtbLog);
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MqttUIManager inicializado correctamente\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog?.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error inicializando MqttUIManager: {ex.Message}\n");
                rtbLog?.ScrollToCaret();
            }
        }

        /// <summary>
        /// Inicializa el botón de refrescar MQTT topics
        /// </summary>
        private void InicializarBotonRefrescarMQTT()
        {
            try
            {
                if (btnRefrescarMQTT == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ Button btnRefrescarMQTT not found - must be added in Designer\n");
                    return;
                }

                // Configurar el botón (el evento Click ya está configurado en el Designer)
                btnRefrescarMQTT.Enabled = false; // Inicialmente deshabilitado

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Refresh MQTT button configured\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error configuring refresh button: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Evento del botón de refrescar MQTT topics
        /// </summary>
        private void btnRefrescarMQTT_Click(object? sender, EventArgs e)
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 Manual MQTT refresh requested\n");
                mqttUIManager?.ActualizarListaTopics(verbose: true);
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Manual MQTT refresh completed\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error in manual MQTT refresh: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza la lista de topics en el DataGridView usando DataTable para compatibilidad con Designer
        /// </summary>
        private void ActualizarListaTopics(bool verbose = false)
        {
            try
            {
                // Step 1: Verify DataGridView exists
                if (dgvMqttTopics == null)
                {
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ DEBUG: dgvMqttTopics is null - check Designer\n");
                    return;
                }

                // Step 2: Verify MQTT connection
                if (mqtt == null)
                {
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ DEBUG: mqtt object is null\n");
                    return;
                }

                if (!mqtt.EstaConectado)
                {
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ DEBUG: MQTT not connected\n");
                    return;
                }

                // Step 3: Create DataTable compatible with Designer columns
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("Topic", typeof(string));
                dataTable.Columns.Add("Valor", typeof(string));
                dataTable.Columns.Add("Timestamp", typeof(string));
                dataTable.Columns.Add("Estado", typeof(string));

                // Step 4: Get topics from MQTT storage
                var topicsData = mqtt.ObtenerTodosLosTopics();
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 DEBUG: MQTT storage has {topicsData.Count} topics with values\n");

                // Add topics with values
                foreach (var topic in topicsData)
                {
                    dataTable.Rows.Add(
                        topic.Key,
                        topic.Value ?? "Sin datos",
                        DateTime.Now.ToString("HH:mm:ss"),
                        string.IsNullOrEmpty(topic.Value) ? "⏳ Esperando" : "✅ Activo"
                    );
                }

                // Step 5: Also add subscribed topics from CSV that might not have values yet
                if (File.Exists("topics.csv"))
                {
                    string[] csvTopics = File.ReadAllLines("topics.csv");
                    foreach (string csvTopic in csvTopics)
                    {
                        if (!csvTopic.StartsWith("#") && !string.IsNullOrEmpty(csvTopic.Trim()))
                        {
                            string topicName = csvTopic.Trim();
                            // Only add if not already in the list
                            if (!topicsData.ContainsKey(topicName))
                            {
                                dataTable.Rows.Add(
                                    topicName,
                                    "Sin datos",
                                    DateTime.Now.ToString("HH:mm:ss"),
                                    "⏳ Esperando"
                                );
                            }
                        }
                    }
                }

                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 DEBUG: Total topics to display: {dataTable.Rows.Count}\n");

                // Step 6: Update DataGridView using DataTable
                if (InvokeRequired)
                {
                    Invoke(new Action(() => BindDataTableToGrid(dataTable, verbose)));
                }
                else
                {
                    BindDataTableToGrid(dataTable, verbose);
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ ERROR in ActualizarListaTopics: {ex.Message}\n");
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Stack trace: {ex.StackTrace}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Binds DataTable to DataGridView (compatible with Designer columns)
        /// </summary>
        private void BindDataTableToGrid(DataTable dataTable, bool verbose = false)
        {
            try
            {
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 DEBUG: Starting DataTable binding...\n");

                // Clear current data source
                dgvMqttTopics.DataSource = null;

                // Bind DataTable
                dgvMqttTopics.DataSource = dataTable;

                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ DEBUG: DataTable binding completed\n");
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 DEBUG: DataGridView now has {dgvMqttTopics.Rows.Count} rows\n");

                // Force refresh
                dgvMqttTopics.Refresh();
                dgvMqttTopics.Invalidate();

                if (verbose) rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ ERROR in BindDataTableToGrid: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }



        /// <summary>
        /// TEST METHOD: Load sample topics to verify DataGridView configuration
        /// Call this method to test if DataGridView can display data
        /// </summary>
        public void TestDataGridViewWithSampleData()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Starting DataGridView test with sample data\n");

                if (dgvMqttTopics == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST FAILED: dgvMqttTopics is null\n");
                    return;
                }

                // Create sample data matching the expected structure
                var sampleData = new List<object>
                {
                    new { Topic = "test/topic1", Valor = "Sample Value 1", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "✅ Active" },
                    new { Topic = "test/topic2", Valor = "Sample Value 2", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "✅ Active" },
                    new { Topic = "test/topic3", Valor = "No data", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "⏳ Waiting" }
                };

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Binding {sampleData.Count} sample records\n");

                dgvMqttTopics.DataSource = null;
                dgvMqttTopics.DataSource = sampleData;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: DataGridView now shows {dgvMqttTopics.Rows.Count} rows\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ TEST COMPLETED: Check if data appears in DataGridView\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST FAILED: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// TEST METHOD: Check MQTT topics storage
        /// Call this method to verify if MQTT topics are being received and stored
        /// </summary>
        public void TestMqttTopicsStorage()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Checking MQTT topics storage\n");

                if (mqtt == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST: MQTT object is null\n");
                    return;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: MQTT Connected: {mqtt.EstaConectado}\n");

                var topics = mqtt.ObtenerTodosLosTopics();
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Total topics in storage: {topics.Count}\n");

                if (topics.Count == 0)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ TEST: No topics found - check MQTT subscription\n");
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 TEST: Topics in storage:\n");
                    foreach (var topic in topics)
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋   - '{topic.Key}' = '{topic.Value}'\n");
                    }
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST FAILED: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        #region Sistema Sinóptico MQTT Bidireccional

        /// <summary>
        /// Inicializa el sistema de monitorización bidireccional del sinóptico
        /// </summary>
        private void InicializarSistemaSinoptico()
        {
            try
            {
                // Inicializar diccionario de últimos valores
                foreach (var topicControl in topicControlFeedback.Keys)
                {
                    ultimosValoresControl[topicControl] = string.Empty;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Sistema sinóptico inicializado\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error inicializando sistema sinóptico: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Suscribe a todos los topics del sinóptico (control y feedback)
        /// </summary>
        private async void SuscribirTopicsSinoptico()
        {
            try
            {
                if (mqtt == null || !mqtt.EstaConectado)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SINÓPTICO: MQTT no conectado\n");
                    return;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 SINÓPTICO: Suscribiendo a topics...\n");

                // Suscribir a topics de control
                foreach (var topicControl in topicControlFeedback.Keys)
                {
                    await mqtt.SuscribirTopic(topicControl);
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📡 SINÓPTICO: Suscrito a control '{topicControl}'\n");
                }

                // Suscribir a topics de feedback
                foreach (var topicFeedback in topicControlFeedback.Values)
                {
                    await mqtt.SuscribirTopic(topicFeedback);
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📡 SINÓPTICO: Suscrito a feedback '{topicFeedback}'\n");
                }

                sinopticoHabilitado = true;
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Sistema habilitado y suscripciones completadas\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error en suscripciones: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Deshabilita el sistema sinóptico
        /// </summary>
        private void DeshabilitarSistemaSinoptico()
        {
            try
            {
                sinopticoHabilitado = false;
                ultimosValoresControl.Clear();

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SINÓPTICO: Sistema deshabilitado\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error deshabilitando: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }



        /// <summary>
        /// Inicializa los controles de visualización del sinóptico usando controles del DESIGNER
        /// </summary>
        private void InicializarVisualizacionSinoptico()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Inicializando controles del Designer\n");

                // Los controles ya están creados en el Designer, solo configuramos las columnas
                dgvSinopticoTopics.AutoGenerateColumns = false;

                // Crear columnas para el DataGridView
                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Tipo",
                    HeaderText = "Tipo",
                    DataPropertyName = "Tipo",
                    Width = 100
                });

                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Topic",
                    HeaderText = "Topic",
                    DataPropertyName = "Topic",
                    Width = 200
                });

                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Valor",
                    HeaderText = "Valor",
                    DataPropertyName = "Valor",
                    Width = 120
                });

                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Timestamp",
                    HeaderText = "Timestamp",
                    DataPropertyName = "Timestamp",
                    Width = 100
                });

                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Estado",
                    HeaderText = "Estado",
                    DataPropertyName = "Estado",
                    Width = 120
                });

                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Sincronizado",
                    HeaderText = "Sincronizado",
                    DataPropertyName = "Sincronizado",
                    Width = 120
                });

                // Los controles ya están agregados en el Designer
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Controles del Designer configurados correctamente\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 SINÓPTICO: DataGridView configurado con {dgvSinopticoTopics.Columns.Count} columnas\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error creando visualización sinóptico: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza la visualización del estado del sinóptico
        /// </summary>
        private void ActualizarVisualizacionSinoptico()
        {
            try
            {
                if (!sinopticoHabilitado || mqtt == null || !mqtt.EstaConectado)
                {
                    // Sistema deshabilitado
                    if (lblSinopticoEstado != null)
                        lblSinopticoEstado.Text = "🔴 Sistema Sinóptico: DESHABILITADO";

                    if (dgvSinopticoTopics != null)
                        dgvSinopticoTopics.DataSource = null;

                    return;
                }

                // Sistema habilitado - actualizar estado
                if (lblSinopticoEstado != null)
                    lblSinopticoEstado.Text = "🟢 Sistema Sinóptico: ACTIVO";

                if (lblSinopticoUltimaActualizacion != null)
                    lblSinopticoUltimaActualizacion.Text = $"Última Actualización: {DateTime.Now:HH:mm:ss}";

                // Actualizar DataGridView con estado de topics
                ActualizarDataGridViewSinoptico();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error actualizando visualización: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza el DataGridView con el estado actual de los topics del sinóptico
        /// </summary>
        private void ActualizarDataGridViewSinoptico()
        {
            try
            {
                if (dgvSinopticoTopics == null) return;

                // Crear DataTable para el estado del sinóptico
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("Tipo", typeof(string));
                dataTable.Columns.Add("Topic", typeof(string));
                dataTable.Columns.Add("Valor", typeof(string));
                dataTable.Columns.Add("Timestamp", typeof(string));
                dataTable.Columns.Add("Estado", typeof(string));
                dataTable.Columns.Add("Sincronizado", typeof(string));

                // Agregar topics de control
                foreach (var par in topicControlFeedback)
                {
                    string topicControl = par.Key;
                    string topicFeedback = par.Value;

                    string? valorControl = mqtt.LeerTopic(topicControl);
                    string? valorFeedback = mqtt.LeerTopic(topicFeedback);

                    // Determinar estado de sincronización
                    string sincronizado = "❓ N/A";
                    if (valorControl != null && valorFeedback != null)
                    {
                        sincronizado = valorControl == valorFeedback ? "✅ SÍ" : "❌ NO";
                    }
                    else if (valorControl != null && valorFeedback == null)
                    {
                        sincronizado = "⏳ Pendiente";
                    }

                    // Agregar fila de control
                    dataTable.Rows.Add(
                        "🎛️ Control",
                        topicControl.Replace("DD/ENAGAS/ALMENDRALEJO/", ""),
                        valorControl ?? "Sin datos",
                        DateTime.Now.ToString("HH:mm:ss"),
                        valorControl != null ? "✅ Activo" : "⏳ Esperando",
                        sincronizado
                    );

                    // Agregar fila de feedback
                    dataTable.Rows.Add(
                        "📤 Feedback",
                        topicFeedback.Replace("DD/ENAGAS/ALMENDRALEJO/", ""),
                        valorFeedback ?? "Sin datos",
                        DateTime.Now.ToString("HH:mm:ss"),
                        valorFeedback != null ? "✅ Activo" : "⏳ Esperando",
                        sincronizado
                    );
                }

                // Actualizar DataGridView
                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        dgvSinopticoTopics.DataSource = null;
                        dgvSinopticoTopics.DataSource = dataTable;
                        dgvSinopticoTopics.Refresh();
                    }));
                }
                else
                {
                    dgvSinopticoTopics.DataSource = null;
                    dgvSinopticoTopics.DataSource = dataTable;
                    dgvSinopticoTopics.Refresh();
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error actualizando DataGridView sinóptico: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Procesa cambios en topics de control y publica feedback automáticamente
        /// </summary>
        private void ProcesarCambiosTopicsControl()
        {
            try
            {
                if (!sinopticoHabilitado || mqtt == null || !mqtt.EstaConectado)
                    return;

                // Verificar cambios en cada topic de control
                foreach (var par in topicControlFeedback)
                {
                    string topicControl = par.Key;
                    string topicFeedback = par.Value;

                    // Obtener valor actual del topic de control
                    string? valorActual = mqtt.LeerTopic(topicControl);

                    if (valorActual != null)
                    {
                        // Verificar si el valor ha cambiado
                        if (!ultimosValoresControl.ContainsKey(topicControl) ||
                            ultimosValoresControl[topicControl] != valorActual)
                        {
                            // Valor ha cambiado, registrar y publicar feedback
                            string valorAnterior = ultimosValoresControl.ContainsKey(topicControl) ?
                                                 ultimosValoresControl[topicControl] : "N/A";

                            ultimosValoresControl[topicControl] = valorActual;

                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 SINÓPTICO: Cambio detectado en '{topicControl}'\n");
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 SINÓPTICO: Valor anterior: '{valorAnterior}' → Nuevo: '{valorActual}'\n");

                            // Publicar feedback automáticamente
                            PublicarFeedback(topicControl, topicFeedback, valorActual);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error procesando cambios: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Publica un valor en el topic de feedback correspondiente
        /// </summary>
        private async void PublicarFeedback(string topicControl, string topicFeedback, string valor)
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📤 SINÓPTICO: Publicando feedback...\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📤 SINÓPTICO: Control: '{topicControl}' → Feedback: '{topicFeedback}'\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📤 SINÓPTICO: Valor: '{valor}'\n");

                // Publicar en el topic de feedback
                bool publicado = await mqtt.EscribirTopic(topicFeedback, valor);

                if (publicado)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Feedback publicado exitosamente\n");

                    // Programar verificación de confirmación
                    Task.Delay(1000).ContinueWith(_ => VerificarConfirmacionFeedback(topicFeedback, valor));
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error publicando feedback\n");
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error en publicación: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Verifica que el feedback fue recibido correctamente
        /// </summary>
        private void VerificarConfirmacionFeedback(string topicFeedback, string valorEsperado)
        {
            try
            {
                if (!sinopticoHabilitado || mqtt == null || !mqtt.EstaConectado)
                    return;

                string? valorRecibido = mqtt.LeerTopic(topicFeedback);

                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        if (valorRecibido == valorEsperado)
                        {
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Confirmación exitosa en '{topicFeedback}'\n");
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Valor confirmado: '{valorRecibido}'\n");
                        }
                        else
                        {
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SINÓPTICO: Confirmación pendiente en '{topicFeedback}'\n");
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SINÓPTICO: Esperado: '{valorEsperado}', Recibido: '{valorRecibido ?? "null"}'\n");
                        }
                        rtbLog.ScrollToCaret();
                    }));
                }
            }
            catch (Exception ex)
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error verificando confirmación: {ex.Message}\n");
                        rtbLog.ScrollToCaret();
                    }));
                }
            }
        }

        private void panelLog_Paint(object sender, PaintEventArgs e)
        {

        }

        #endregion

        #region Modbus con EasyModbusTCP.NET

        /// <summary>
        /// Inicializa la configuración Modbus con valores por defecto
        /// </summary>
        private void InicializarModbus()
        {
            try
            {
                // Configurar valores por defecto según las preferencias del usuario
                txtModbusIp.Text = "*************"; // IP por defecto para ENAGAS Huelva
                nudModbusPuerto.Value = 502; // Puerto estándar Modbus TCP
                nudModbusDeviceId.Value = 1; // Device ID por defecto

                // Configurar estado inicial de botones
                btnModbusConectar.Enabled = true;
                btnModbusDesconectar.Enabled = false;
                btnModbusLeerRegistros.Enabled = false;
                btnModbusEscribirRegistro.Enabled = false;

                // Estado inicial
                lblEstadoModbus.Text = "🔴 Modbus: Desconectado";
                lblEstadoModbus.ForeColor = Color.Red;
                lblModbusValorLeido.Text = "Valor: --";
                lblModbusValorLeido.ForeColor = Color.Gray;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Configuración inicial cargada\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔧 MODBUS: IP={txtModbusIp.Text}, Puerto={nudModbusPuerto.Value}, Device ID={nudModbusDeviceId.Value}\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error inicializando Modbus: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Evento del botón Conectar Modbus
        /// </summary>
        private async void btnModbusConectar_Click(object sender, EventArgs e)
        {
            try
            {
                // Validar datos de entrada
                if (string.IsNullOrWhiteSpace(txtModbusIp.Text))
                {
                    MessageBox.Show("❌ Ingrese una dirección IP válida", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Actualizar indicador de estado
                lblEstadoModbus.Text = "🔄 Conectando...";
                lblEstadoModbus.ForeColor = Color.Orange;
                btnModbusConectar.Enabled = false;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MODBUS: Iniciando conexión a {txtModbusIp.Text}:{nudModbusPuerto.Value}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔧 MODBUS: Device ID {nudModbusDeviceId.Value}, Timeout: 5000ms\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📚 MODBUS: Usando EasyModbusTCP.NET v5.6.0\n");
                rtbLog.ScrollToCaret();

                // Crear cliente Modbus TCP
                modbusClient = new ModbusClient(txtModbusIp.Text.Trim(), (int)nudModbusPuerto.Value);
                modbusClient.UnitIdentifier = (byte)nudModbusDeviceId.Value;
                modbusClient.ConnectionTimeout = 5000; // 5 segundos timeout

                // Conectar de forma asíncrona con timeout
                var connectTask = Task.Run(() => modbusClient.Connect());
                var timeoutTask = Task.Delay(8000); // 8 segundos timeout total

                var completedTask = await Task.WhenAny(connectTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    throw new TimeoutException("Timeout de conexión (8 segundos)");
                }

                await connectTask; // Esperar a que termine la conexión

                if (modbusClient.Connected)
                {
                    modbusConectado = true;
                    lblEstadoModbus.Text = "🟢 Modbus: Conectado";
                    lblEstadoModbus.ForeColor = Color.LimeGreen;

                    btnModbusConectar.Enabled = false;
                    btnModbusDesconectar.Enabled = true;
                    btnModbusLeerRegistros.Enabled = true;
                    btnModbusEscribirRegistro.Enabled = true;

                    // Guardar configuración válida para reconexión automática
                    GuardarConfiguracionModbusValida();

                    // Habilitar reconexión automática
                    reconexionModbusHabilitada = true;
                    contadorReconexionesModbus = 0; // Resetear contador

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Conectado exitosamente a {txtModbusIp.Text}:{nudModbusPuerto.Value}\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🎉 MODBUS: Listo para operaciones de lectura/escritura\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 Reconexión automática MODBUS habilitada\n");

                    // Mostrar mensaje de éxito
                    MessageBox.Show(
                        $"✅ CONEXIÓN MODBUS EXITOSA\n\n" +
                        $"🔌 IP: {txtModbusIp.Text}\n" +
                        $"🔌 Puerto: {nudModbusPuerto.Value}\n" +
                        $"🔧 Device ID: {nudModbusDeviceId.Value}\n" +
                        $"📚 Librería: EasyModbusTCP.NET v5.6.0\n\n" +
                        $"🎯 Ahora puede realizar operaciones de lectura y escritura",
                        "🎉 CONEXIÓN EXITOSA",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information
                    );
                }
                else
                {
                    throw new Exception("No se pudo establecer la conexión");
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                modbusConectado = false;
                lblEstadoModbus.Text = "🔴 Modbus: Error";
                lblEstadoModbus.ForeColor = Color.Red;
                btnModbusConectar.Enabled = true;
                btnModbusDesconectar.Enabled = false;
                btnModbusLeerRegistros.Enabled = false;
                btnModbusEscribirRegistro.Enabled = false;

                string errorMsg = ex.Message;
                if (ex is TimeoutException)
                {
                    errorMsg = "Timeout de conexión. Verifique que el PLC esté encendido y accesible en la red.";
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error conectando - {errorMsg}\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show(
                    $"❌ ERROR AL CONECTAR CON EL PLC\n\n" +
                    $"🔌 IP: {txtModbusIp.Text}\n" +
                    $"🔌 Puerto: {nudModbusPuerto.Value}\n" +
                    $"🔧 Device ID: {nudModbusDeviceId.Value}\n\n" +
                    $"💥 Error: {errorMsg}\n\n" +
                    $"🔧 Soluciones:\n" +
                    $"• Verificar que el PLC esté encendido\n" +
                    $"• Comprobar la dirección IP y puerto\n" +
                    $"• Verificar conectividad de red\n" +
                    $"• Revisar configuración del firewall",
                    "💥 ERROR DE CONEXIÓN",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// Evento del botón Desconectar Modbus
        /// </summary>
        private void btnModbusDesconectar_Click(object sender, EventArgs e)
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MODBUS: Desconectando...\n");
                rtbLog.ScrollToCaret();

                if (modbusClient != null && modbusClient.Connected)
                {
                    modbusClient.Disconnect();
                }

                // Limpiar referencia
                modbusClient = null;
                modbusConectado = false;

                // Deshabilitar reconexión automática temporalmente (se rehabilita al conectar manualmente)
                reconexionModbusHabilitada = false;

                // Actualizar UI
                lblEstadoModbus.Text = "🔴 Modbus: Desconectado";
                lblEstadoModbus.ForeColor = Color.Red;

                btnModbusConectar.Enabled = true;
                btnModbusDesconectar.Enabled = false;
                btnModbusLeerRegistros.Enabled = false;
                btnModbusEscribirRegistro.Enabled = false;

                // Limpiar valor mostrado
                lblModbusValorLeido.Text = "Valor: --";
                lblModbusValorLeido.ForeColor = Color.Gray;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Desconectado correctamente\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show(
                    "✅ DESCONEXIÓN EXITOSA\n\n" +
                    "El cliente Modbus se ha desconectado correctamente.",
                    "🔌 DESCONECTADO",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error desconectando - {ex.Message}\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show(
                    $"⚠️ ERROR AL DESCONECTAR\n\n{ex.Message}",
                    "Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
            }
        }

        /// <summary>
        /// Evento del botón Leer Registros Modbus
        /// </summary>
        private async void btnModbusLeerRegistros_Click(object sender, EventArgs e)
        {
            try
            {
                if (modbusClient == null || !modbusClient.Connected)
                {
                    MessageBox.Show("❌ No hay conexión Modbus activa", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtModbusDireccionRegistro.Text))
                {
                    MessageBox.Show("❌ Ingrese la dirección del registro", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Convertir dirección (soporta MW430 o 40431)
                int direccion = ConvertirDireccionModbus(txtModbusDireccionRegistro.Text.Trim());
                if (direccion == -1)
                {
                    MessageBox.Show("❌ Dirección de registro inválida\n\nEjemplos válidos:\n- MW430 (se convierte a 430)\n- 430 (dirección directa)\n- 40431 (formato Modbus)",
                        "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 MODBUS: Leyendo registro {direccion}...\n");
                rtbLog.ScrollToCaret();

                // Intentar leer como Holding Register primero
                int[] valores = null;
                bool exitoso = false;
                string tipoRegistro = "";
                Exception ultimoError = null;

                try
                {
                    // Timeout para la operación de lectura
                    var readTask = Task.Run(() => modbusClient.ReadHoldingRegisters(direccion, 1));
                    var timeoutTask = Task.Delay(5000); // 5 segundos timeout

                    var completedTask = await Task.WhenAny(readTask, timeoutTask);

                    if (completedTask == timeoutTask)
                    {
                        throw new TimeoutException("Timeout leyendo Holding Register");
                    }

                    valores = await readTask;
                    exitoso = true;
                    tipoRegistro = "Holding Register";
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Éxito leyendo como Holding Register\n");
                }
                catch (Exception exHolding)
                {
                    ultimoError = exHolding;
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error en Holding Register - {exHolding.Message}\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MODBUS: Intentando como Input Register...\n");
                    rtbLog.ScrollToCaret();

                    try
                    {
                        // Timeout para Input Register también
                        var readTask = Task.Run(() => modbusClient.ReadInputRegisters(direccion, 1));
                        var timeoutTask = Task.Delay(5000); // 5 segundos timeout

                        var completedTask = await Task.WhenAny(readTask, timeoutTask);

                        if (completedTask == timeoutTask)
                        {
                            throw new TimeoutException("Timeout leyendo Input Register");
                        }

                        valores = await readTask;
                        exitoso = true;
                        tipoRegistro = "Input Register";
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Éxito leyendo como Input Register\n");
                    }
                    catch (Exception exInput)
                    {
                        ultimoError = exInput;
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error en Input Register - {exInput.Message}\n");
                        throw new Exception($"No se pudo leer como Holding Register ni Input Register.\nHolding: {exHolding.Message}\nInput: {exInput.Message}");
                    }
                }

                if (exitoso && valores != null && valores.Length > 0)
                {
                    int valor = valores[0];

                    // Actualizar el label con el valor leído
                    lblModbusValorLeido.Text = $"Valor: {valor}";
                    lblModbusValorLeido.ForeColor = Color.LimeGreen;

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 MODBUS: {tipoRegistro} {direccion} = {valor} (0x{valor:X4})\n");

                    // Mostrar MessageBox con el valor leído (según preferencias del usuario)
                    MessageBox.Show(
                        $"✅ VALOR LEÍDO EXITOSAMENTE\n\n" +
                        $"📍 Dirección: {direccion}\n" +
                        $"📊 Tipo: {tipoRegistro}\n" +
                        $"🔢 Valor Decimal: {valor}\n" +
                        $"🔢 Valor Hexadecimal: 0x{valor:X4}\n" +
                        $"🔢 Valor Binario: {Convert.ToString(valor, 2).PadLeft(16, '0')}\n\n" +
                        $"🔌 PLC: Conectado\n" +
                        $"📚 Librería: EasyModbusTCP.NET v5.6.0",
                        "🎉 LECTURA MODBUS EXITOSA",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information
                    );
                }
                else
                {
                    lblModbusValorLeido.Text = "Valor: Sin datos";
                    lblModbusValorLeido.ForeColor = Color.Orange;
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ MODBUS: No se recibieron datos válidos\n");

                    MessageBox.Show(
                        "❌ NO SE RECIBIERON DATOS VÁLIDOS\n\n" +
                        "Posibles causas:\n" +
                        "• Dirección inexistente en el PLC\n" +
                        "• PLC no responde\n" +
                        "• Configuración incorrecta\n" +
                        "• Problema de comunicación",
                        "⚠️ ERROR DE LECTURA MODBUS",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                lblModbusValorLeido.Text = "Valor: ERROR";
                lblModbusValorLeido.ForeColor = Color.Red;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error leyendo registro - {ex.Message}\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show(
                    $"❌ ERROR AL LEER REGISTRO MODBUS\n\n" +
                    $"📍 Dirección solicitada: {txtModbusDireccionRegistro.Text}\n" +
                    $"🔌 Estado conexión: {(modbusClient?.Connected == true ? "Conectado" : "Desconectado")}\n\n" +
                    $"💥 Error detallado:\n{ex.Message}\n\n" +
                    $"🔧 Soluciones:\n" +
                    $"• Verificar que el PLC esté conectado\n" +
                    $"• Comprobar la dirección del registro\n" +
                    $"• Verificar Device ID (actual: {nudModbusDeviceId.Value})\n" +
                    $"• Revisar configuración de red",
                    "💥 ERROR DE COMUNICACIÓN MODBUS",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// Evento del botón Escribir Registro Modbus
        /// </summary>
        private async void btnModbusEscribirRegistro_Click(object sender, EventArgs e)
        {
            try
            {
                if (modbusClient == null || !modbusClient.Connected)
                {
                    MessageBox.Show("❌ No hay conexión Modbus activa", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtModbusDireccionRegistro.Text))
                {
                    MessageBox.Show("❌ Ingrese la dirección del registro", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtModbusValorEscritura.Text))
                {
                    MessageBox.Show("❌ Ingrese el valor a escribir", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Convertir dirección
                int direccion = ConvertirDireccionModbus(txtModbusDireccionRegistro.Text.Trim());
                if (direccion == -1)
                {
                    MessageBox.Show("❌ Dirección de registro inválida\n\nEjemplos válidos:\n- MW430 (se convierte a 430)\n- 430 (dirección directa)\n- 40431 (formato Modbus)",
                        "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!int.TryParse(txtModbusValorEscritura.Text, out int valor) || valor < 0 || valor > 65535)
                {
                    MessageBox.Show("❌ Valor de escritura inválido (debe ser entre 0 y 65535)", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✏️ MODBUS: Escribiendo registro {direccion} = {valor}...\n");
                rtbLog.ScrollToCaret();

                // Escribir registro con timeout
                var writeTask = Task.Run(() => modbusClient.WriteSingleRegister(direccion, valor));
                var timeoutTask = Task.Delay(5000); // 5 segundos timeout

                var completedTask = await Task.WhenAny(writeTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    throw new TimeoutException("Timeout escribiendo registro");
                }

                await writeTask; // Esperar a que termine la escritura

                // Actualizar el label para mostrar que se escribió correctamente
                lblModbusValorLeido.Text = $"Escrito: {valor}";
                lblModbusValorLeido.ForeColor = Color.Cyan;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Registro {direccion} escrito exitosamente\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 💡 RECOMENDACIÓN: Leer el registro para verificar la escritura\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show(
                    $"✅ VALOR ESCRITO EXITOSAMENTE\n\n" +
                    $"📍 Dirección: {direccion}\n" +
                    $"🔢 Valor: {valor}\n" +
                    $"📚 Librería: EasyModbusTCP.NET v5.6.0\n\n" +
                    $"💡 Recomendación: Lea el registro para verificar que se escribió correctamente",
                    "🎉 ESCRITURA MODBUS EXITOSA",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
            }
            catch (Exception ex)
            {
                lblModbusValorLeido.Text = "Valor: ERROR";
                lblModbusValorLeido.ForeColor = Color.Red;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error escribiendo registro - {ex.Message}\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show(
                    $"❌ ERROR AL ESCRIBIR REGISTRO MODBUS\n\n" +
                    $"📍 Dirección: {txtModbusDireccionRegistro.Text}\n" +
                    $"🔢 Valor: {txtModbusValorEscritura.Text}\n\n" +
                    $"💥 Error detallado:\n{ex.Message}",
                    "💥 ERROR DE ESCRITURA MODBUS",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// Convierte direcciones MW de Schneider a direcciones Modbus
        /// </summary>
        /// <param name="direccionTexto">Dirección como texto (MW430, 430, o 40431)</param>
        /// <returns>Dirección Modbus válida o -1 si es inválida</returns>
        private int ConvertirDireccionModbus(string direccionTexto)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(direccionTexto))
                    return -1;

                direccionTexto = direccionTexto.ToUpper().Trim();

                // Si es formato MW (Memory Word de Schneider)
                if (direccionTexto.StartsWith("MW"))
                {
                    string numeroStr = direccionTexto.Substring(2);
                    if (int.TryParse(numeroStr, out int numeroMW))
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MODBUS: Convertido {direccionTexto} → dirección {numeroMW}\n");
                        return numeroMW;
                    }
                }
                // Si es dirección Modbus con formato 40xxx (convertir a base 0)
                else if (int.TryParse(direccionTexto, out int direccionDirecta))
                {
                    if (direccionDirecta >= 40001 && direccionDirecta <= 49999)
                    {
                        int direccionConvertida = direccionDirecta - 40001;
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MODBUS: Convertido {direccionTexto} → dirección {direccionConvertida}\n");
                        return direccionConvertida;
                    }
                    else if (direccionDirecta >= 0 && direccionDirecta <= 9999)
                    {
                        // Dirección directa (ya en base 0)
                        return direccionDirecta;
                    }
                }

                return -1; // Dirección inválida
            }
            catch
            {
                return -1;
            }
        }

        /// <summary>
        /// Limpia recursos al cerrar la aplicación
        /// </summary>
        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            try
            {
                // Desconectar Modbus si está conectado
                if (modbusClient != null && modbusClient.Connected)
                {
                    modbusClient.Disconnect();
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MODBUS: Desconectado al cerrar aplicación\n");
                }

                // Desconectar MQTT si está conectado
                if (mqtt != null)
                {
                    mqtt.Desconectar();
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MQTT: Desconectado al cerrar aplicación\n");
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 👋 APLICACIÓN: Cerrando correctamente\n");
            }
            catch (Exception ex)
            {
                // Log del error pero no mostrar MessageBox al cerrar
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error al cerrar: {ex.Message}\n");
            }
            finally
            {
                base.OnFormClosed(e);
            }
        }

        #endregion

        #region Sistema de Reconexión Automática

        /// <summary>
        /// Inicializa el sistema de reconexión automática
        /// </summary>
        private void InicializarSistemaReconexion()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 RECONEXIÓN: Sistema de reconexión automática inicializado\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⏱️ RECONEXIÓN: Timer configurado cada {timer1.Interval}ms\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error inicializando sistema de reconexión: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Carga la configuración Modbus desde archivo
        /// </summary>
        private void CargarConfiguracionModbus()
        {
            try
            {
                ultimaConfigModbus = ConfiguracionModbus.CargarDesdeArchivo();

                // Aplicar configuración a los controles del formulario
                txtModbusIp.Text = ultimaConfigModbus.Ip;
                nudModbusPuerto.Value = ultimaConfigModbus.Puerto;
                nudModbusDeviceId.Value = ultimaConfigModbus.DeviceId;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Configuración cargada desde archivo\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 MODBUS: {ultimaConfigModbus}\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error cargando configuración Modbus: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Verifica el estado de las conexiones y reconecta si es necesario
        /// </summary>
        private void VerificarYReconectarServicios()
        {
            try
            {
                // Verificar y reconectar MQTT
                VerificarReconexionMQTT();

                // Verificar y reconectar Modbus
                VerificarReconexionModbus();
            }
            catch (Exception ex)
            {
                // No mostrar errores en el log para evitar spam, solo en casos críticos
                if (ex.Message.Contains("crítico"))
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error crítico en reconexión: {ex.Message}\n");
                    rtbLog.ScrollToCaret();
                }
            }
        }

        /// <summary>
        /// Verifica y reconecta MQTT si es necesario
        /// </summary>
        private void VerificarReconexionMQTT()
        {
            try
            {
                // Solo intentar reconexión si está habilitada y tenemos configuración válida
                if (!reconexionMQTTHabilitada || ultimaConfigMQTT == null || !ultimaConfigMQTT.EsValida())
                    return;

                // Verificar si MQTT está desconectado
                bool mqttDesconectado = mqtt == null || !MQTTConectado;

                if (mqttDesconectado)
                {
                    // Verificar si ha pasado suficiente tiempo desde el último intento
                    var tiempoTranscurrido = DateTime.Now - ultimoIntentoReconexionMQTT;
                    if (tiempoTranscurrido.TotalSeconds >= 30) // Intentar cada 30 segundos
                    {
                        ultimoIntentoReconexionMQTT = DateTime.Now;
                        contadorReconexionesMQTT++;

                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MQTT: Intento de reconexión #{contadorReconexionesMQTT}\n");
                        rtbLog.ScrollToCaret();

                        // Intentar reconexión en segundo plano
                        Task.Run(async () => await IntentarReconexionMQTT());
                    }
                }
            }
            catch (Exception ex)
            {
                // Error silencioso para evitar spam en el log
            }
        }

        /// <summary>
        /// Verifica y reconecta Modbus si es necesario
        /// </summary>
        private void VerificarReconexionModbus()
        {
            try
            {
                // Solo intentar reconexión si está habilitada y tenemos configuración válida
                if (!reconexionModbusHabilitada || ultimaConfigModbus == null || !ultimaConfigModbus.EsValida())
                    return;

                // Verificar si Modbus está desconectado
                bool modbusDesconectado = modbusClient == null || !modbusClient.Connected || !modbusConectado;

                if (modbusDesconectado)
                {
                    // Verificar si ha pasado suficiente tiempo desde el último intento
                    var tiempoTranscurrido = DateTime.Now - ultimoIntentoReconexionModbus;
                    if (tiempoTranscurrido.TotalSeconds >= ultimaConfigModbus.IntervaloReconexion)
                    {
                        ultimoIntentoReconexionModbus = DateTime.Now;
                        contadorReconexionesModbus++;

                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MODBUS: Intento de reconexión #{contadorReconexionesModbus}\n");
                        rtbLog.ScrollToCaret();

                        // Intentar reconexión en segundo plano
                        Task.Run(async () => await IntentarReconexionModbus());
                    }
                }
            }
            catch (Exception ex)
            {
                // Error silencioso para evitar spam en el log
            }
        }

        /// <summary>
        /// Guarda la configuración MQTT válida para reconexión automática
        /// </summary>
        private void GuardarConfiguracionMQTTValida()
        {
            try
            {
                if (!int.TryParse(txtMqttPuerto.Text.Trim(), out int port))
                    return;

                ultimaConfigMQTT = new ConfiguracionMQTT
                {
                    Protocolo = cmbMqttProtocolo.SelectedItem?.ToString() ?? "mqtts://",
                    Host = txtMqttHost.Text.Trim(),
                    Puerto = port,
                    ClientId = txtMqttClientId.Text.Trim(),
                    Usuario = txtMqttUsuario.Text.Trim(),
                    Password = txtMqttPassword.Text,
                    UsarSslTls = chkMqttSslTls.Checked,
                    CertificadoCA = true
                };

                // Guardar en archivo para persistencia
                ConfiguracionMQTT.GuardarEnArchivo(ultimaConfigMQTT);

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 💾 MQTT: Configuración válida guardada para reconexión automática\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error guardando configuración MQTT: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Guarda la configuración Modbus válida para reconexión automática
        /// </summary>
        private void GuardarConfiguracionModbusValida()
        {
            try
            {
                ultimaConfigModbus = new ConfiguracionModbus
                {
                    Ip = txtModbusIp.Text.Trim(),
                    Puerto = (int)nudModbusPuerto.Value,
                    DeviceId = (byte)nudModbusDeviceId.Value,
                    Timeout = 5000,
                    ReconexionAutomatica = true,
                    IntervaloReconexion = 10
                };

                // Guardar en archivo para persistencia
                ConfiguracionModbus.GuardarEnArchivo(ultimaConfigModbus);

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 💾 MODBUS: Configuración válida guardada para reconexión automática\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error guardando configuración Modbus: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Intenta reconectar MQTT de forma asíncrona
        /// </summary>
        private async Task IntentarReconexionMQTT()
        {
            try
            {
                if (ultimaConfigMQTT == null) return;

                // Crear nueva conexión MQTT
                var nuevoMqtt = new ConexionMQTT();

                bool conectado = await nuevoMqtt.ConectarAsync(
                    ultimaConfigMQTT.Protocolo,
                    ultimaConfigMQTT.Host,
                    ultimaConfigMQTT.Puerto,
                    ultimaConfigMQTT.ClientId,
                    ultimaConfigMQTT.Usuario,
                    ultimaConfigMQTT.Password,
                    ultimaConfigMQTT.UsarSslTls,
                    ultimaConfigMQTT.CertificadoCA,
                    false
                );

                if (conectado)
                {
                    // Actualizar UI en el hilo principal
                    this.Invoke(new Action(() =>
                    {
                        mqtt = nuevoMqtt;
                        MQTTConectado = true;
                        lblEstadoMqtt.Text = "MQTT: Reconectado ✅";
                        lblEstadoMqtt.ForeColor = Color.LimeGreen;
                        btnMqttConectar.Enabled = false;
                        btnMqttDesconectar.Enabled = true;

                        if (btnRefrescarMQTT != null)
                            btnRefrescarMQTT.Enabled = true;

                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MQTT: Reconectado exitosamente (intento #{contadorReconexionesMQTT})\n");
                        rtbLog.ScrollToCaret();

                        // Suscribir topics nuevamente
                        SuscribirTopicsDelCSV();
                        SuscribirTopicsSinoptico();
                    }));
                }
            }
            catch (Exception ex)
            {
                this.Invoke(new Action(() =>
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MQTT: Error en reconexión #{contadorReconexionesMQTT}: {ex.Message}\n");
                    rtbLog.ScrollToCaret();
                }));
            }
        }

        /// <summary>
        /// Intenta reconectar Modbus de forma asíncrona
        /// </summary>
        private async Task IntentarReconexionModbus()
        {
            try
            {
                if (ultimaConfigModbus == null) return;

                // Limpiar conexión anterior si existe
                if (modbusClient != null)
                {
                    try
                    {
                        modbusClient.Disconnect();
                    }
                    catch { }
                    modbusClient = null;
                }

                // Crear nueva conexión Modbus
                var nuevoModbusClient = new ModbusClient(ultimaConfigModbus.Ip, ultimaConfigModbus.Puerto);
                nuevoModbusClient.UnitIdentifier = ultimaConfigModbus.DeviceId;
                nuevoModbusClient.ConnectionTimeout = ultimaConfigModbus.Timeout;

                // Conectar con timeout
                var connectTask = Task.Run(() => nuevoModbusClient.Connect());
                var timeoutTask = Task.Delay(ultimaConfigModbus.Timeout + 2000);

                var completedTask = await Task.WhenAny(connectTask, timeoutTask);

                if (completedTask == connectTask && nuevoModbusClient.Connected)
                {
                    // Actualizar UI en el hilo principal
                    this.Invoke(new Action(() =>
                    {
                        modbusClient = nuevoModbusClient;
                        modbusConectado = true;
                        lblEstadoModbus.Text = "🟢 Modbus: Reconectado";
                        lblEstadoModbus.ForeColor = Color.LimeGreen;
                        btnModbusConectar.Enabled = false;
                        btnModbusDesconectar.Enabled = true;
                        btnModbusLeerRegistros.Enabled = true;
                        btnModbusEscribirRegistro.Enabled = true;

                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Reconectado exitosamente (intento #{contadorReconexionesModbus})\n");
                        rtbLog.ScrollToCaret();
                    }));
                }
                else
                {
                    // Timeout o error de conexión
                    nuevoModbusClient?.Disconnect();
                    throw new TimeoutException("Timeout en reconexión Modbus");
                }
            }
            catch (Exception ex)
            {
                this.Invoke(new Action(() =>
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error en reconexión #{contadorReconexionesModbus}: {ex.Message}\n");
                    rtbLog.ScrollToCaret();
                }));
            }
        }

        #endregion

        #region GESTIÓN DE CORREO ELECTRÓNICO

        /// <summary>
        /// Inicializa la configuración de correo
        /// </summary>
        private void InicializarConfiguracionCorreo()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔧 CORREO: Iniciando configuración...\n");

                configuracionCorreo = new ConfiguracionCorreo();
                gestorCorreo = new GestorDeCorreo(configuracionCorreo);

                // Estado inicial
                ActualizarEstadoConexionCorreo(false);

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ CORREO: Configuración inicializada correctamente\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error inicializando configuración: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza el estado visual de la conexión de correo
        /// </summary>
        /// <param name="conectado">Estado de conexión</param>
        private void ActualizarEstadoConexionCorreo(bool conectado)
        {
            try
            {
                correoConectado = conectado;

                if (conectado)
                {
                    lblEstadoCorreo.Text = "Correo: Conectado";
                    lblEstadoCorreo.ForeColor = Color.LimeGreen;
                    panelEstadoCorreo.BackColor = Color.LimeGreen;

                    btnConectarCorreo.Enabled = false;
                    btnDesconectarCorreo.Enabled = true;
                    btnProcesarCorreos.Enabled = true;

                    btnConectarCorreo.Text = "Conectado";
                    btnConectarCorreo.BackColor = Color.LimeGreen;

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ CORREO: Estado actualizado - Conectado\n");
                    rtbLog.ScrollToCaret();
                }
                else
                {
                    lblEstadoCorreo.Text = "Correo: Desconectado";
                    lblEstadoCorreo.ForeColor = Color.White;
                    panelEstadoCorreo.BackColor = Color.Gray;

                    btnConectarCorreo.Enabled = true;
                    btnDesconectarCorreo.Enabled = false;
                    btnProcesarCorreos.Enabled = false;

                    btnConectarCorreo.Text = "Conectar";
                    btnConectarCorreo.BackColor = Color.FromArgb(70, 130, 180);

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚪ CORREO: Estado actualizado - Desconectado\n");
                    rtbLog.ScrollToCaret();
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error actualizando estado: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Carga la configuración de correo desde archivo
        /// </summary>
        private void CargarConfiguracionCorreo()
        {
            try
            {
                configuracionCorreo = ConfiguracionCorreo.CargarConfiguracion();

                // Cargar valores en la interfaz
                txtEwsServerUrl.Text = configuracionCorreo.EwsServerUrl;
                txtEwsUsuario.Text = configuracionCorreo.EwsUsername;
                txtEwsPassword.Text = configuracionCorreo.EwsPassword;
                txtEwsDominio.Text = configuracionCorreo.EwsDomain;
                txtEmailNotificacion.Text = configuracionCorreo.EmailNotification;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ CORREO: Configuración cargada correctamente\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error cargando configuración: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Guarda la configuración de correo en archivo
        /// </summary>
        private void GuardarConfiguracionCorreo()
        {
            try
            {
                if (configuracionCorreo == null)
                    configuracionCorreo = new ConfiguracionCorreo();

                // Actualizar configuración con valores de la interfaz
                configuracionCorreo.EwsServerUrl = txtEwsServerUrl.Text.Trim();
                configuracionCorreo.EwsUsername = txtEwsUsuario.Text.Trim();
                configuracionCorreo.EwsPassword = txtEwsPassword.Text;
                configuracionCorreo.EwsDomain = txtEwsDominio.Text.Trim();
                configuracionCorreo.EmailNotification = txtEmailNotificacion.Text.Trim();

                configuracionCorreo.GuardarConfiguracion();

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ CORREO: Configuración guardada correctamente\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error guardando configuración: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Inicializa el DataGridView de estados horarios
        /// </summary>
        private void InicializarDataGridViewEstadosHorarios()
        {
            try
            {
                if (dgvEstadosHorarios == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ CORREO: DataGridView no inicializado\n");
                    return;
                }

                dgvEstadosHorarios.Columns.Clear();

                // Configurar columnas
                dgvEstadosHorarios.Columns.Add("Hora", "Hora");
                dgvEstadosHorarios.Columns.Add("Estado", "Estado");
                dgvEstadosHorarios.Columns.Add("Descripcion", "Descripción");

                // Configurar ancho de columnas
                dgvEstadosHorarios.Columns["Hora"].Width = 80;
                dgvEstadosHorarios.Columns["Estado"].Width = 120;
                dgvEstadosHorarios.Columns["Descripcion"].Width = 280;

                // Llenar con datos iniciales (24 horas)
                ActualizarDataGridViewEstadosHorarios(new bool[24]);
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error inicializando grid estados: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza el DataGridView con los estados horarios actuales
        /// </summary>
        private void ActualizarDataGridViewEstadosHorarios(bool[] estados)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() => ActualizarDataGridViewEstadosHorarios(estados)));
                    return;
                }

                dgvEstadosHorarios.Rows.Clear();

                for (int i = 0; i < 24; i++)
                {
                    string hora = $"H{i + 1:00} ({i + 1:00}:00)";
                    string estado = estados[i] ? "RESTRINGIDA" : "Disponible";
                    string descripcion = estados[i] ? "Hora con restricciones activas" : "Hora sin restricciones";

                    int rowIndex = dgvEstadosHorarios.Rows.Add(hora, estado, descripcion);

                    // Colorear filas según estado
                    if (estados[i])
                    {
                        dgvEstadosHorarios.Rows[rowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 200, 200);
                        dgvEstadosHorarios.Rows[rowIndex].DefaultCellStyle.ForeColor = Color.DarkRed;
                    }
                    else
                    {
                        dgvEstadosHorarios.Rows[rowIndex].DefaultCellStyle.BackColor = Color.FromArgb(200, 255, 200);
                        dgvEstadosHorarios.Rows[rowIndex].DefaultCellStyle.ForeColor = Color.DarkGreen;
                    }
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error actualizando grid estados: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Inicializa los eventos de los controles de correo
        /// </summary>
        private void InicializarEventosCorreo()
        {
            try
            {
                if (btnConectarCorreo != null)
                    btnConectarCorreo.Click += BtnConectarCorreo_Click;
                if (btnDesconectarCorreo != null)
                    btnDesconectarCorreo.Click += BtnDesconectarCorreo_Click;
                if (btnProcesarCorreos != null)
                    btnProcesarCorreos.Click += BtnProcesarCorreos_Click;

                // Eventos de cambio de texto para guardar configuración automáticamente
                if (txtEwsServerUrl != null)
                    txtEwsServerUrl.TextChanged += (s, e) => GuardarConfiguracionCorreo();
                if (txtEwsUsuario != null)
                    txtEwsUsuario.TextChanged += (s, e) => GuardarConfiguracionCorreo();
                if (txtEwsPassword != null)
                    txtEwsPassword.TextChanged += (s, e) => GuardarConfiguracionCorreo();
                if (txtEwsDominio != null)
                    txtEwsDominio.TextChanged += (s, e) => GuardarConfiguracionCorreo();
                if (txtEmailNotificacion != null)
                    txtEmailNotificacion.TextChanged += (s, e) => GuardarConfiguracionCorreo();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error inicializando eventos: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Event handler para conectar correo
        /// </summary>
        private async void BtnConectarCorreo_Click(object sender, EventArgs e)
        {
            try
            {
                btnConectarCorreo.Enabled = false;
                btnConectarCorreo.Text = "Conectando...";

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 CORREO: Iniciando conexión EWS...\n");
                rtbLog.ScrollToCaret();

                // Guardar configuración actual
                GuardarConfiguracionCorreo();

                // Crear gestor de correo
                gestorCorreo = new GestorDeCorreo(configuracionCorreo);

                // Probar conexión
                bool conexionExitosa = await gestorCorreo.ProbarConexionAsync();

                if (conexionExitosa)
                {
                    correoConectado = true;
                    btnConectarCorreo.Text = "Conectado";
                    btnConectarCorreo.BackColor = Color.FromArgb(0, 204, 122);
                    btnDesconectarCorreo.Enabled = true;
                    btnProcesarCorreos.Enabled = true;

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ CORREO: Conexión EWS establecida correctamente\n");
                    rtbLog.ScrollToCaret();

                    // Actualizar indicador de estado
                    lblEstadoCorreo.Text = "Conectado";
                    lblEstadoCorreo.ForeColor = Color.LimeGreen;
                    panelEstadoCorreo.BackColor = Color.LimeGreen;
                }
                else
                {
                    throw new Exception("No se pudo establecer conexión con el servidor EWS");
                }
            }
            catch (Exception ex)
            {
                correoConectado = false;
                btnConectarCorreo.Text = "Conectar";
                btnConectarCorreo.BackColor = Color.FromArgb(0, 122, 204);
                btnDesconectarCorreo.Enabled = false;
                btnProcesarCorreos.Enabled = false;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error de conexión: {ex.Message}\n");
                rtbLog.ScrollToCaret();

                // Actualizar indicador de estado
                lblEstadoCorreo.Text = "Error";
                lblEstadoCorreo.ForeColor = Color.Red;
                panelEstadoCorreo.BackColor = Color.Red;

                MessageBox.Show($"Error conectando al servidor EWS:\n{ex.Message}", "Error de Conexión",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnConectarCorreo.Enabled = true;
            }
        }

        /// <summary>
        /// Event handler para desconectar correo
        /// </summary>
        private void BtnDesconectarCorreo_Click(object sender, EventArgs e)
        {
            try
            {
                if (gestorCorreo != null)
                {
                    gestorCorreo.Dispose();
                    gestorCorreo = null;
                }

                correoConectado = false;
                btnConectarCorreo.Text = "Conectar";
                btnConectarCorreo.BackColor = Color.FromArgb(0, 122, 204);
                btnDesconectarCorreo.Enabled = false;
                btnProcesarCorreos.Enabled = false;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔌 CORREO: Desconectado del servidor EWS\n");
                rtbLog.ScrollToCaret();

                // Actualizar indicador de estado
                lblEstadoCorreo.Text = "Desconectado";
                lblEstadoCorreo.ForeColor = Color.Gray;
                panelEstadoCorreo.BackColor = Color.Gray;
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error desconectando: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Event handler para procesar correos
        /// </summary>
        private async void BtnProcesarCorreos_Click(object sender, EventArgs e)
        {
            try
            {
                if (!correoConectado || gestorCorreo == null)
                {
                    MessageBox.Show("Debe conectarse primero al servidor EWS", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                btnProcesarCorreos.Enabled = false;
                btnProcesarCorreos.Text = "Procesando...";

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 CORREO: Iniciando procesamiento de correos...\n");
                rtbLog.ScrollToCaret();

                // Procesar correos
                var resultado = await gestorCorreo.ConectarYProcesarCorreosAsync();

                // Mostrar resultados
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 CORREO: {resultado.ResumenProcesamiento}\n");

                if (resultado.TieneErrores)
                {
                    foreach (var error in resultado.ErroresProcesamiento)
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ CORREO: {error}\n");
                    }
                }

                // Actualizar grid con estados horarios actuales
                var estadosActuales = gestorCorreo.ObtenerEstadosActuales();
                ActualizarDataGridViewEstadosHorarios(estadosActuales);

                // Mostrar resumen de horas
                var horasConLimitaciones = gestorCorreo.ObtenerHorasConLimitaciones();
                var horasSinLimitaciones = gestorCorreo.ObtenerHorasSinLimitaciones();

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🕐 CORREO: Horas CON limitaciones: {string.Join(", ", horasConLimitaciones)}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🕐 CORREO: Horas SIN limitaciones: {string.Join(", ", horasSinLimitaciones)}\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show($"Procesamiento completado:\n\n" +
                    $"• Correos encontrados: {resultado.TotalCorreosEncontrados}\n" +
                    $"• Correos filtrados: {resultado.CorreosFiltrados}\n" +
                    $"• Correos procesados: {resultado.CorreosProcesados}\n" +
                    $"• Horas con limitaciones: {horasConLimitaciones.Count}\n" +
                    $"• Horas sin limitaciones: {horasSinLimitaciones.Count}",
                    "Procesamiento Completado", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error procesando correos: {ex.Message}\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show($"Error procesando correos:\n{ex.Message}", "Error de Procesamiento",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnProcesarCorreos.Enabled = true;
                btnProcesarCorreos.Text = "Procesar";
            }
        }

        #endregion

        // FIN GESTIÓN DE LA NAVEGACIÓN ************************************************************************
    }
}
