﻿using ControlDeProducciónENAGAS.Clases;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using EasyModbus;

namespace ControlDeProducciónENAGAS.Formularios
{
    public partial class MAIN : Form
    {
        // Variables para gestión de correo
        private ConfiguracionCorreo configuracionCorreo;
        private GestorDeCorreo gestorCorreo;
        private bool correoConectado = false;

        public MAIN()
        {
            InitializeComponent();

            // Solo inicializar lo mínimo necesario en el constructor
            // para no interferir con el diseñador
            if (!DesignMode)
            {
                // Cargar configuración MQTT automáticamente
                CargarConfiguracionMQTT();

                // Cargar configuración Modbus automáticamente
                CargarConfiguracionModbus();

                // Cargar configuración de correo automáticamente
                CargarConfiguracionCorreo();
            }
        }

        /// <summary>
        /// Evento Load del formulario - inicializa todo después de que el formulario esté completamente cargado
        /// </summary>
        private void MAIN_Load(object sender, EventArgs e)
        {
            if (!DesignMode)
            {
                // Ocultar pestañas en ejecución (pero mantenerlas visibles en diseñador)
                OcultarPestañasEnEjecucion();

                // Configura el estado inicial del formulario
                ConfigurarEstadoInicial();

                // Inicializar DataGridView de topics MQTT
                InicializarDataGridViewTopics();

                // Inicializar gestor de UI MQTT
                InicializarMqttUIManager();

                // Inicializar botón de refrescar MQTT
                InicializarBotonRefrescarMQTT();

                // Inicializar sistema sinóptico
                InicializarSistemaSinoptico();

                // Inicializar controles de visualización sinóptico
                InicializarVisualizacionSinoptico();

                // Inicializar configuración Modbus
                InicializarModbus();

                // Inicializar DataGridView de estados horarios
                InicializarDataGridViewEstadosHorarios();

                // Inicializar eventos de correo
                InicializarEventosCorreo();

                // Inicializar controles del modo automático
                InicializarModoAutomatico();

                // Inicializar visualización de limitaciones horarias
                InicializarLimitacionesHorarias();

                // Inicializar sistema de reconexión automática
                InicializarSistemaReconexion();

                // Habilitar timer después de que todo esté inicializado
                timer1.Enabled = true;
            }
        }

        private ConexionMQTT? mqtt;
        private bool MQTTConectado = false;

        // EasyModbusTCP.NET client
        private ModbusClient? modbusClient;
        private bool modbusConectado = false;

        // Sistema de reconexión automática
        private ConfiguracionMQTT? ultimaConfigMQTT;
        private ConfiguracionModbus? ultimaConfigModbus;
        private DateTime ultimoIntentoReconexionMQTT = DateTime.MinValue;
        private DateTime ultimoIntentoReconexionModbus = DateTime.MinValue;
        private bool reconexionMQTTHabilitada = true;
        private bool reconexionModbusHabilitada = true;
        private int contadorReconexionesMQTT = 0;
        private int contadorReconexionesModbus = 0;

        // Variables para controlar reconexión automática (solo después de conexión exitosa)
        private bool modbusConectadoAlgunaVez = false;
        private bool mqttConectadoAlgunaVez = false;

        // Los controles dgvMqttTopics y btnRefrescarMQTT están declarados en el Designer

        // Gestor de UI MQTT para separar lógica de negocio
        private MqttUIManager? mqttUIManager;

        // Sistema de monitorización bidireccional para sinóptico
        private readonly Dictionary<string, string> topicControlFeedback = new Dictionary<string, string>
        {
            // CONSIGNAS AUTOMÁTICAS
            { "DD/ENAGAS/ALMENDRALEJO/CONSIGNAPA", "DD/ENAGAS/ALMENDRALEJO/FEEDBACKPA" },
            { "DD/ENAGAS/ALMENDRALEJO/MOTIVOPA", "DD/ENAGAS/ALMENDRALEJO/FEEDBACKMOTIVOPA" },

            // SRAP (Sistema de Regulación Automática de Presión)
            { "DD/ENAGAS/ALMENDRALEJO/PARADAR", "DD/ENAGAS/ALMENDRALEJO/FEEDBACKPARADAR" },
            { "DD/ENAGAS/ALMENDRALEJO/PARADAM", "DD/ENAGAS/ALMENDRALEJO/FEEDBACKPARADAM" },
            { "DD/ENAGAS/ALMENDRALEJO/PARADAL", "DD/ENAGAS/ALMENDRALEJO/FEEDBACKPARADAL" },
            { "DD/ENAGAS/ALMENDRALEJO/PRESELECCION", "DD/ENAGAS/ALMENDRALEJO/FEEDBACKPRESELECCION" }
        };

        private readonly Dictionary<string, string> ultimosValoresControl = new Dictionary<string, string>();
        private bool sinopticoHabilitado = false;

        // Variables de estado de paradas SRAP (renombradas según especificación)
        private bool paradaR = false;      // Parada Radar
        private bool paradaMl = false;     // Parada Manual
        private bool paradaL = false;      // Parada Local
        private bool preseleccionActiva = false;

        // Variables del sistema de modo automático
        private bool modoAutomaticoActivo = false;
        private string direccionModbusModoAutomatico = "";
        private int valorConsignaActual = 0;
        private int valorModoAutomaticoCalculado = 0;

        // Variables de prioridades del sistema automático
        private string prioridadActiva = "NINGUNA";
        private bool hayParadasSRAPActivas = false;
        private bool hayLimitacionesEmailActivas = false;

        // Variables de configuración del modo automático
        private bool limitacionesEmailHabilitadas = true;
        private bool paradasSRAPHabilitadas = true;
        private int valorConsignaPa = 100;  // Valor que se envía por CONSIGNAPA
        private int consignaManual = 100;   // Valor manual cuando no está en automático

        // Los controles del sinóptico ahora están en el Designer
        // No necesitamos declaraciones privadas adicionales




        // GESTIÓN DE LA NAVEGACIÓN Y DISEÑO ***************************************************************

        /// <summary>
        /// Método central para cambiar de pestaña y actualizar el estilo de los botones.
        /// </summary>
        /// <param name="paginaDestino">La TabPage que se va a mostrar.</param>
        /// <param name="botonActivo">El botón de navegación que se va a resaltar.</param>
        /// 
        private void ConfigurarEstadoInicial()
        {
            // Solo configurar si no estamos en modo diseño
            if (!DesignMode)
            {
                // Establece la pestaña de Correo como la inicial
                NavegarHacia(tabPageCorreo, btnNavCorreo);
            }
        }
        private void NavegarHacia(TabPage paginaDestino, Button botonActivo)
        {
            // Cambia la pestaña activa en el TabControl
            if (tabControlPrincipal.SelectedTab != paginaDestino)
            {
                tabControlPrincipal.SelectedTab = paginaDestino;
            }

            // Actualiza el estilo de los botones
            ResetearEstilosBotones();
            ResaltarBoton(botonActivo);
        }

        /// <summary>
        /// Pone todos los botones de navegación en su estado "inactivo".
        /// </summary>
        private void ResetearEstilosBotones()
        {
            // Resetear todos los botones a su estado inactivo
            btnNavCorreo.BackColor = Color.FromArgb(64, 64, 64);
            btnNavCorreo.ForeColor = Color.White;

            btnNavMqtt.BackColor = Color.FromArgb(64, 64, 64);
            btnNavMqtt.ForeColor = Color.White;

            btnNavModbus.BackColor = Color.FromArgb(64, 64, 64);
            btnNavModbus.ForeColor = Color.White;

            btnNavSinoptico.BackColor = Color.FromArgb(64, 64, 64);
            btnNavSinoptico.ForeColor = Color.White;
        }

        /// <summary>
        /// Resalta un botón de navegación para mostrar que está "activo".
        /// </summary>
        private void ResaltarBoton(Button boton)
        {
            // Resaltar el botón activo con color azul
            boton.BackColor = Color.FromArgb(0, 122, 204);
            boton.ForeColor = Color.White;
        }


        // --- EVENTOS CLICK DE LOS BOTONES DE NAVEGACIÓN ---

        private void btnNavCorreo_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageCorreo, btnNavCorreo);
        }

        private void btnNavMqtt_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageMqtt, btnNavMqtt);
        }

        private void btnNavModbus_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageModbus, btnNavModbus);
        }

        private void btnNavSinoptico_Click(object sender, EventArgs e)
        {
            NavegarHacia(tabPageSinoptico, btnNavSinoptico);
        }

        // --- EVENTOS CLICK DE LOS BOTONES SRAP ---

        private async void btnSRAPParadar_Click(object sender, EventArgs e)
        {
            await EnviarComandoSRAP("DD/ENAGAS/ALMENDRALEJO/PARADAR", "1", "PARADAR activado");
        }

        private async void btnSRAPParadam_Click(object sender, EventArgs e)
        {
            await EnviarComandoSRAP("DD/ENAGAS/ALMENDRALEJO/PARADAM", "1", "PARADAM activado");
        }

        private async void btnSRAPParadal_Click(object sender, EventArgs e)
        {
            await EnviarComandoSRAP("DD/ENAGAS/ALMENDRALEJO/PARADAL", "1", "PARADAL activado");
        }

        private async void btnSRAPPreseleccion_Click(object sender, EventArgs e)
        {
            await EnviarComandoSRAP("DD/ENAGAS/ALMENDRALEJO/PRESELECCION", "1", "PRESELECCIÓN activada");
        }
        private void btnCerrar_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// Oculta las pestañas del TabControl en ejecución (pero las mantiene visibles en el diseñador)
        /// </summary>
        private void OcultarPestañasEnEjecucion()
        {
            try
            {
                // Configurar TabControl para ocultar pestañas en ejecución
                tabControlPrincipal.Appearance = TabAppearance.FlatButtons;
                tabControlPrincipal.ItemSize = new Size(0, 1);
                tabControlPrincipal.SizeMode = TabSizeMode.Fixed;

                // Seleccionar la primera pestaña por defecto
                tabControlPrincipal.SelectedIndex = 0;

                LogSafe("🔒 SISTEMA: Pestañas ocultas en modo ejecución");
            }
            catch (Exception ex)
            {
                LogSafe($"❌ ERROR: No se pudieron ocultar las pestañas: {ex.Message}");
            }
        }
        private void btnLimpiarLog_Click(object sender, EventArgs e)
        {
            rtbLog.Clear();
            rtbLog.AppendText("=== LOG DEL SISTEMA (limpiado) ===\n");
        }




        private async void btnMqttConectar_Click(object sender, EventArgs e)
        {
            try
            {
                // Validar puerto
                if (!int.TryParse(txtMqttPuerto.Text.Trim(), out int port))
                {
                    MessageBox.Show("El puerto MQTT debe ser un número válido.", "Puerto Inválido", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Puerto MQTT inválido\n");
                    return;
                }

                // Usar el MqttUIManager para la conexión
                if (mqttUIManager == null)
                {
                    MessageBox.Show("Error: MqttUIManager no inicializado", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                bool conectado = await mqttUIManager.ConectarAsync(
                    cmbMqttProtocolo.SelectedItem?.ToString() ?? "mqtts://",  // protocol
                    txtMqttHost.Text.Trim(),                                  // hostAddress
                    port,                                                     // port
                    txtMqttClientId.Text.Trim(),                             // clientId
                    txtMqttUsuario.Text.Trim(),                              // username
                    txtMqttPassword.Text,                                     // password (SIN Trim!)
                    chkMqttSslTls.Checked                                    // useTlsFromCheckbox
                );

                if (conectado)
                {
                    lblEstadoMqtt.Text = "MQTT: Conectado ✅";
                    lblEstadoMqtt.ForeColor = Color.LimeGreen;
                    btnMqttConectar.Enabled = false;
                    btnMqttDesconectar.Enabled = true;

                    // Habilitar botón de refrescar MQTT
                    if (btnRefrescarMQTT != null)
                        btnRefrescarMQTT.Enabled = true;

                    // Asignar la instancia MQTT desde el UIManager
                    mqtt = mqttUIManager.Mqtt;

                    // Suscribir topics del CSV de forma asíncrona
                    Task.Run(async () =>
                    {
                        try
                        {
                            await SuscribirTopicsDelCSV();
                            await SuscribirTopicsSinoptico();
                        }
                        catch (Exception ex)
                        {
                            LogSafe($"⚠️ Error en suscripciones iniciales: {ex.Message}");
                        }
                    });

                    // Habilitar Lectura de Topics
                    MQTTConectado = true;

                    // Guardar configuración válida para reconexión automática
                    GuardarConfiguracionMQTTValida();

                    // Habilitar reconexión automática
                    reconexionMQTTHabilitada = true;
                    contadorReconexionesMQTT = 0; // Resetear contador
                    mqttConectadoAlgunaVez = true; // Marcar que se ha conectado exitosamente

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Conectado a MQTT: {txtMqttHost.Text}:{port}\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 💡 Usa el botón 'Refrescar MQTT' para ver los topics disponibles\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 Reconexión automática MQTT habilitada\n");
                    rtbLog.ScrollToCaret();
                }
                else
                {
                    MessageBox.Show("No se pudo conectar al broker MQTT");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error conectando a MQTT\n");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error al conectar MQTT: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error conectando a MQTT: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }


        /// <summary>
        /// Suscribe a topics del CSV de forma segura y asíncrona
        /// </summary>
        private async Task SuscribirTopicsDelCSV()
        {
            try
            {
                LogSafe("📋 Iniciando suscripción de topics desde CSV...");

                // Verificar conexión MQTT
                if (mqtt == null || !mqtt.EstaConectado)
                {
                    LogSafe("⚠️ CSV: MQTT no conectado, omitiendo suscripción");
                    return;
                }

                // Verificar y crear archivo CSV si no existe
                if (!File.Exists("topics.csv"))
                {
                    LogSafe("⚠️ topics.csv no existe, creando archivo de ejemplo...");
                    try
                    {
                        await File.WriteAllTextAsync("topics.csv", "enagas/temperatura\nenagas/presion\nenagas/estado\nenagas/caudal");
                        LogSafe("✅ Archivo topics.csv creado");
                    }
                    catch (Exception ex)
                    {
                        LogSafe($"❌ Error creando topics.csv: {ex.Message}");
                        return;
                    }
                }

                // Leer archivo CSV de forma asíncrona
                string[] topics;
                try
                {
                    topics = await File.ReadAllLinesAsync("topics.csv");
                }
                catch (Exception ex)
                {
                    LogSafe($"❌ Error leyendo topics.csv: {ex.Message}");
                    return;
                }

                LogSafe($"📋 Leyendo {topics.Length} líneas de topics.csv");

                int topicsSuscritos = 0;
                int topicsError = 0;

                // Suscribir topics con manejo de errores individual
                foreach (string topic in topics)
                {
                    if (!topic.StartsWith("#") && !string.IsNullOrEmpty(topic.Trim()))
                    {
                        var topicLimpio = topic.Trim();
                        try
                        {
                            LogSafe($"📡 Suscribiendo a: {topicLimpio}");
                            await mqtt.SuscribirTopic(topicLimpio);
                            topicsSuscritos++;
                        }
                        catch (Exception ex)
                        {
                            LogSafe($"❌ Error suscribiendo a '{topicLimpio}': {ex.Message}");
                            topicsError++;
                        }
                    }
                }

                LogSafe($"✅ Suscripción CSV completada: {topicsSuscritos} exitosos, {topicsError} errores");
            }
            catch (Exception ex)
            {
                LogSafe($"❌ Error general suscribiendo topics del CSV: {ex.Message}");
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            // No ejecutar nada en modo diseño
            if (DesignMode) return;

            // Timer ya no actualiza automáticamente el DataGridView
            // Solo se actualiza manualmente con el botón btnRefrescarMQTT

            // Procesar cambios en topics de control del sinóptico
            ProcesarCambiosTopicsControl();

            // Actualizar visualización del sinóptico
            ActualizarVisualizacionSinoptico();

            // Sistema de reconexión automática
            VerificarYReconectarServicios();
        }

        private void btnMqttDesconectar_Click(object sender, EventArgs e)
        {
            // Usar el MqttUIManager para la desconexión
            mqttUIManager?.Desconectar();

            // Limpiar la referencia al objeto mqtt
            mqtt = null;

            lblEstadoMqtt.Text = "MQTT: Desconectado ❌";
            lblEstadoMqtt.ForeColor = Color.Red;
            btnMqttConectar.Enabled = true;
            btnMqttDesconectar.Enabled = false;
            MQTTConectado = false;

            // Deshabilitar reconexión automática temporalmente (se rehabilita al conectar manualmente)
            reconexionMQTTHabilitada = false;

            // Deshabilitar sistema sinóptico
            DeshabilitarSistemaSinoptico();

            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔌 MQTT desconectado\n");
            rtbLog.ScrollToCaret();
        }

        /// <summary>
        /// Carga la configuración MQTT desde el archivo JSON y la aplica al formulario
        /// </summary>
        private void CargarConfiguracionMQTT()
        {
            try
            {
                var config = ConfiguracionMQTT.CargarDesdeArchivo();

                // Aplicar configuración a los controles del formulario
                cmbMqttProtocolo.SelectedItem = config.Protocolo;
                txtMqttHost.Text = config.Host;
                txtMqttPuerto.Text = config.Puerto.ToString();
                txtMqttClientId.Text = config.ClientId;
                txtMqttUsuario.Text = config.Usuario;
                txtMqttPassword.Text = config.Password;
                chkMqttSslTls.Checked = config.UsarSslTls;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚙️ Configuración MQTT cargada: {config.Name}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🌐 Servidor: {config.Host}:{config.Puerto}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔒 SSL/TLS: {(config.UsarSslTls ? "Activado" : "Desactivado")}\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error cargando configuración: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }




        /// <summary>
        /// Inicializa el DataGridView para mostrar los topics MQTT (SOLO verifica, NO modifica Designer)
        /// </summary>
        private void InicializarDataGridViewTopics()
        {
            try
            {
                // Verificar que el control existe (debe ser agregado desde el diseñador)
                if (dgvMqttTopics == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CRITICAL: DataGridView dgvMqttTopics not found - must be added in Designer\n");
                    return;
                }

                // SOLO VERIFICAR - NO MODIFICAR NADA DEL DESIGNER
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 DataGridView found - Columns: {dgvMqttTopics.Columns.Count}\n");

                // Listar columnas configuradas en el Designer
                for (int i = 0; i < dgvMqttTopics.Columns.Count; i++)
                {
                    var col = dgvMqttTopics.Columns[i];
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 Column {i}: Name='{col.Name}', DataPropertyName='{col.DataPropertyName}'\n");
                }

                if (dgvMqttTopics.Columns.Count == 0)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ WARNING: No columns configured in Designer\n");
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ DataGridView configured in Designer with {dgvMqttTopics.Columns.Count} columns\n");
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error checking DataGridView: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Inicializa el gestor de UI MQTT para separar lógica de negocio
        /// </summary>
        private void InicializarMqttUIManager()
        {
            try
            {
                if (dgvMqttTopics == null || btnRefrescarMQTT == null || rtbLog == null)
                {
                    rtbLog?.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error: Controles requeridos no encontrados para MqttUIManager\n");
                    return;
                }

                mqttUIManager = new MqttUIManager(this, dgvMqttTopics, btnRefrescarMQTT, rtbLog);
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MqttUIManager inicializado correctamente\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog?.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error inicializando MqttUIManager: {ex.Message}\n");
                rtbLog?.ScrollToCaret();
            }
        }

        /// <summary>
        /// Inicializa el botón de refrescar MQTT topics
        /// </summary>
        private void InicializarBotonRefrescarMQTT()
        {
            try
            {
                if (btnRefrescarMQTT == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ Button btnRefrescarMQTT not found - must be added in Designer\n");
                    return;
                }

                // Configurar el botón (el evento Click ya está configurado en el Designer)
                btnRefrescarMQTT.Enabled = false; // Inicialmente deshabilitado

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Refresh MQTT button configured\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error configuring refresh button: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Evento del botón de refrescar MQTT topics
        /// </summary>
        private void btnRefrescarMQTT_Click(object? sender, EventArgs e)
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 Manual MQTT refresh requested\n");
                mqttUIManager?.ActualizarListaTopics(verbose: true);
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Manual MQTT refresh completed\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error in manual MQTT refresh: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza la lista de topics en el DataGridView usando DataTable para compatibilidad con Designer
        /// </summary>
        private void ActualizarListaTopics(bool verbose = false)
        {
            try
            {
                // Step 1: Verify DataGridView exists
                if (dgvMqttTopics == null)
                {
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ DEBUG: dgvMqttTopics is null - check Designer\n");
                    return;
                }

                // Step 2: Verify MQTT connection
                if (mqtt == null)
                {
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ DEBUG: mqtt object is null\n");
                    return;
                }

                if (!mqtt.EstaConectado)
                {
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ DEBUG: MQTT not connected\n");
                    return;
                }

                // Step 3: Create DataTable compatible with Designer columns
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("Topic", typeof(string));
                dataTable.Columns.Add("Valor", typeof(string));
                dataTable.Columns.Add("Timestamp", typeof(string));
                dataTable.Columns.Add("Estado", typeof(string));

                // Step 4: Get topics from MQTT storage
                var topicsData = mqtt.ObtenerTodosLosTopics();
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 DEBUG: MQTT storage has {topicsData.Count} topics with values\n");

                // Add topics with values
                foreach (var topic in topicsData)
                {
                    dataTable.Rows.Add(
                        topic.Key,
                        topic.Value ?? "Sin datos",
                        DateTime.Now.ToString("HH:mm:ss"),
                        string.IsNullOrEmpty(topic.Value) ? "⏳ Esperando" : "✅ Activo"
                    );
                }

                // Step 5: Also add subscribed topics from CSV that might not have values yet
                if (File.Exists("topics.csv"))
                {
                    string[] csvTopics = File.ReadAllLines("topics.csv");
                    foreach (string csvTopic in csvTopics)
                    {
                        if (!csvTopic.StartsWith("#") && !string.IsNullOrEmpty(csvTopic.Trim()))
                        {
                            string topicName = csvTopic.Trim();
                            // Only add if not already in the list
                            if (!topicsData.ContainsKey(topicName))
                            {
                                dataTable.Rows.Add(
                                    topicName,
                                    "Sin datos",
                                    DateTime.Now.ToString("HH:mm:ss"),
                                    "⏳ Esperando"
                                );
                            }
                        }
                    }
                }

                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 DEBUG: Total topics to display: {dataTable.Rows.Count}\n");

                // Step 6: Update DataGridView using DataTable
                if (InvokeRequired)
                {
                    Invoke(new Action(() => BindDataTableToGrid(dataTable, verbose)));
                }
                else
                {
                    BindDataTableToGrid(dataTable, verbose);
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ ERROR in ActualizarListaTopics: {ex.Message}\n");
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Stack trace: {ex.StackTrace}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Binds DataTable to DataGridView (compatible with Designer columns) preservando scroll
        /// </summary>
        private void BindDataTableToGrid(DataTable dataTable, bool verbose = false)
        {
            try
            {
                if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 DEBUG: Starting DataTable binding...\n");

                // Guardar posición de scroll actual
                int firstDisplayedScrollingRowIndex = -1;
                int firstDisplayedScrollingColumnIndex = -1;

                if (dgvMqttTopics.Rows.Count > 0)
                {
                    firstDisplayedScrollingRowIndex = dgvMqttTopics.FirstDisplayedScrollingRowIndex;
                    firstDisplayedScrollingColumnIndex = dgvMqttTopics.FirstDisplayedScrollingColumnIndex;
                }

                // Suspender layout para evitar parpadeo
                dgvMqttTopics.SuspendLayout();

                try
                {
                    // Clear current data source
                    dgvMqttTopics.DataSource = null;

                    // Bind DataTable
                    dgvMqttTopics.DataSource = dataTable;

                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ DEBUG: DataTable binding completed\n");
                    if (verbose) rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 DEBUG: DataGridView now has {dgvMqttTopics.Rows.Count} rows\n");

                    // Restaurar posición de scroll si es válida
                    if (firstDisplayedScrollingRowIndex >= 0 &&
                        firstDisplayedScrollingRowIndex < dgvMqttTopics.Rows.Count)
                    {
                        dgvMqttTopics.FirstDisplayedScrollingRowIndex = firstDisplayedScrollingRowIndex;
                    }

                    if (firstDisplayedScrollingColumnIndex >= 0 &&
                        firstDisplayedScrollingColumnIndex < dgvMqttTopics.Columns.Count)
                    {
                        dgvMqttTopics.FirstDisplayedScrollingColumnIndex = firstDisplayedScrollingColumnIndex;
                    }
                }
                finally
                {
                    // Reanudar layout
                    dgvMqttTopics.ResumeLayout(true);
                }

                if (verbose) rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ ERROR in BindDataTableToGrid: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }



        /// <summary>
        /// TEST METHOD: Load sample topics to verify DataGridView configuration
        /// Call this method to test if DataGridView can display data
        /// </summary>
        public void TestDataGridViewWithSampleData()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Starting DataGridView test with sample data\n");

                if (dgvMqttTopics == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST FAILED: dgvMqttTopics is null\n");
                    return;
                }

                // Create sample data matching the expected structure
                var sampleData = new List<object>
                {
                    new { Topic = "test/topic1", Valor = "Sample Value 1", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "✅ Active" },
                    new { Topic = "test/topic2", Valor = "Sample Value 2", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "✅ Active" },
                    new { Topic = "test/topic3", Valor = "No data", Timestamp = DateTime.Now.ToString("HH:mm:ss"), Estado = "⏳ Waiting" }
                };

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Binding {sampleData.Count} sample records\n");

                dgvMqttTopics.DataSource = null;
                dgvMqttTopics.DataSource = sampleData;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: DataGridView now shows {dgvMqttTopics.Rows.Count} rows\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ TEST COMPLETED: Check if data appears in DataGridView\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST FAILED: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// TEST METHOD: Check MQTT topics storage
        /// Call this method to verify if MQTT topics are being received and stored
        /// </summary>
        public void TestMqttTopicsStorage()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Checking MQTT topics storage\n");

                if (mqtt == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST: MQTT object is null\n");
                    return;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: MQTT Connected: {mqtt.EstaConectado}\n");

                var topics = mqtt.ObtenerTodosLosTopics();
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🧪 TEST: Total topics in storage: {topics.Count}\n");

                if (topics.Count == 0)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ TEST: No topics found - check MQTT subscription\n");
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 TEST: Topics in storage:\n");
                    foreach (var topic in topics)
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋   - '{topic.Key}' = '{topic.Value}'\n");
                    }
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ TEST FAILED: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        #region Sistema Sinóptico MQTT Bidireccional

        /// <summary>
        /// Inicializa el sistema de monitorización bidireccional del sinóptico
        /// </summary>
        private void InicializarSistemaSinoptico()
        {
            try
            {
                // Inicializar diccionario de últimos valores
                foreach (var topicControl in topicControlFeedback.Keys)
                {
                    ultimosValoresControl[topicControl] = string.Empty;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ Sistema sinóptico inicializado\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error inicializando sistema sinóptico: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Suscribe a todos los topics del sinóptico (control y feedback) de forma segura y asíncrona
        /// </summary>
        private async Task SuscribirTopicsSinoptico()
        {
            try
            {
                // Verificar conexión MQTT
                if (mqtt == null || !mqtt.EstaConectado)
                {
                    LogSafe("⚠️ SINÓPTICO: MQTT no conectado, omitiendo suscripción");
                    return;
                }

                LogSafe("🔄 SINÓPTICO: Suscribiendo a topics...");

                int topicsSuscritos = 0;
                int topicsError = 0;

                // Suscribir a topics de control con manejo de errores individual
                if (topicControlFeedback != null)
                {
                    foreach (var topicControl in topicControlFeedback.Keys)
                    {
                        try
                        {
                            await mqtt.SuscribirTopic(topicControl);
                            LogSafe($"📡 SINÓPTICO: Suscrito a control '{topicControl}'");
                            topicsSuscritos++;
                        }
                        catch (Exception ex)
                        {
                            LogSafe($"❌ SINÓPTICO: Error suscribiendo a control '{topicControl}': {ex.Message}");
                            topicsError++;
                        }
                    }

                    // Suscribir a topics de feedback con manejo de errores individual
                    foreach (var topicFeedback in topicControlFeedback.Values)
                    {
                        try
                        {
                            await mqtt.SuscribirTopic(topicFeedback);
                            LogSafe($"📡 SINÓPTICO: Suscrito a feedback '{topicFeedback}'");
                            topicsSuscritos++;
                        }
                        catch (Exception ex)
                        {
                            LogSafe($"❌ SINÓPTICO: Error suscribiendo a feedback '{topicFeedback}': {ex.Message}");
                            topicsError++;
                        }
                    }
                }

                // Habilitar sinóptico solo si al menos algunas suscripciones fueron exitosas
                if (topicsSuscritos > 0)
                {
                    sinopticoHabilitado = true;
                    LogSafe($"✅ SINÓPTICO: Sistema habilitado - {topicsSuscritos} suscripciones exitosas, {topicsError} errores");
                }
                else
                {
                    LogSafe($"❌ SINÓPTICO: No se pudo suscribir a ningún topic - {topicsError} errores");
                }
            }
            catch (Exception ex)
            {
                LogSafe($"❌ SINÓPTICO: Error general en suscripciones: {ex.Message}");
            }
        }

        /// <summary>
        /// Deshabilita el sistema sinóptico
        /// </summary>
        private void DeshabilitarSistemaSinoptico()
        {
            try
            {
                sinopticoHabilitado = false;
                ultimosValoresControl.Clear();

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SINÓPTICO: Sistema deshabilitado\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error deshabilitando: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }



        /// <summary>
        /// Inicializa los controles de visualización del sinóptico usando controles del DESIGNER
        /// </summary>
        private void InicializarVisualizacionSinoptico()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Inicializando controles del Designer\n");

                // Los controles ya están creados en el Designer, solo configuramos las columnas
                dgvSinopticoTopics.AutoGenerateColumns = false;

                // Crear columnas para el DataGridView
                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Tipo",
                    HeaderText = "Tipo",
                    DataPropertyName = "Tipo",
                    Width = 100
                });

                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Topic",
                    HeaderText = "Topic",
                    DataPropertyName = "Topic",
                    Width = 200
                });

                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Valor",
                    HeaderText = "Valor",
                    DataPropertyName = "Valor",
                    Width = 120
                });

                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Timestamp",
                    HeaderText = "Timestamp",
                    DataPropertyName = "Timestamp",
                    Width = 100
                });

                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Estado",
                    HeaderText = "Estado",
                    DataPropertyName = "Estado",
                    Width = 120
                });

                dgvSinopticoTopics.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Sincronizado",
                    HeaderText = "Sincronizado",
                    DataPropertyName = "Sincronizado",
                    Width = 120
                });

                // Los controles ya están agregados en el Designer
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Controles del Designer configurados correctamente\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 SINÓPTICO: DataGridView configurado con {dgvSinopticoTopics.Columns.Count} columnas\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error creando visualización sinóptico: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza la visualización del estado del sinóptico
        /// </summary>
        private void ActualizarVisualizacionSinoptico()
        {
            try
            {
                if (!sinopticoHabilitado || mqtt == null || !mqtt.EstaConectado)
                {
                    // Sistema deshabilitado
                    if (lblSinopticoEstado != null)
                        lblSinopticoEstado.Text = "🔴 Sistema Sinóptico: DESHABILITADO";

                    if (dgvSinopticoTopics != null)
                        dgvSinopticoTopics.DataSource = null;

                    return;
                }

                // Sistema habilitado - actualizar estado
                if (lblSinopticoEstado != null)
                    lblSinopticoEstado.Text = "🟢 Sistema Sinóptico: ACTIVO";

                if (lblUltimaActualizacion != null)
                    lblUltimaActualizacion.Text = $"Última Actualización: {DateTime.Now:HH:mm:ss}";

                // Actualizar DataGridView con estado de topics
                ActualizarDataGridViewSinoptico();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error actualizando visualización: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza el DataGridView con el estado actual de los topics del sinóptico
        /// </summary>
        private void ActualizarDataGridViewSinoptico()
        {
            try
            {
                if (dgvSinopticoTopics == null) return;

                // Crear DataTable para el estado del sinóptico
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("Tipo", typeof(string));
                dataTable.Columns.Add("Topic", typeof(string));
                dataTable.Columns.Add("Valor", typeof(string));
                dataTable.Columns.Add("Timestamp", typeof(string));
                dataTable.Columns.Add("Estado", typeof(string));
                dataTable.Columns.Add("Sincronizado", typeof(string));

                // Agregar topics de control
                foreach (var par in topicControlFeedback)
                {
                    string topicControl = par.Key;
                    string topicFeedback = par.Value;

                    string? valorControl = mqtt.LeerTopic(topicControl);
                    string? valorFeedback = mqtt.LeerTopic(topicFeedback);

                    // Determinar estado de sincronización
                    string sincronizado = "❓ N/A";
                    if (valorControl != null && valorFeedback != null)
                    {
                        sincronizado = valorControl == valorFeedback ? "✅ SÍ" : "❌ NO";
                    }
                    else if (valorControl != null && valorFeedback == null)
                    {
                        sincronizado = "⏳ Pendiente";
                    }

                    // Agregar fila de control
                    dataTable.Rows.Add(
                        "🎛️ Control",
                        topicControl.Replace("DD/ENAGAS/ALMENDRALEJO/", ""),
                        valorControl ?? "Sin datos",
                        DateTime.Now.ToString("HH:mm:ss"),
                        valorControl != null ? "✅ Activo" : "⏳ Esperando",
                        sincronizado
                    );

                    // Agregar fila de feedback
                    dataTable.Rows.Add(
                        "📤 Feedback",
                        topicFeedback.Replace("DD/ENAGAS/ALMENDRALEJO/", ""),
                        valorFeedback ?? "Sin datos",
                        DateTime.Now.ToString("HH:mm:ss"),
                        valorFeedback != null ? "✅ Activo" : "⏳ Esperando",
                        sincronizado
                    );
                }

                // Actualizar DataGridView
                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        dgvSinopticoTopics.DataSource = null;
                        dgvSinopticoTopics.DataSource = dataTable;
                        dgvSinopticoTopics.Refresh();
                    }));
                }
                else
                {
                    dgvSinopticoTopics.DataSource = null;
                    dgvSinopticoTopics.DataSource = dataTable;
                    dgvSinopticoTopics.Refresh();
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error actualizando DataGridView sinóptico: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Procesa cambios en topics de control y publica feedback automáticamente
        /// </summary>
        private async void ProcesarCambiosTopicsControl()
        {
            try
            {
                if (!sinopticoHabilitado || mqtt == null || !mqtt.EstaConectado)
                    return;

                // Verificar cambios en cada topic de control
                foreach (var par in topicControlFeedback)
                {
                    string topicControl = par.Key;
                    string topicFeedback = par.Value;

                    // Obtener valor actual del topic de control
                    string? valorActual = mqtt.LeerTopic(topicControl);

                    if (valorActual != null)
                    {
                        // Verificar si el valor ha cambiado
                        if (!ultimosValoresControl.ContainsKey(topicControl) ||
                            ultimosValoresControl[topicControl] != valorActual)
                        {
                            // Valor ha cambiado, registrar y publicar feedback
                            string valorAnterior = ultimosValoresControl.ContainsKey(topicControl) ?
                                                 ultimosValoresControl[topicControl] : "N/A";

                            ultimosValoresControl[topicControl] = valorActual;

                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 SINÓPTICO: Cambio detectado en '{topicControl}'\n");
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 SINÓPTICO: Valor anterior: '{valorAnterior}' → Nuevo: '{valorActual}'\n");

                            // Procesar específicamente las paradas SRAP
                            ProcesarParadaSRAP(topicControl, valorActual);

                            // Publicar feedback automáticamente
                            PublicarFeedback(topicControl, topicFeedback, valorActual);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error procesando cambios: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Procesa los valores de parada SRAP (0 o 1) y actualiza variables y visualización
        /// </summary>
        private async void ProcesarParadaSRAP(string topicControl, string valor)
        {
            try
            {
                // Convertir valor a booleano (1 = true = parada activa, 0 = false = parada inactiva)
                bool estadoParada = valor == "1";

                // Procesar según el tipo de parada (usando variables renombradas)
                switch (topicControl)
                {
                    case "DD/ENAGAS/ALMENDRALEJO/PARADAR":
                        paradaR = estadoParada;
                        ActualizarVisualizacionSRAP("PARADAR", estadoParada, btnSRAPParadar);
                        LogSafe($"🔧 SRAP: PARADAR {(estadoParada ? "ACTIVADA" : "DESACTIVADA")} - Valor: {valor}");
                        break;

                    case "DD/ENAGAS/ALMENDRALEJO/PARADAM":
                        paradaMl = estadoParada;
                        ActualizarVisualizacionSRAP("PARADAM", estadoParada, btnSRAPParadam);
                        LogSafe($"🔧 SRAP: PARADAM {(estadoParada ? "ACTIVADA" : "DESACTIVADA")} - Valor: {valor}");
                        break;

                    case "DD/ENAGAS/ALMENDRALEJO/PARADAL":
                        paradaL = estadoParada;
                        ActualizarVisualizacionSRAP("PARADAL", estadoParada, btnSRAPParadal);
                        LogSafe($"🔧 SRAP: PARADAL {(estadoParada ? "ACTIVADA" : "DESACTIVADA")} - Valor: {valor}");
                        break;

                    case "DD/ENAGAS/ALMENDRALEJO/PRESELECCION":
                        preseleccionActiva = estadoParada;
                        ActualizarVisualizacionSRAP("PRESELECCIÓN", estadoParada, btnSRAPPreseleccion);
                        LogSafe($"🔧 SRAP: PRESELECCIÓN {(estadoParada ? "ACTIVADA" : "DESACTIVADA")} - Valor: {valor}");
                        break;
                }

                // Actualizar estado general del sistema SRAP
                ActualizarEstadoGeneralSRAP();

                // Si el modo automático está activo, recalcular y enviar valor
                if (modoAutomaticoActivo)
                {
                    await CalcularYEnviarValorModoAutomatico();
                }
            }
            catch (Exception ex)
            {
                LogSafe($"❌ SRAP: Error procesando parada: {ex.Message}");
            }
        }

        /// <summary>
        /// Actualiza la visualización de un botón SRAP según su estado
        /// </summary>
        private void ActualizarVisualizacionSRAP(string tipoParada, bool activa, Button boton)
        {
            try
            {
                if (boton.InvokeRequired)
                {
                    boton.Invoke(new Action(() => ActualizarVisualizacionSRAP(tipoParada, activa, boton)));
                    return;
                }

                if (activa)
                {
                    // Parada activa - mostrar en rojo
                    boton.BackColor = Color.Red;
                    boton.ForeColor = Color.White;
                    boton.Text = $"🔴 {tipoParada} - ACTIVA";
                }
                else
                {
                    // Parada inactiva - mostrar en color normal
                    boton.BackColor = Color.FromArgb(70, 70, 73);
                    boton.ForeColor = Color.White;
                    boton.Text = $"⚪ {tipoParada}";
                }
            }
            catch (Exception ex)
            {
                LogSafe($"❌ SRAP: Error actualizando visualización de {tipoParada}: {ex.Message}");
            }
        }

        /// <summary>
        /// Actualiza el estado general del sistema SRAP
        /// </summary>
        private void ActualizarEstadoGeneralSRAP()
        {
            try
            {
                if (lblSRAPEstado.InvokeRequired)
                {
                    lblSRAPEstado.Invoke(new Action(ActualizarEstadoGeneralSRAP));
                    return;
                }

                // Contar paradas activas (usando variables renombradas)
                int paradasActivas = 0;
                if (paradaR) paradasActivas++;
                if (paradaMl) paradasActivas++;
                if (paradaL) paradasActivas++;

                // Actualizar variables globales para el modo automático
                hayParadasSRAPActivas = paradasActivas > 0;

                string estadoTexto;
                Color colorEstado;

                if (paradasActivas > 0)
                {
                    estadoTexto = $"⚠️ {paradasActivas} PARADA(S) ACTIVA(S)";
                    colorEstado = Color.Red;
                    prioridadActiva = "PARADAS SRAP";
                }
                else if (preseleccionActiva)
                {
                    estadoTexto = "✅ PRESELECCIÓN ACTIVA";
                    colorEstado = Color.LightGreen;
                    if (!hayLimitacionesEmailActivas)
                        prioridadActiva = "PRESELECCIÓN";
                }
                else
                {
                    estadoTexto = "⚪ SISTEMA NORMAL";
                    colorEstado = Color.White;
                    if (!hayLimitacionesEmailActivas)
                        prioridadActiva = "CONSIGNA";
                }

                // Agregar información del modo automático
                if (modoAutomaticoActivo)
                {
                    estadoTexto += $" | 🤖 MODO AUTO: {prioridadActiva}";
                }

                lblSRAPEstado.Text = $"Estado: {estadoTexto} - {DateTime.Now:HH:mm:ss}";
                lblSRAPEstado.ForeColor = colorEstado;
            }
            catch (Exception ex)
            {
                LogSafe($"❌ SRAP: Error actualizando estado general: {ex.Message}");
            }
        }

        /// <summary>
        /// Publica un valor en el topic de feedback correspondiente
        /// </summary>
        private async void PublicarFeedback(string topicControl, string topicFeedback, string valor)
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📤 SINÓPTICO: Publicando feedback...\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📤 SINÓPTICO: Control: '{topicControl}' → Feedback: '{topicFeedback}'\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📤 SINÓPTICO: Valor: '{valor}'\n");

                // Publicar en el topic de feedback
                bool publicado = await mqtt.EscribirTopic(topicFeedback, valor);

                if (publicado)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Feedback publicado exitosamente\n");

                    // Programar verificación de confirmación
                    Task.Delay(1000).ContinueWith(_ => VerificarConfirmacionFeedback(topicFeedback, valor));
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error publicando feedback\n");
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error en publicación: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Verifica que el feedback fue recibido correctamente
        /// </summary>
        private void VerificarConfirmacionFeedback(string topicFeedback, string valorEsperado)
        {
            try
            {
                if (!sinopticoHabilitado || mqtt == null || !mqtt.EstaConectado)
                    return;

                string? valorRecibido = mqtt.LeerTopic(topicFeedback);

                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        if (valorRecibido == valorEsperado)
                        {
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Confirmación exitosa en '{topicFeedback}'\n");
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SINÓPTICO: Valor confirmado: '{valorRecibido}'\n");
                        }
                        else
                        {
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SINÓPTICO: Confirmación pendiente en '{topicFeedback}'\n");
                            rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SINÓPTICO: Esperado: '{valorEsperado}', Recibido: '{valorRecibido ?? "null"}'\n");
                        }
                        rtbLog.ScrollToCaret();
                    }));
                }
            }
            catch (Exception ex)
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() =>
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SINÓPTICO: Error verificando confirmación: {ex.Message}\n");
                        rtbLog.ScrollToCaret();
                    }));
                }
            }
        }

        private void panelLog_Paint(object sender, PaintEventArgs e)
        {

        }

        #endregion

        #region Modbus con EasyModbusTCP.NET

        /// <summary>
        /// Inicializa la configuración Modbus con valores por defecto
        /// </summary>
        private void InicializarModbus()
        {
            try
            {
                // Configurar valores por defecto según las preferencias del usuario
                txtModbusIp.Text = "*************"; // IP por defecto para ENAGAS Huelva
                nudModbusPuerto.Value = 502; // Puerto estándar Modbus TCP
                nudModbusDeviceId.Value = 1; // Device ID por defecto

                // Configurar estado inicial de botones
                btnModbusConectar.Enabled = true;
                btnModbusDesconectar.Enabled = false;
                btnModbusLeerRegistros.Enabled = false;
                btnModbusEscribirRegistro.Enabled = false;

                // Estado inicial
                lblEstadoModbus.Text = "🔴 Modbus: Desconectado";
                lblEstadoModbus.ForeColor = Color.Red;
                lblModbusValorLeido.Text = "Valor: --";
                lblModbusValorLeido.ForeColor = Color.Gray;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Configuración inicial cargada\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔧 MODBUS: IP={txtModbusIp.Text}, Puerto={nudModbusPuerto.Value}, Device ID={nudModbusDeviceId.Value}\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error inicializando Modbus: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Evento del botón Conectar Modbus
        /// </summary>
        private async void btnModbusConectar_Click(object sender, EventArgs e)
        {
            try
            {
                // Validar datos de entrada
                if (string.IsNullOrWhiteSpace(txtModbusIp.Text))
                {
                    MessageBox.Show("❌ Ingrese una dirección IP válida", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Actualizar indicador de estado
                lblEstadoModbus.Text = "🔄 Conectando...";
                lblEstadoModbus.ForeColor = Color.Orange;
                btnModbusConectar.Enabled = false;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MODBUS: Iniciando conexión a {txtModbusIp.Text}:{nudModbusPuerto.Value}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔧 MODBUS: Device ID {nudModbusDeviceId.Value}, Timeout: 5000ms\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📚 MODBUS: Usando EasyModbusTCP.NET v5.6.0\n");
                rtbLog.ScrollToCaret();

                // Verificar conectividad de red antes de intentar conexión Modbus
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 MODBUS: Verificando conectividad a {txtModbusIp.Text}...\n");
                rtbLog.ScrollToCaret();

                bool ipAlcanzable = await VerificarConectividadIP(txtModbusIp.Text.Trim(), 3000);

                if (!ipAlcanzable)
                {
                    throw new System.Net.NetworkInformation.PingException($"La IP {txtModbusIp.Text} no es alcanzable. Verifique la conectividad de red.");
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: IP alcanzable, procediendo con conexión Modbus...\n");
                rtbLog.ScrollToCaret();

                // Crear cliente Modbus TCP
                modbusClient = new ModbusClient(txtModbusIp.Text.Trim(), (int)nudModbusPuerto.Value);
                modbusClient.UnitIdentifier = (byte)nudModbusDeviceId.Value;
                modbusClient.ConnectionTimeout = 5000; // 5 segundos timeout

                // Conectar de forma asíncrona con timeout
                var connectTask = Task.Run(() => modbusClient.Connect());
                var timeoutTask = Task.Delay(8000); // 8 segundos timeout total

                var completedTask = await Task.WhenAny(connectTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    throw new TimeoutException("Timeout de conexión (8 segundos)");
                }

                await connectTask; // Esperar a que termine la conexión

                if (modbusClient.Connected)
                {
                    modbusConectado = true;
                    lblEstadoModbus.Text = "🟢 Modbus: Conectado";
                    lblEstadoModbus.ForeColor = Color.LimeGreen;

                    btnModbusConectar.Enabled = false;
                    btnModbusDesconectar.Enabled = true;
                    btnModbusLeerRegistros.Enabled = true;
                    btnModbusEscribirRegistro.Enabled = true;

                    // Guardar configuración válida para reconexión automática
                    GuardarConfiguracionModbusValida();

                    // Habilitar reconexión automática
                    reconexionModbusHabilitada = true;
                    contadorReconexionesModbus = 0; // Resetear contador
                    modbusConectadoAlgunaVez = true; // Marcar que se ha conectado exitosamente

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Conectado exitosamente a {txtModbusIp.Text}:{nudModbusPuerto.Value}\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🎉 MODBUS: Listo para operaciones de lectura/escritura\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 Reconexión automática MODBUS habilitada\n");

                    // Mostrar mensaje de éxito
                    MessageBox.Show(
                        $"✅ CONEXIÓN MODBUS EXITOSA\n\n" +
                        $"🔌 IP: {txtModbusIp.Text}\n" +
                        $"🔌 Puerto: {nudModbusPuerto.Value}\n" +
                        $"🔧 Device ID: {nudModbusDeviceId.Value}\n" +
                        $"📚 Librería: EasyModbusTCP.NET v5.6.0\n\n" +
                        $"🎯 Ahora puede realizar operaciones de lectura y escritura",
                        "🎉 CONEXIÓN EXITOSA",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information
                    );
                }
                else
                {
                    throw new Exception("No se pudo establecer la conexión");
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                modbusConectado = false;
                lblEstadoModbus.Text = "🔴 Modbus: Error";
                lblEstadoModbus.ForeColor = Color.Red;
                btnModbusConectar.Enabled = true;
                btnModbusDesconectar.Enabled = false;
                btnModbusLeerRegistros.Enabled = false;
                btnModbusEscribirRegistro.Enabled = false;

                string errorMsg = ex.Message;
                if (ex is TimeoutException)
                {
                    errorMsg = "Timeout de conexión. Verifique que el PLC esté encendido y accesible en la red.";
                }
                else if (ex is System.Net.NetworkInformation.PingException)
                {
                    errorMsg = "IP no alcanzable. Verifique la conectividad de red y que la IP sea correcta.";
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error conectando - {errorMsg}\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show(
                    $"❌ ERROR AL CONECTAR CON EL PLC\n\n" +
                    $"🔌 IP: {txtModbusIp.Text}\n" +
                    $"🔌 Puerto: {nudModbusPuerto.Value}\n" +
                    $"🔧 Device ID: {nudModbusDeviceId.Value}\n\n" +
                    $"💥 Error: {errorMsg}\n\n" +
                    $"🔧 Soluciones:\n" +
                    $"• Verificar que el PLC esté encendido\n" +
                    $"• Comprobar la dirección IP y puerto\n" +
                    $"• Verificar conectividad de red\n" +
                    $"• Revisar configuración del firewall",
                    "💥 ERROR DE CONEXIÓN",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// Evento del botón Desconectar Modbus
        /// </summary>
        private void btnModbusDesconectar_Click(object sender, EventArgs e)
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MODBUS: Desconectando...\n");
                rtbLog.ScrollToCaret();

                if (modbusClient != null && modbusClient.Connected)
                {
                    modbusClient.Disconnect();
                }

                // Limpiar referencia
                modbusClient = null;
                modbusConectado = false;

                // Deshabilitar reconexión automática temporalmente (se rehabilita al conectar manualmente)
                reconexionModbusHabilitada = false;

                // Actualizar UI
                lblEstadoModbus.Text = "🔴 Modbus: Desconectado";
                lblEstadoModbus.ForeColor = Color.Red;

                btnModbusConectar.Enabled = true;
                btnModbusDesconectar.Enabled = false;
                btnModbusLeerRegistros.Enabled = false;
                btnModbusEscribirRegistro.Enabled = false;

                // Limpiar valor mostrado
                lblModbusValorLeido.Text = "Valor: --";
                lblModbusValorLeido.ForeColor = Color.Gray;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Desconectado correctamente\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show(
                    "✅ DESCONEXIÓN EXITOSA\n\n" +
                    "El cliente Modbus se ha desconectado correctamente.",
                    "🔌 DESCONECTADO",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error desconectando - {ex.Message}\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show(
                    $"⚠️ ERROR AL DESCONECTAR\n\n{ex.Message}",
                    "Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
            }
        }

        /// <summary>
        /// Evento del botón Leer Registros Modbus
        /// </summary>
        private async void btnModbusLeerRegistros_Click(object sender, EventArgs e)
        {
            try
            {
                if (modbusClient == null || !modbusClient.Connected)
                {
                    MessageBox.Show("❌ No hay conexión Modbus activa", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtModbusDireccionRegistro.Text))
                {
                    MessageBox.Show("❌ Ingrese la dirección del registro", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Convertir dirección (soporta MW430 o 40431)
                int direccion = ConvertirDireccionModbus(txtModbusDireccionRegistro.Text.Trim());
                if (direccion == -1)
                {
                    MessageBox.Show("❌ Dirección de registro inválida\n\nEjemplos válidos:\n- MW430 (se convierte a 430)\n- 430 (dirección directa)\n- 40431 (formato Modbus)",
                        "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔍 MODBUS: Leyendo registro {direccion}...\n");
                rtbLog.ScrollToCaret();

                // Intentar leer como Holding Register primero
                int[] valores = null;
                bool exitoso = false;
                string tipoRegistro = "";
                Exception ultimoError = null;

                try
                {
                    // Timeout para la operación de lectura
                    var readTask = Task.Run(() => modbusClient.ReadHoldingRegisters(direccion, 1));
                    var timeoutTask = Task.Delay(5000); // 5 segundos timeout

                    var completedTask = await Task.WhenAny(readTask, timeoutTask);

                    if (completedTask == timeoutTask)
                    {
                        throw new TimeoutException("Timeout leyendo Holding Register");
                    }

                    valores = await readTask;
                    exitoso = true;
                    tipoRegistro = "Holding Register";
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Éxito leyendo como Holding Register\n");
                }
                catch (Exception exHolding)
                {
                    ultimoError = exHolding;
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error en Holding Register - {exHolding.Message}\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MODBUS: Intentando como Input Register...\n");
                    rtbLog.ScrollToCaret();

                    try
                    {
                        // Timeout para Input Register también
                        var readTask = Task.Run(() => modbusClient.ReadInputRegisters(direccion, 1));
                        var timeoutTask = Task.Delay(5000); // 5 segundos timeout

                        var completedTask = await Task.WhenAny(readTask, timeoutTask);

                        if (completedTask == timeoutTask)
                        {
                            throw new TimeoutException("Timeout leyendo Input Register");
                        }

                        valores = await readTask;
                        exitoso = true;
                        tipoRegistro = "Input Register";
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Éxito leyendo como Input Register\n");
                    }
                    catch (Exception exInput)
                    {
                        ultimoError = exInput;
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error en Input Register - {exInput.Message}\n");
                        throw new Exception($"No se pudo leer como Holding Register ni Input Register.\nHolding: {exHolding.Message}\nInput: {exInput.Message}");
                    }
                }

                if (exitoso && valores != null && valores.Length > 0)
                {
                    int valor = valores[0];

                    // Actualizar el label con el valor leído
                    lblModbusValorLeido.Text = $"Valor: {valor}";
                    lblModbusValorLeido.ForeColor = Color.LimeGreen;

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 MODBUS: {tipoRegistro} {direccion} = {valor} (0x{valor:X4})\n");

                    // Mostrar MessageBox con el valor leído (según preferencias del usuario)
                    MessageBox.Show(
                        $"✅ VALOR LEÍDO EXITOSAMENTE\n\n" +
                        $"📍 Dirección: {direccion}\n" +
                        $"📊 Tipo: {tipoRegistro}\n" +
                        $"🔢 Valor Decimal: {valor}\n" +
                        $"🔢 Valor Hexadecimal: 0x{valor:X4}\n" +
                        $"🔢 Valor Binario: {Convert.ToString(valor, 2).PadLeft(16, '0')}\n\n" +
                        $"🔌 PLC: Conectado\n" +
                        $"📚 Librería: EasyModbusTCP.NET v5.6.0",
                        "🎉 LECTURA MODBUS EXITOSA",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information
                    );
                }
                else
                {
                    lblModbusValorLeido.Text = "Valor: Sin datos";
                    lblModbusValorLeido.ForeColor = Color.Orange;
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ MODBUS: No se recibieron datos válidos\n");

                    MessageBox.Show(
                        "❌ NO SE RECIBIERON DATOS VÁLIDOS\n\n" +
                        "Posibles causas:\n" +
                        "• Dirección inexistente en el PLC\n" +
                        "• PLC no responde\n" +
                        "• Configuración incorrecta\n" +
                        "• Problema de comunicación",
                        "⚠️ ERROR DE LECTURA MODBUS",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                lblModbusValorLeido.Text = "Valor: ERROR";
                lblModbusValorLeido.ForeColor = Color.Red;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error leyendo registro - {ex.Message}\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show(
                    $"❌ ERROR AL LEER REGISTRO MODBUS\n\n" +
                    $"📍 Dirección solicitada: {txtModbusDireccionRegistro.Text}\n" +
                    $"🔌 Estado conexión: {(modbusClient?.Connected == true ? "Conectado" : "Desconectado")}\n\n" +
                    $"💥 Error detallado:\n{ex.Message}\n\n" +
                    $"🔧 Soluciones:\n" +
                    $"• Verificar que el PLC esté conectado\n" +
                    $"• Comprobar la dirección del registro\n" +
                    $"• Verificar Device ID (actual: {nudModbusDeviceId.Value})\n" +
                    $"• Revisar configuración de red",
                    "💥 ERROR DE COMUNICACIÓN MODBUS",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// Evento del botón Escribir Registro Modbus
        /// </summary>
        private async void btnModbusEscribirRegistro_Click(object sender, EventArgs e)
        {
            try
            {
                if (modbusClient == null || !modbusClient.Connected)
                {
                    MessageBox.Show("❌ No hay conexión Modbus activa", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtModbusDireccionRegistro.Text))
                {
                    MessageBox.Show("❌ Ingrese la dirección del registro", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtModbusValorEscritura.Text))
                {
                    MessageBox.Show("❌ Ingrese el valor a escribir", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Convertir dirección
                int direccion = ConvertirDireccionModbus(txtModbusDireccionRegistro.Text.Trim());
                if (direccion == -1)
                {
                    MessageBox.Show("❌ Dirección de registro inválida\n\nEjemplos válidos:\n- MW430 (se convierte a 430)\n- 430 (dirección directa)\n- 40431 (formato Modbus)",
                        "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!int.TryParse(txtModbusValorEscritura.Text, out int valor) || valor < 0 || valor > 65535)
                {
                    MessageBox.Show("❌ Valor de escritura inválido (debe ser entre 0 y 65535)", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✏️ MODBUS: Escribiendo registro {direccion} = {valor}...\n");
                rtbLog.ScrollToCaret();

                // Escribir registro con timeout
                var writeTask = Task.Run(() => modbusClient.WriteSingleRegister(direccion, valor));
                var timeoutTask = Task.Delay(5000); // 5 segundos timeout

                var completedTask = await Task.WhenAny(writeTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    throw new TimeoutException("Timeout escribiendo registro");
                }

                await writeTask; // Esperar a que termine la escritura

                // Actualizar el label para mostrar que se escribió correctamente
                lblModbusValorLeido.Text = $"Escrito: {valor}";
                lblModbusValorLeido.ForeColor = Color.Cyan;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Registro {direccion} escrito exitosamente\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 💡 RECOMENDACIÓN: Leer el registro para verificar la escritura\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show(
                    $"✅ VALOR ESCRITO EXITOSAMENTE\n\n" +
                    $"📍 Dirección: {direccion}\n" +
                    $"🔢 Valor: {valor}\n" +
                    $"📚 Librería: EasyModbusTCP.NET v5.6.0\n\n" +
                    $"💡 Recomendación: Lea el registro para verificar que se escribió correctamente",
                    "🎉 ESCRITURA MODBUS EXITOSA",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
            }
            catch (Exception ex)
            {
                lblModbusValorLeido.Text = "Valor: ERROR";
                lblModbusValorLeido.ForeColor = Color.Red;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ MODBUS: Error escribiendo registro - {ex.Message}\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show(
                    $"❌ ERROR AL ESCRIBIR REGISTRO MODBUS\n\n" +
                    $"📍 Dirección: {txtModbusDireccionRegistro.Text}\n" +
                    $"🔢 Valor: {txtModbusValorEscritura.Text}\n\n" +
                    $"💥 Error detallado:\n{ex.Message}",
                    "💥 ERROR DE ESCRITURA MODBUS",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// Convierte direcciones MW de Schneider a direcciones Modbus
        /// </summary>
        /// <param name="direccionTexto">Dirección como texto (MW430, 430, o 40431)</param>
        /// <returns>Dirección Modbus válida o -1 si es inválida</returns>
        private int ConvertirDireccionModbus(string direccionTexto)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(direccionTexto))
                    return -1;

                direccionTexto = direccionTexto.ToUpper().Trim();

                // Si es formato MW (Memory Word de Schneider)
                if (direccionTexto.StartsWith("MW"))
                {
                    string numeroStr = direccionTexto.Substring(2);
                    if (int.TryParse(numeroStr, out int numeroMW))
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MODBUS: Convertido {direccionTexto} → dirección {numeroMW}\n");
                        return numeroMW;
                    }
                }
                // Si es dirección Modbus con formato 40xxx (convertir a base 0)
                else if (int.TryParse(direccionTexto, out int direccionDirecta))
                {
                    if (direccionDirecta >= 40001 && direccionDirecta <= 49999)
                    {
                        int direccionConvertida = direccionDirecta - 40001;
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MODBUS: Convertido {direccionTexto} → dirección {direccionConvertida}\n");
                        return direccionConvertida;
                    }
                    else if (direccionDirecta >= 0 && direccionDirecta <= 9999)
                    {
                        // Dirección directa (ya en base 0)
                        return direccionDirecta;
                    }
                }

                return -1; // Dirección inválida
            }
            catch
            {
                return -1;
            }
        }

        /// <summary>
        /// Limpia recursos al cerrar la aplicación
        /// </summary>
        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            try
            {
                // Desconectar Modbus si está conectado
                if (modbusClient != null && modbusClient.Connected)
                {
                    modbusClient.Disconnect();
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MODBUS: Desconectado al cerrar aplicación\n");
                }

                // Desconectar MQTT si está conectado
                if (mqtt != null)
                {
                    mqtt.Desconectar();
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 MQTT: Desconectado al cerrar aplicación\n");
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 👋 APLICACIÓN: Cerrando correctamente\n");
            }
            catch (Exception ex)
            {
                // Log del error pero no mostrar MessageBox al cerrar
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error al cerrar: {ex.Message}\n");
            }
            finally
            {
                base.OnFormClosed(e);
            }
        }

        #endregion

        #region Sistema de Reconexión Automática

        /// <summary>
        /// Inicializa el sistema de reconexión automática
        /// </summary>
        private void InicializarSistemaReconexion()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 RECONEXIÓN: Sistema de reconexión automática inicializado\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⏱️ RECONEXIÓN: Timer configurado cada {timer1.Interval}ms\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error inicializando sistema de reconexión: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Carga la configuración Modbus desde archivo
        /// </summary>
        private void CargarConfiguracionModbus()
        {
            try
            {
                ultimaConfigModbus = ConfiguracionModbus.CargarDesdeArchivo();

                // Aplicar configuración a los controles del formulario
                txtModbusIp.Text = ultimaConfigModbus.Ip;
                nudModbusPuerto.Value = ultimaConfigModbus.Puerto;
                nudModbusDeviceId.Value = ultimaConfigModbus.DeviceId;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Configuración cargada desde archivo\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📋 MODBUS: {ultimaConfigModbus}\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error cargando configuración Modbus: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Verifica el estado de las conexiones y reconecta si es necesario de forma robusta
        /// </summary>
        private void VerificarYReconectarServicios()
        {
            try
            {
                // Verificar y reconectar MQTT de forma segura
                Task.Run(async () =>
                {
                    try
                    {
                        await VerificarReconexionMQTTSafe();
                    }
                    catch (Exception ex)
                    {
                        LogSafe($"⚠️ Error en verificación MQTT: {ex.Message}");
                    }
                });

                // Verificar y reconectar Modbus de forma segura
                Task.Run(async () =>
                {
                    try
                    {
                        await VerificarReconexionModbusSafe();
                    }
                    catch (Exception ex)
                    {
                        LogSafe($"⚠️ Error en verificación Modbus: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                // Error crítico en el sistema de verificación
                LogSafe($"❌ Error crítico en sistema de reconexión: {ex.Message}");
            }
        }

        /// <summary>
        /// Verifica y reconecta MQTT si es necesario de forma segura
        /// </summary>
        private async Task VerificarReconexionMQTTSafe()
        {
            try
            {
                // Solo intentar reconexión si está habilitada y tenemos configuración válida
                if (!reconexionMQTTHabilitada || ultimaConfigMQTT == null)
                    return;

                // Validación adicional de configuración
                if (!ultimaConfigMQTT.EsValida() || string.IsNullOrWhiteSpace(ultimaConfigMQTT.Host))
                    return;

                // Verificar si MQTT está desconectado de forma segura
                bool mqttDesconectado = false;
                try
                {
                    mqttDesconectado = mqtt == null || !MQTTConectado;
                }
                catch (Exception ex)
                {
                    LogSafe($"⚠️ MQTT: Error verificando estado de conexión: {ex.Message}");
                    mqttDesconectado = true; // Asumir desconectado si hay error
                }

                if (mqttDesconectado)
                {
                    // Solo intentar reconectar si se ha conectado exitosamente antes
                    if (!mqttConectadoAlgunaVez)
                    {
                        // No intentar reconexión automática si nunca se ha conectado
                        return;
                    }

                    // Verificar si ha pasado suficiente tiempo desde el último intento
                    var tiempoTranscurrido = DateTime.Now - ultimoIntentoReconexionMQTT;
                    if (tiempoTranscurrido.TotalSeconds >= 30) // Intentar cada 30 segundos
                    {
                        ultimoIntentoReconexionMQTT = DateTime.Now;

                        LogSafe($"🔄 MQTT: Iniciando intento de reconexión #{contadorReconexionesMQTT + 1}");

                        // Intentar reconexión de forma asíncrona
                        await IntentarReconexionMQTT();
                    }
                }
            }
            catch (Exception ex)
            {
                LogSafe($"⚠️ MQTT: Error en verificación de reconexión: {ex.Message}");
            }
        }

        /// <summary>
        /// Verifica y reconecta MQTT si es necesario (método legacy para compatibilidad)
        /// </summary>
        private void VerificarReconexionMQTT()
        {
            Task.Run(async () => await VerificarReconexionMQTTSafe());
        }

        /// <summary>
        /// Verifica y reconecta Modbus si es necesario de forma segura
        /// </summary>
        private async Task VerificarReconexionModbusSafe()
        {
            try
            {
                // Solo intentar reconexión si está habilitada y tenemos configuración válida
                if (!reconexionModbusHabilitada || ultimaConfigModbus == null)
                    return;

                // Validación adicional de configuración
                if (!ultimaConfigModbus.EsValida() || string.IsNullOrWhiteSpace(ultimaConfigModbus.Ip))
                    return;

                // Verificar si Modbus está desconectado de forma segura
                bool modbusDesconectado = false;
                try
                {
                    modbusDesconectado = modbusClient == null || !modbusConectado;

                    // Verificación adicional del estado de conexión si el cliente existe
                    if (modbusClient != null && modbusConectado)
                    {
                        try
                        {
                            // Verificar si realmente está conectado
                            modbusDesconectado = !modbusClient.Connected;
                        }
                        catch (Exception ex)
                        {
                            LogSafe($"⚠️ MODBUS: Error verificando estado Connected: {ex.Message}");
                            modbusDesconectado = true; // Asumir desconectado si hay error
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogSafe($"⚠️ MODBUS: Error verificando estado de conexión: {ex.Message}");
                    modbusDesconectado = true; // Asumir desconectado si hay error
                }

                if (modbusDesconectado)
                {
                    // Solo intentar reconectar si se ha conectado exitosamente antes
                    if (!modbusConectadoAlgunaVez)
                    {
                        // No intentar reconexión automática si nunca se ha conectado
                        return;
                    }

                    // Verificar si ha pasado suficiente tiempo desde el último intento
                    var tiempoTranscurrido = DateTime.Now - ultimoIntentoReconexionModbus;
                    var intervaloReconexion = Math.Max(10, ultimaConfigModbus.IntervaloReconexion); // Mínimo 10 segundos

                    if (tiempoTranscurrido.TotalSeconds >= intervaloReconexion)
                    {
                        ultimoIntentoReconexionModbus = DateTime.Now;

                        LogSafe($"🔄 MODBUS: Iniciando intento de reconexión #{contadorReconexionesModbus + 1}");

                        // Intentar reconexión de forma asíncrona
                        await IntentarReconexionModbus();
                    }
                }
            }
            catch (Exception ex)
            {
                LogSafe($"⚠️ MODBUS: Error en verificación de reconexión: {ex.Message}");
            }
        }

        /// <summary>
        /// Verifica y reconecta Modbus si es necesario (método legacy para compatibilidad)
        /// </summary>
        private void VerificarReconexionModbus()
        {
            Task.Run(async () => await VerificarReconexionModbusSafe());
        }

        /// <summary>
        /// Guarda la configuración MQTT válida para reconexión automática
        /// </summary>
        private void GuardarConfiguracionMQTTValida()
        {
            try
            {
                if (!int.TryParse(txtMqttPuerto.Text.Trim(), out int port))
                    return;

                ultimaConfigMQTT = new ConfiguracionMQTT
                {
                    Protocolo = cmbMqttProtocolo.SelectedItem?.ToString() ?? "mqtts://",
                    Host = txtMqttHost.Text.Trim(),
                    Puerto = port,
                    ClientId = txtMqttClientId.Text.Trim(),
                    Usuario = txtMqttUsuario.Text.Trim(),
                    Password = txtMqttPassword.Text,
                    UsarSslTls = chkMqttSslTls.Checked,
                    CertificadoCA = true
                };

                // Guardar en archivo para persistencia
                ConfiguracionMQTT.GuardarEnArchivo(ultimaConfigMQTT);

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 💾 MQTT: Configuración válida guardada para reconexión automática\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error guardando configuración MQTT: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Guarda la configuración Modbus válida para reconexión automática
        /// </summary>
        private void GuardarConfiguracionModbusValida()
        {
            try
            {
                ultimaConfigModbus = new ConfiguracionModbus
                {
                    Ip = txtModbusIp.Text.Trim(),
                    Puerto = (int)nudModbusPuerto.Value,
                    DeviceId = (byte)nudModbusDeviceId.Value,
                    Timeout = 5000,
                    ReconexionAutomatica = true,
                    IntervaloReconexion = 10
                };

                // Guardar en archivo para persistencia
                ConfiguracionModbus.GuardarEnArchivo(ultimaConfigModbus);

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 💾 MODBUS: Configuración válida guardada para reconexión automática\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ Error guardando configuración Modbus: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Intenta reconectar MQTT de forma asíncrona con manejo robusto de excepciones
        /// </summary>
        private async Task IntentarReconexionMQTT()
        {
            ConexionMQTT? nuevoMqtt = null;

            try
            {
                if (ultimaConfigMQTT == null)
                {
                    LogSafe("❌ MQTT: No hay configuración válida para reconexión");
                    return;
                }

                contadorReconexionesMQTT++;

                // Validar configuración antes de crear conexión
                if (string.IsNullOrWhiteSpace(ultimaConfigMQTT.Host) || ultimaConfigMQTT.Puerto <= 0)
                {
                    LogSafe($"❌ MQTT: Configuración inválida - Host: '{ultimaConfigMQTT.Host}', Puerto: {ultimaConfigMQTT.Puerto}");
                    return;
                }

                // Crear nueva conexión MQTT
                nuevoMqtt = new ConexionMQTT();

                // Conectar con timeout robusto
                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)))
                {
                    try
                    {
                        bool conectado = await Task.Run(async () =>
                        {
                            cts.Token.ThrowIfCancellationRequested();
                            return await nuevoMqtt.ConectarAsync(
                                ultimaConfigMQTT.Protocolo,
                                ultimaConfigMQTT.Host,
                                ultimaConfigMQTT.Puerto,
                                ultimaConfigMQTT.ClientId,
                                ultimaConfigMQTT.Usuario,
                                ultimaConfigMQTT.Password,
                                ultimaConfigMQTT.UsarSslTls,
                                ultimaConfigMQTT.CertificadoCA,
                                false
                            );
                        }, cts.Token);

                        if (conectado)
                        {
                            // Actualizar UI de forma segura
                            await ActualizarUIMQTTReconectado(nuevoMqtt);
                        }
                        else
                        {
                            throw new InvalidOperationException("Conexión MQTT falló - estado no conectado");
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        throw new TimeoutException("Timeout en conexión MQTT después de 30 segundos");
                    }
                }
            }
            catch (TimeoutException tex)
            {
                LogSafe($"⏱️ MQTT: Timeout en reconexión #{contadorReconexionesMQTT}: {tex.Message}");
                await LimpiarMQTTClientSafe(nuevoMqtt);
            }
            catch (System.Net.Sockets.SocketException sex)
            {
                LogSafe($"🌐 MQTT: Error de red en reconexión #{contadorReconexionesMQTT}: {sex.Message}");
                await LimpiarMQTTClientSafe(nuevoMqtt);
            }
            catch (MQTTnet.Exceptions.MqttCommunicationException mex)
            {
                LogSafe($"📡 MQTT: Error de comunicación en reconexión #{contadorReconexionesMQTT}: {mex.Message}");
                await LimpiarMQTTClientSafe(nuevoMqtt);
            }
            catch (Exception ex)
            {
                LogSafe($"❌ MQTT: Error inesperado en reconexión #{contadorReconexionesMQTT}: {ex.GetType().Name} - {ex.Message}");
                await LimpiarMQTTClientSafe(nuevoMqtt);
            }
        }

        /// <summary>
        /// Limpia un cliente MQTT de forma segura
        /// </summary>
        private async Task LimpiarMQTTClientSafe(ConexionMQTT? cliente)
        {
            if (cliente != null)
            {
                try
                {
                    await Task.Run(() =>
                    {
                        cliente.Desconectar();
                    });
                }
                catch (Exception ex)
                {
                    LogSafe($"⚠️ MQTT: Error al limpiar cliente: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Actualiza la UI cuando MQTT se reconecta exitosamente
        /// </summary>
        private async Task ActualizarUIMQTTReconectado(ConexionMQTT nuevoCliente)
        {
            try
            {
                if (this.InvokeRequired)
                {
                    await Task.Run(() =>
                    {
                        this.Invoke(new Action(() => ActualizarUIMQTTReconectadoSync(nuevoCliente)));
                    });
                }
                else
                {
                    ActualizarUIMQTTReconectadoSync(nuevoCliente);
                }
            }
            catch (Exception ex)
            {
                LogSafe($"❌ MQTT: Error actualizando UI: {ex.Message}");
            }
        }

        /// <summary>
        /// Actualiza la UI de forma síncrona para MQTT (debe ejecutarse en el hilo principal)
        /// </summary>
        private void ActualizarUIMQTTReconectadoSync(ConexionMQTT nuevoCliente)
        {
            try
            {
                mqtt = nuevoCliente;
                MQTTConectado = true;
                mqttConectadoAlgunaVez = true; // Marcar reconexión exitosa
                lblEstadoMqtt.Text = "MQTT: Reconectado ✅";
                lblEstadoMqtt.ForeColor = Color.LimeGreen;
                btnMqttConectar.Enabled = false;
                btnMqttDesconectar.Enabled = true;

                if (btnRefrescarMQTT != null)
                    btnRefrescarMQTT.Enabled = true;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MQTT: Reconectado exitosamente (intento #{contadorReconexionesMQTT})\n");
                rtbLog.ScrollToCaret();

                // Suscribir topics nuevamente de forma segura
                Task.Run(async () =>
                {
                    try
                    {
                        await SuscribirTopicsDelCSV();
                        await SuscribirTopicsSinoptico();
                    }
                    catch (Exception ex)
                    {
                        LogSafe($"⚠️ MQTT: Error suscribiendo topics tras reconexión: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                LogSafe($"❌ MQTT: Error en actualización UI síncrona: {ex.Message}");
            }
        }

        /// <summary>
        /// Verifica si una IP es alcanzable mediante ping
        /// </summary>
        /// <param name="ip">IP a verificar</param>
        /// <param name="timeoutMs">Timeout en milisegundos</param>
        /// <returns>True si la IP es alcanzable</returns>
        private async Task<bool> VerificarConectividadIP(string ip, int timeoutMs = 3000)
        {
            try
            {
                using (var ping = new System.Net.NetworkInformation.Ping())
                {
                    var reply = await ping.SendPingAsync(ip, timeoutMs);
                    return reply.Status == System.Net.NetworkInformation.IPStatus.Success;
                }
            }
            catch (Exception ex)
            {
                LogSafe($"⚠️ PING: Error verificando conectividad a {ip}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Intenta reconectar Modbus de forma asíncrona con manejo robusto de excepciones
        /// </summary>
        private async Task IntentarReconexionModbus()
        {
            ModbusClient? nuevoModbusClient = null;

            try
            {
                if (ultimaConfigModbus == null)
                {
                    LogSafe("❌ MODBUS: No hay configuración válida para reconexión");
                    return;
                }

                contadorReconexionesModbus++;

                // Desconectar cliente existente de forma segura
                await DesconectarModbusSafe();

                // Validar configuración antes de crear conexión
                if (string.IsNullOrWhiteSpace(ultimaConfigModbus.Ip) || ultimaConfigModbus.Puerto <= 0)
                {
                    LogSafe($"❌ MODBUS: Configuración inválida - IP: '{ultimaConfigModbus.Ip}', Puerto: {ultimaConfigModbus.Puerto}");
                    return;
                }

                // Verificar conectividad de red antes de intentar conexión Modbus
                LogSafe($"🔍 MODBUS: Verificando conectividad a {ultimaConfigModbus.Ip}...");
                bool ipAlcanzable = await VerificarConectividadIP(ultimaConfigModbus.Ip, 3000);

                if (!ipAlcanzable)
                {
                    LogSafe($"🌐 MODBUS: IP {ultimaConfigModbus.Ip} no es alcanzable - cancelando reconexión #{contadorReconexionesModbus}");
                    return;
                }

                // Crear nueva conexión Modbus con validación
                nuevoModbusClient = new ModbusClient(ultimaConfigModbus.Ip, ultimaConfigModbus.Puerto);
                nuevoModbusClient.UnitIdentifier = ultimaConfigModbus.DeviceId;
                nuevoModbusClient.ConnectionTimeout = Math.Max(1000, Math.Min(ultimaConfigModbus.Timeout, 15000)); // Entre 1-15 segundos

                // Conectar con timeout robusto y cancelación
                using (var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(ultimaConfigModbus.Timeout + 5000)))
                {
                    try
                    {
                        await Task.Run(() =>
                        {
                            cts.Token.ThrowIfCancellationRequested();
                            nuevoModbusClient.Connect();
                        }, cts.Token);

                        // Verificar conexión exitosa
                        if (nuevoModbusClient.Connected)
                        {
                            // Actualizar UI de forma segura
                            await ActualizarUIModbusReconectado(nuevoModbusClient);
                        }
                        else
                        {
                            throw new InvalidOperationException("Conexión Modbus falló - estado no conectado");
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        throw new TimeoutException($"Timeout en conexión Modbus después de {ultimaConfigModbus.Timeout + 5000}ms");
                    }
                }
            }
            catch (TimeoutException tex)
            {
                LogSafe($"⏱️ MODBUS: Timeout en reconexión #{contadorReconexionesModbus}: {tex.Message}");
                await LimpiarModbusClientSafe(nuevoModbusClient);
            }
            catch (System.Net.Sockets.SocketException sex)
            {
                LogSafe($"🌐 MODBUS: Error de red en reconexión #{contadorReconexionesModbus}: {sex.Message}");
                await LimpiarModbusClientSafe(nuevoModbusClient);
            }
            catch (EasyModbus.Exceptions.ConnectionException cex)
            {
                LogSafe($"🔌 MODBUS: Error de conexión en reconexión #{contadorReconexionesModbus}: {cex.Message}");
                await LimpiarModbusClientSafe(nuevoModbusClient);
            }
            catch (Exception ex)
            {
                LogSafe($"❌ MODBUS: Error inesperado en reconexión #{contadorReconexionesModbus}: {ex.GetType().Name} - {ex.Message}");
                await LimpiarModbusClientSafe(nuevoModbusClient);
            }
        }

        /// <summary>
        /// Desconecta el cliente Modbus de forma segura
        /// </summary>
        private async Task DesconectarModbusSafe()
        {
            if (modbusClient != null)
            {
                try
                {
                    await Task.Run(() =>
                    {
                        if (modbusClient.Connected)
                        {
                            modbusClient.Disconnect();
                        }
                    });
                }
                catch (Exception ex)
                {
                    LogSafe($"⚠️ MODBUS: Error al desconectar cliente anterior: {ex.Message}");
                }
                finally
                {
                    modbusClient = null;
                }
            }
        }

        /// <summary>
        /// Limpia un cliente Modbus de forma segura
        /// </summary>
        private async Task LimpiarModbusClientSafe(ModbusClient? cliente)
        {
            if (cliente != null)
            {
                try
                {
                    await Task.Run(() =>
                    {
                        if (cliente.Connected)
                        {
                            cliente.Disconnect();
                        }
                    });
                }
                catch (Exception ex)
                {
                    LogSafe($"⚠️ MODBUS: Error al limpiar cliente: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Actualiza la UI cuando Modbus se reconecta exitosamente
        /// </summary>
        private async Task ActualizarUIModbusReconectado(ModbusClient nuevoCliente)
        {
            try
            {
                if (this.InvokeRequired)
                {
                    await Task.Run(() =>
                    {
                        this.Invoke(new Action(() => ActualizarUIModbusReconectadoSync(nuevoCliente)));
                    });
                }
                else
                {
                    ActualizarUIModbusReconectadoSync(nuevoCliente);
                }
            }
            catch (Exception ex)
            {
                LogSafe($"❌ MODBUS: Error actualizando UI: {ex.Message}");
            }
        }

        /// <summary>
        /// Actualiza la UI de forma síncrona (debe ejecutarse en el hilo principal)
        /// </summary>
        private void ActualizarUIModbusReconectadoSync(ModbusClient nuevoCliente)
        {
            try
            {
                modbusClient = nuevoCliente;
                modbusConectado = true;
                lblEstadoModbus.Text = "🟢 Modbus: Reconectado";
                lblEstadoModbus.ForeColor = Color.LimeGreen;
                btnModbusConectar.Enabled = false;
                btnModbusDesconectar.Enabled = true;
                btnModbusLeerRegistros.Enabled = true;
                btnModbusEscribirRegistro.Enabled = true;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ MODBUS: Reconectado exitosamente (intento #{contadorReconexionesModbus})\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                LogSafe($"❌ MODBUS: Error en actualización UI síncrona: {ex.Message}");
            }
        }

        /// <summary>
        /// Registra mensajes de forma segura en el log
        /// </summary>
        private void LogSafe(string mensaje)
        {
            try
            {
                if (this.InvokeRequired)
                {
                    this.BeginInvoke(new Action(() => LogSafeSync(mensaje)));
                }
                else
                {
                    LogSafeSync(mensaje);
                }
            }
            catch (Exception ex)
            {
                // Si falla el logging, al menos intentar escribir en consola
                System.Diagnostics.Debug.WriteLine($"Error en LogSafe: {ex.Message} | Mensaje original: {mensaje}");
            }
        }

        /// <summary>
        /// Registra mensajes de forma síncrona (debe ejecutarse en el hilo principal)
        /// </summary>
        private void LogSafeSync(string mensaje)
        {
            try
            {
                if (rtbLog != null && !rtbLog.IsDisposed)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} {mensaje}\n");
                    rtbLog.ScrollToCaret();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error en LogSafeSync: {ex.Message}");
            }
        }

        #endregion

        #region GESTIÓN DE CORREO ELECTRÓNICO

        /// <summary>
        /// Inicializa la configuración de correo
        /// </summary>
        private void InicializarConfiguracionCorreo()
        {
            try
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔧 CORREO: Iniciando configuración...\n");

                configuracionCorreo = new ConfiguracionCorreo();
                gestorCorreo = new GestorDeCorreo(configuracionCorreo);

                // Suscribirse a eventos del gestor
                gestorCorreo.LimitacionesActualizadas += GestorCorreo_LimitacionesActualizadas;
                gestorCorreo.CambioDeHoraEjecutado += GestorCorreo_CambioDeHoraEjecutado;

                // Estado inicial
                ActualizarEstadoConexionCorreo(false);

                // Actualizar DataGridView con estados iniciales
                ActualizarDataGridViewEstados();

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ CORREO: Configuración inicializada correctamente\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 CORREO: Timers automáticos configurados\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error inicializando configuración: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza el estado visual de la conexión de correo
        /// </summary>
        /// <param name="conectado">Estado de conexión</param>
        private void ActualizarEstadoConexionCorreo(bool conectado)
        {
            try
            {
                correoConectado = conectado;

                if (conectado)
                {
                    lblEstadoCorreo.Text = "Correo: Conectado";
                    lblEstadoCorreo.ForeColor = Color.LimeGreen;
                    panelEstadoCorreo.BackColor = Color.LimeGreen;

                    btnConectarCorreo.Enabled = false;
                    btnDesconectarCorreo.Enabled = true;
                    btnProcesarCorreos.Enabled = true;

                    btnConectarCorreo.Text = "Conectado";
                    btnConectarCorreo.BackColor = Color.LimeGreen;

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ CORREO: Estado actualizado - Conectado\n");
                    rtbLog.ScrollToCaret();
                }
                else
                {
                    lblEstadoCorreo.Text = "Correo: Desconectado";
                    lblEstadoCorreo.ForeColor = Color.White;
                    panelEstadoCorreo.BackColor = Color.Gray;

                    btnConectarCorreo.Enabled = true;
                    btnDesconectarCorreo.Enabled = false;
                    btnProcesarCorreos.Enabled = false;

                    btnConectarCorreo.Text = "Conectar";
                    btnConectarCorreo.BackColor = Color.FromArgb(70, 130, 180);

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚪ CORREO: Estado actualizado - Desconectado\n");
                    rtbLog.ScrollToCaret();
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error actualizando estado: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Carga la configuración de correo desde archivo
        /// </summary>
        private void CargarConfiguracionCorreo()
        {
            try
            {
                configuracionCorreo = ConfiguracionCorreo.CargarConfiguracion();

                // Cargar valores en la interfaz
                txtEwsServerUrl.Text = configuracionCorreo.EwsServerUrl;
                txtEwsUsuario.Text = configuracionCorreo.EwsUsername;
                txtEwsPassword.Text = configuracionCorreo.EwsPassword;
                txtEwsDominio.Text = configuracionCorreo.EwsDomain;
                txtEmailNotificacion.Text = configuracionCorreo.EmailNotification;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ CORREO: Configuración cargada correctamente\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error cargando configuración: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Guarda la configuración de correo en archivo
        /// </summary>
        private void GuardarConfiguracionCorreo()
        {
            try
            {
                if (configuracionCorreo == null)
                    configuracionCorreo = new ConfiguracionCorreo();

                // Actualizar configuración con valores de la interfaz
                configuracionCorreo.EwsServerUrl = txtEwsServerUrl.Text.Trim();
                configuracionCorreo.EwsUsername = txtEwsUsuario.Text.Trim();
                configuracionCorreo.EwsPassword = txtEwsPassword.Text;
                configuracionCorreo.EwsDomain = txtEwsDominio.Text.Trim();
                configuracionCorreo.EmailNotification = txtEmailNotificacion.Text.Trim();

                configuracionCorreo.GuardarConfiguracion();

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ CORREO: Configuración guardada correctamente\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error guardando configuración: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Inicializa el DataGridView de estados horarios
        /// </summary>
        private void InicializarDataGridViewEstadosHorarios()
        {
            try
            {
                if (dgvEstadosHorarios == null)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ CORREO: DataGridView no inicializado\n");
                    return;
                }

                dgvEstadosHorarios.Columns.Clear();

                // Configurar columnas
                dgvEstadosHorarios.Columns.Add("Hora", "Hora");
                dgvEstadosHorarios.Columns.Add("Estado", "Estado");
                dgvEstadosHorarios.Columns.Add("Descripcion", "Descripción");

                // Configurar ancho de columnas
                dgvEstadosHorarios.Columns["Hora"].Width = 80;
                dgvEstadosHorarios.Columns["Estado"].Width = 120;
                dgvEstadosHorarios.Columns["Descripcion"].Width = 280;

                // Llenar con datos iniciales (24 horas)
                ActualizarDataGridViewEstadosHorarios(new bool[24]);
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error inicializando grid estados: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza el DataGridView con los estados horarios actuales
        /// </summary>
        private void ActualizarDataGridViewEstadosHorarios(bool[] estados)
        {
            try
            {
                if (InvokeRequired)
                {
                    Invoke(new Action(() => ActualizarDataGridViewEstadosHorarios(estados)));
                    return;
                }

                dgvEstadosHorarios.Rows.Clear();

                for (int i = 0; i < 24; i++)
                {
                    string hora = $"H{i + 1:00} ({i + 1:00}:00)";
                    string estado = estados[i] ? "RESTRINGIDA" : "Disponible";
                    string descripcion = estados[i] ? "Hora con restricciones activas" : "Hora sin restricciones";

                    int rowIndex = dgvEstadosHorarios.Rows.Add(hora, estado, descripcion);

                    // Colorear filas según estado
                    if (estados[i])
                    {
                        dgvEstadosHorarios.Rows[rowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 200, 200);
                        dgvEstadosHorarios.Rows[rowIndex].DefaultCellStyle.ForeColor = Color.DarkRed;
                    }
                    else
                    {
                        dgvEstadosHorarios.Rows[rowIndex].DefaultCellStyle.BackColor = Color.FromArgb(200, 255, 200);
                        dgvEstadosHorarios.Rows[rowIndex].DefaultCellStyle.ForeColor = Color.DarkGreen;
                    }
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error actualizando grid estados: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Inicializa los eventos de los controles de correo
        /// </summary>
        private void InicializarEventosCorreo()
        {
            try
            {
                if (btnConectarCorreo != null)
                    btnConectarCorreo.Click += BtnConectarCorreo_Click;
                if (btnDesconectarCorreo != null)
                    btnDesconectarCorreo.Click += BtnDesconectarCorreo_Click;
                if (btnProcesarCorreos != null)
                    btnProcesarCorreos.Click += BtnProcesarCorreos_Click;
                if (btnForzarCambioHora != null)
                    btnForzarCambioHora.Click += BtnForzarCambioHora_Click;

                // Eventos de cambio de texto para guardar configuración automáticamente
                if (txtEwsServerUrl != null)
                    txtEwsServerUrl.TextChanged += (s, e) => GuardarConfiguracionCorreo();
                if (txtEwsUsuario != null)
                    txtEwsUsuario.TextChanged += (s, e) => GuardarConfiguracionCorreo();
                if (txtEwsPassword != null)
                    txtEwsPassword.TextChanged += (s, e) => GuardarConfiguracionCorreo();
                if (txtEwsDominio != null)
                    txtEwsDominio.TextChanged += (s, e) => GuardarConfiguracionCorreo();
                if (txtEmailNotificacion != null)
                    txtEmailNotificacion.TextChanged += (s, e) => GuardarConfiguracionCorreo();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error inicializando eventos: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Event handler para conectar correo
        /// </summary>
        private async void BtnConectarCorreo_Click(object sender, EventArgs e)
        {
            try
            {
                btnConectarCorreo.Enabled = false;
                btnConectarCorreo.Text = "Conectando...";

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 CORREO: Iniciando conexión EWS...\n");
                rtbLog.ScrollToCaret();

                // Guardar configuración actual
                GuardarConfiguracionCorreo();

                // Crear gestor de correo
                gestorCorreo = new GestorDeCorreo(configuracionCorreo);

                // Probar conexión
                bool conexionExitosa = await gestorCorreo.ProbarConexionAsync();

                if (conexionExitosa)
                {
                    correoConectado = true;
                    btnConectarCorreo.Text = "Conectado";
                    btnConectarCorreo.BackColor = Color.FromArgb(0, 204, 122);
                    btnDesconectarCorreo.Enabled = true;
                    btnProcesarCorreos.Enabled = true;
                    btnForzarCambioHora.Enabled = true;

                    // Iniciar revisión automática de correos
                    gestorCorreo.IniciarRevisionAutomatica();

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ CORREO: Conexión EWS establecida correctamente\n");
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 CORREO: Revisión automática iniciada (cada 5 minutos)\n");
                    rtbLog.ScrollToCaret();

                    // Actualizar indicador de estado
                    lblEstadoCorreo.Text = "Conectado";
                    lblEstadoCorreo.ForeColor = Color.LimeGreen;
                    panelEstadoCorreo.BackColor = Color.LimeGreen;
                }
                else
                {
                    throw new Exception("No se pudo establecer conexión con el servidor EWS");
                }
            }
            catch (Exception ex)
            {
                correoConectado = false;
                btnConectarCorreo.Text = "Conectar";
                btnConectarCorreo.BackColor = Color.FromArgb(0, 122, 204);
                btnDesconectarCorreo.Enabled = false;
                btnProcesarCorreos.Enabled = false;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error de conexión: {ex.Message}\n");
                rtbLog.ScrollToCaret();

                // Actualizar indicador de estado
                lblEstadoCorreo.Text = "Error";
                lblEstadoCorreo.ForeColor = Color.Red;
                panelEstadoCorreo.BackColor = Color.Red;

                MessageBox.Show($"Error conectando al servidor EWS:\n{ex.Message}", "Error de Conexión",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnConectarCorreo.Enabled = true;
            }
        }

        /// <summary>
        /// Event handler para desconectar correo
        /// </summary>
        private void BtnDesconectarCorreo_Click(object sender, EventArgs e)
        {
            try
            {
                if (gestorCorreo != null)
                {
                    // Detener revisión automática
                    gestorCorreo.DetenerRevisionAutomatica();
                    gestorCorreo.Dispose();
                    gestorCorreo = null;
                }

                correoConectado = false;
                btnConectarCorreo.Text = "Conectar";
                btnConectarCorreo.BackColor = Color.FromArgb(0, 122, 204);
                btnDesconectarCorreo.Enabled = false;
                btnProcesarCorreos.Enabled = false;
                btnForzarCambioHora.Enabled = false;

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔌 CORREO: Desconectado del servidor EWS\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⏹️ CORREO: Revisión automática detenida\n");
                rtbLog.ScrollToCaret();

                // Actualizar indicador de estado
                lblEstadoCorreo.Text = "Desconectado";
                lblEstadoCorreo.ForeColor = Color.Gray;
                panelEstadoCorreo.BackColor = Color.Gray;
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error desconectando: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Event handler para procesar correos
        /// </summary>
        private async void BtnProcesarCorreos_Click(object sender, EventArgs e)
        {
            try
            {
                if (!correoConectado || gestorCorreo == null)
                {
                    MessageBox.Show("Debe conectarse primero al servidor EWS", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                btnProcesarCorreos.Enabled = false;
                btnProcesarCorreos.Text = "Procesando...";

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 CORREO: Iniciando procesamiento de correos...\n");
                rtbLog.ScrollToCaret();

                // Procesar correos
                var resultado = await gestorCorreo.ConectarYProcesarCorreosAsync();

                // Mostrar resultados
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📊 CORREO: {resultado.ResumenProcesamiento}\n");

                if (resultado.TieneErrores)
                {
                    foreach (var error in resultado.ErroresProcesamiento)
                    {
                        rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ CORREO: {error}\n");
                    }
                }

                // Actualizar grid con estados horarios actuales
                var estadosActuales = gestorCorreo.ObtenerEstadosActuales();
                ActualizarDataGridViewEstadosHorarios(estadosActuales);

                // Mostrar resumen de horas
                var horasConLimitaciones = gestorCorreo.ObtenerHorasConLimitaciones();
                var horasSinLimitaciones = gestorCorreo.ObtenerHorasSinLimitaciones();

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🕐 CORREO: Horas CON limitaciones: {string.Join(", ", horasConLimitaciones)}\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🕐 CORREO: Horas SIN limitaciones: {string.Join(", ", horasSinLimitaciones)}\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show($"Procesamiento completado:\n\n" +
                    $"• Correos encontrados: {resultado.TotalCorreosEncontrados}\n" +
                    $"• Correos filtrados: {resultado.CorreosFiltrados}\n" +
                    $"• Correos procesados: {resultado.CorreosProcesados}\n" +
                    $"• Horas con limitaciones: {horasConLimitaciones.Count}\n" +
                    $"• Horas sin limitaciones: {horasSinLimitaciones.Count}",
                    "Procesamiento Completado", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error procesando correos: {ex.Message}\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show($"Error procesando correos:\n{ex.Message}", "Error de Procesamiento",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnProcesarCorreos.Enabled = true;
                btnProcesarCorreos.Text = "Procesar";
            }
        }

        /// <summary>
        /// Event handler para forzar cambio de limitaciones de mañana a hoy (depuración)
        /// </summary>
        private void BtnForzarCambioHora_Click(object sender, EventArgs e)
        {
            try
            {
                if (gestorCorreo == null)
                {
                    MessageBox.Show("El gestor de correo no está inicializado", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Confirmar acción
                var resultado = MessageBox.Show(
                    "¿Está seguro de que desea forzar el cambio de limitaciones de MAÑANA a HOY?\n\n" +
                    "Esta acción:\n" +
                    "• Copiará las limitaciones de mañana a hoy\n" +
                    "• Limpiará las limitaciones de mañana\n" +
                    "• Es irreversible",
                    "Confirmar Cambio de Hora",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (resultado == DialogResult.Yes)
                {
                    gestorCorreo.EjecutarCambioDeHora();

                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 CORREO: Cambio de hora forzado - Mañana → Hoy\n");
                    rtbLog.ScrollToCaret();

                    MessageBox.Show("Cambio de limitaciones ejecutado correctamente", "Cambio Completado",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error en cambio forzado: {ex.Message}\n");
                rtbLog.ScrollToCaret();

                MessageBox.Show($"Error ejecutando cambio de hora:\n{ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Event handler para cuando se actualizan las limitaciones
        /// </summary>
        private void GestorCorreo_LimitacionesActualizadas(object sender, EventArgs e)
        {
            try
            {
                // Actualizar UI en el hilo principal
                if (InvokeRequired)
                {
                    Invoke(new Action(() => GestorCorreo_LimitacionesActualizadas(sender, e)));
                    return;
                }

                ActualizarDataGridViewEstados();

                // Actualizar visualización de limitaciones horarias en el sinóptico
                ActualizarVisualizacionLimitacionesHorarias();

                // Si el modo automático está activo, recalcular valor
                if (modoAutomaticoActivo)
                {
                    Task.Run(async () => await CalcularYEnviarValorModoAutomatico());
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔄 CORREO: Limitaciones actualizadas\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error actualizando UI: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Event handler para cuando se ejecuta el cambio de hora automático
        /// </summary>
        private void GestorCorreo_CambioDeHoraEjecutado(object sender, EventArgs e)
        {
            try
            {
                // Actualizar UI en el hilo principal
                if (InvokeRequired)
                {
                    Invoke(new Action(() => GestorCorreo_CambioDeHoraEjecutado(sender, e)));
                    return;
                }

                ActualizarDataGridViewEstados();
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🕛 CORREO: Cambio automático de hora ejecutado (00:00)\n");
                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error en cambio automático: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Actualiza el DataGridView con los estados actuales de limitaciones
        /// </summary>
        private void ActualizarDataGridViewEstados()
        {
            try
            {
                if (gestorCorreo == null || dgvEstadosHorarios == null)
                    return;

                var limitacionesHoy = gestorCorreo.ObtenerLimitacionesHoy();
                var limitacionesMañana = gestorCorreo.ObtenerLimitacionesMañana();

                // Limpiar y configurar DataGridView
                dgvEstadosHorarios.Rows.Clear();
                if (dgvEstadosHorarios.Columns.Count == 0)
                {
                    dgvEstadosHorarios.Columns.Add("Hora", "Hora");
                    dgvEstadosHorarios.Columns.Add("Hoy", "HOY");
                    dgvEstadosHorarios.Columns.Add("Mañana", "MAÑANA");
                }

                // Agregar filas con estados
                for (int i = 0; i < 24; i++)
                {
                    string hora = $"H{i + 1:00}";
                    string estadoHoy = limitacionesHoy[i] ? "LIMITADA" : "DISPONIBLE";
                    string estadoMañana = limitacionesMañana[i] ? "LIMITADA" : "DISPONIBLE";

                    int rowIndex = dgvEstadosHorarios.Rows.Add(hora, estadoHoy, estadoMañana);

                    // Colorear filas según estado
                    if (limitacionesHoy[i])
                    {
                        dgvEstadosHorarios.Rows[rowIndex].Cells[1].Style.BackColor = Color.FromArgb(220, 53, 69); // Rojo
                        dgvEstadosHorarios.Rows[rowIndex].Cells[1].Style.ForeColor = Color.White;
                    }
                    else
                    {
                        dgvEstadosHorarios.Rows[rowIndex].Cells[1].Style.BackColor = Color.FromArgb(40, 167, 69); // Verde
                        dgvEstadosHorarios.Rows[rowIndex].Cells[1].Style.ForeColor = Color.White;
                    }

                    if (limitacionesMañana[i])
                    {
                        dgvEstadosHorarios.Rows[rowIndex].Cells[2].Style.BackColor = Color.FromArgb(255, 193, 7); // Amarillo
                        dgvEstadosHorarios.Rows[rowIndex].Cells[2].Style.ForeColor = Color.Black;
                    }
                    else
                    {
                        dgvEstadosHorarios.Rows[rowIndex].Cells[2].Style.BackColor = Color.FromArgb(108, 117, 125); // Gris
                        dgvEstadosHorarios.Rows[rowIndex].Cells[2].Style.ForeColor = Color.White;
                    }
                }
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ CORREO: Error actualizando DataGridView: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        /// <summary>
        /// Envía un comando SRAP y actualiza la UI
        /// </summary>
        private async Task EnviarComandoSRAP(string topicComando, string valor, string descripcion)
        {
            try
            {
                if (!sinopticoHabilitado || mqtt == null || !mqtt.EstaConectado)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ⚠️ SRAP: Sistema no habilitado o MQTT desconectado\n");
                    return;
                }

                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 🔧 SRAP: Enviando comando '{descripcion}'\n");
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} 📤 SRAP: Topic: '{topicComando}' → Valor: '{valor}'\n");

                // Publicar comando en el topic correspondiente
                bool enviado = await mqtt.EscribirTopic(topicComando, valor);

                if (enviado)
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ✅ SRAP: Comando enviado exitosamente\n");

                    // Actualizar estado en la UI
                    if (lblSRAPEstado.InvokeRequired)
                    {
                        lblSRAPEstado.Invoke(new Action(() =>
                        {
                            lblSRAPEstado.Text = $"Estado: {descripcion} - {DateTime.Now:HH:mm:ss}";
                            lblSRAPEstado.ForeColor = Color.LightGreen;
                        }));
                    }
                    else
                    {
                        lblSRAPEstado.Text = $"Estado: {descripcion} - {DateTime.Now:HH:mm:ss}";
                        lblSRAPEstado.ForeColor = Color.LightGreen;
                    }

                    // El feedback se procesará automáticamente cuando llegue el mensaje
                }
                else
                {
                    rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SRAP: Error enviando comando\n");

                    // Actualizar estado de error en la UI
                    if (lblSRAPEstado.InvokeRequired)
                    {
                        lblSRAPEstado.Invoke(new Action(() =>
                        {
                            lblSRAPEstado.Text = "Estado: Error enviando comando";
                            lblSRAPEstado.ForeColor = Color.Red;
                        }));
                    }
                    else
                    {
                        lblSRAPEstado.Text = "Estado: Error enviando comando";
                        lblSRAPEstado.ForeColor = Color.Red;
                    }
                }

                rtbLog.ScrollToCaret();
            }
            catch (Exception ex)
            {
                rtbLog.AppendText($"{DateTime.Now:HH:mm:ss} ❌ SRAP: Error enviando comando: {ex.Message}\n");
                rtbLog.ScrollToCaret();
            }
        }

        #endregion

        #region SISTEMA DE MODO AUTOMÁTICO

        /// <summary>
        /// Verifica que todos los sistemas estén conectados para el modo automático
        /// </summary>
        private bool VerificarConectividadSistemas()
        {
            bool mqttOk = MQTTConectado && mqtt != null;
            bool modbusOk = modbusConectado && modbusClient != null && modbusClient.Connected;
            bool correoOk = correoConectado && gestorCorreo != null;

            return mqttOk && modbusOk && correoOk;
        }

        /// <summary>
        /// Obtiene el estado de conectividad de cada sistema
        /// </summary>
        private (bool mqtt, bool modbus, bool correo) ObtenerEstadoSistemas()
        {
            bool mqttOk = MQTTConectado && mqtt != null;
            bool modbusOk = modbusConectado && modbusClient != null && modbusClient.Connected;
            bool correoOk = correoConectado && gestorCorreo != null;

            return (mqttOk, modbusOk, correoOk);
        }

        /// <summary>
        /// Valida que existe una dirección Modbus configurada para el modo automático
        /// </summary>
        private bool ValidarDireccionModbus()
        {
            if (string.IsNullOrWhiteSpace(txtModbusDireccionRegistro.Text))
            {
                return false;
            }

            int direccion = ConvertirDireccionModbus(txtModbusDireccionRegistro.Text.Trim());
            if (direccion == -1)
            {
                return false;
            }

            direccionModbusModoAutomatico = txtModbusDireccionRegistro.Text.Trim();
            return true;
        }

        /// <summary>
        /// Calcula el valor que debe enviarse por Modbus según las prioridades configuradas
        /// </summary>
        private int CalcularValorSegunPrioridades()
        {
            // Actualizar valores desde los controles
            ActualizarValoresDesdeControles();

            // Prioridad 1: Paradas SRAP (si están habilitadas y hay paradas activas)
            if (paradasSRAPHabilitadas && hayParadasSRAPActivas)
            {
                prioridadActiva = "PARADAS SRAP";
                return 0; // Valor 0 para paradas SRAP activas
            }

            // Prioridad 2: Limitaciones de email (si están habilitadas y hay limitaciones activas)
            if (limitacionesEmailHabilitadas && hayLimitacionesEmailActivas)
            {
                prioridadActiva = "LIMITACIONES EMAIL";
                // Verificar si hay limitación en la hora actual
                int horaActual = DateTime.Now.Hour + 1; // Convertir a formato 1-24
                var limitaciones = gestorCorreo?.ObtenerEstadosActuales();
                if (limitaciones != null && horaActual >= 1 && horaActual <= 24)
                {
                    bool hayLimitacionAhora = limitaciones[horaActual - 1];
                    if (hayLimitacionAhora)
                    {
                        return 0; // Valor 0 para limitaciones activas
                    }
                }
            }

            // Prioridad 3: Sin limitaciones ni paradas - enviar valor de CONSIGNAPA o consigna manual
            if (modoAutomaticoActivo)
            {
                prioridadActiva = "CONSIGNAPA";
                return valorConsignaPa;
            }
            else
            {
                prioridadActiva = "CONSIGNA MANUAL";
                return consignaManual;
            }
        }

        /// <summary>
        /// Actualiza los valores de configuración desde los controles de la interfaz
        /// </summary>
        private void ActualizarValoresDesdeControles()
        {
            try
            {
                // Actualizar configuraciones desde checkboxes
                limitacionesEmailHabilitadas = chkHabilitarLimitacionesEmail?.Checked ?? true;
                paradasSRAPHabilitadas = chkHabilitarParadasSRAP?.Checked ?? true;

                // Actualizar valor CONSIGNAPA
                if (int.TryParse(txtValorConsignaPa?.Text, out int consignaPa))
                {
                    valorConsignaPa = consignaPa;
                }

                // Actualizar consigna manual
                if (int.TryParse(txtConsignaManual?.Text, out int manual))
                {
                    consignaManual = manual;
                }
            }
            catch (Exception ex)
            {
                LogSafe($"❌ MODO AUTO: Error actualizando valores desde controles: {ex.Message}");
            }
        }

        /// <summary>
        /// Calcula y envía el valor por Modbus en modo automático
        /// </summary>
        private async Task CalcularYEnviarValorModoAutomatico()
        {
            try
            {
                if (!modoAutomaticoActivo)
                    return;

                // Verificar conectividad
                if (!VerificarConectividadSistemas())
                {
                    LogSafe("❌ MODO AUTO: No todos los sistemas están conectados");
                    return;
                }

                // Actualizar estado de limitaciones de email
                ActualizarEstadoLimitacionesEmail();

                // Calcular valor según prioridades
                valorModoAutomaticoCalculado = CalcularValorSegunPrioridades();

                // Enviar valor por Modbus al PLC
                await EnviarValorPorModbus(valorModoAutomaticoCalculado);

                // Publicar valor en CONSIGNAPA por MQTT y enviar feedback
                await PublicarConsignaPorMQTT(valorModoAutomaticoCalculado);

                LogSafe($"🤖 MODO AUTO: Valor enviado: {valorModoAutomaticoCalculado} | Prioridad: {prioridadActiva}");
            }
            catch (Exception ex)
            {
                LogSafe($"❌ MODO AUTO: Error calculando y enviando valor: {ex.Message}");
            }
        }

        /// <summary>
        /// Publica el valor de consigna por MQTT y envía el feedback correspondiente
        /// </summary>
        private async Task PublicarConsignaPorMQTT(int valor)
        {
            try
            {
                if (mqtt == null || !mqtt.EstaConectado)
                {
                    LogSafe("⚠️ MQTT: No hay conexión para publicar CONSIGNAPA");
                    return;
                }

                // Publicar valor en CONSIGNAPA
                string topicConsigna = "DD/ENAGAS/ALMENDRALEJO/CONSIGNAPA";
                await mqtt.EscribirTopic(topicConsigna, valor.ToString());
                LogSafe($"📤 MQTT: CONSIGNAPA publicado: {valor}");

                // Enviar feedback de CONSIGNAPA
                string topicFeedback = "DD/ENAGAS/ALMENDRALEJO/FEEDBACKPA";
                await mqtt.EscribirTopic(topicFeedback, valor.ToString());
                LogSafe($"📤 MQTT: FEEDBACKPA enviado: {valor}");

                // Publicar motivo informativo
                string topicMotivo = "DD/ENAGAS/ALMENDRALEJO/MOTIVOPA";
                await mqtt.EscribirTopic(topicMotivo, prioridadActiva);
                LogSafe($"📤 MQTT: MOTIVOPA publicado: {prioridadActiva}");

                // Enviar feedback del motivo
                string topicFeedbackMotivo = "DD/ENAGAS/ALMENDRALEJO/FEEDBACKMOTIVOPA";
                await mqtt.EscribirTopic(topicFeedbackMotivo, prioridadActiva);
                LogSafe($"📤 MQTT: FEEDBACKMOTIVOPA enviado: {prioridadActiva}");
            }
            catch (Exception ex)
            {
                LogSafe($"❌ MQTT: Error publicando CONSIGNAPA: {ex.Message}");
            }
        }

        /// <summary>
        /// Actualiza el estado de las limitaciones de email para el día actual
        /// </summary>
        private void ActualizarEstadoLimitacionesEmail()
        {
            try
            {
                if (gestorCorreo == null)
                {
                    hayLimitacionesEmailActivas = false;
                    return;
                }

                var limitaciones = gestorCorreo.ObtenerEstadosActuales();
                if (limitaciones == null)
                {
                    hayLimitacionesEmailActivas = false;
                    return;
                }

                // Verificar si hay alguna limitación activa en el día
                hayLimitacionesEmailActivas = limitaciones.Any(l => l);

                // Si hay limitaciones, actualizar prioridad si no hay paradas SRAP
                if (hayLimitacionesEmailActivas && !hayParadasSRAPActivas)
                {
                    prioridadActiva = "LIMITACIONES EMAIL";
                }
            }
            catch (Exception ex)
            {
                LogSafe($"❌ MODO AUTO: Error actualizando limitaciones email: {ex.Message}");
                hayLimitacionesEmailActivas = false;
            }
        }

        /// <summary>
        /// Envía un valor por Modbus a la dirección configurada
        /// </summary>
        private async Task EnviarValorPorModbus(int valor)
        {
            try
            {
                if (modbusClient == null || !modbusClient.Connected)
                {
                    throw new InvalidOperationException("Cliente Modbus no conectado");
                }

                int direccion = ConvertirDireccionModbus(direccionModbusModoAutomatico);
                if (direccion == -1)
                {
                    throw new InvalidOperationException("Dirección Modbus inválida");
                }

                // Enviar valor con timeout
                var writeTask = Task.Run(() => modbusClient.WriteSingleRegister(direccion, valor));
                if (await Task.WhenAny(writeTask, Task.Delay(5000)) == writeTask)
                {
                    LogSafe($"✅ MODBUS AUTO: Registro {direccion} = {valor} enviado exitosamente");
                }
                else
                {
                    throw new TimeoutException("Timeout enviando valor por Modbus");
                }
            }
            catch (Exception ex)
            {
                LogSafe($"❌ MODBUS AUTO: Error enviando valor: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region INICIALIZACIÓN Y EVENTOS DEL MODO AUTOMÁTICO

        /// <summary>
        /// Inicializa los controles del modo automático
        /// </summary>
        private void InicializarModoAutomatico()
        {
            try
            {
                // Estado inicial de botones
                btnActivarModoAuto.Enabled = true;
                btnDesactivarModoAuto.Enabled = false;

                // Actualizar estado inicial de sistemas
                ActualizarEstadoSistemasUI();

                // Configurar timer para actualización periódica
                var timerModoAuto = new System.Windows.Forms.Timer();
                timerModoAuto.Interval = 2000; // Actualizar cada 2 segundos
                timerModoAuto.Tick += (s, e) => ActualizarEstadoSistemasUI();
                timerModoAuto.Start();

                LogSafe("✅ MODO AUTO: Inicialización completada");
            }
            catch (Exception ex)
            {
                LogSafe($"❌ MODO AUTO: Error en inicialización: {ex.Message}");
            }
        }

        /// <summary>
        /// Inicializa la visualización de limitaciones horarias
        /// </summary>
        private void InicializarLimitacionesHorarias()
        {
            try
            {
                pnlHoras.Controls.Clear();

                // Crear 24 labels para las horas (1-24)
                for (int hora = 1; hora <= 24; hora++)
                {
                    var lblHora = new Label
                    {
                        Text = hora.ToString("00"),
                        Size = new Size(25, 25),
                        Location = new Point((hora - 1) * 29 + 5, 25),
                        BackColor = Color.Green,
                        ForeColor = Color.White,
                        TextAlign = ContentAlignment.MiddleCenter,
                        Font = new Font("Segoe UI", 8F, FontStyle.Bold),
                        BorderStyle = BorderStyle.FixedSingle,
                        Tag = hora
                    };

                    pnlHoras.Controls.Add(lblHora);
                }

                // Actualizar colores según limitaciones actuales
                ActualizarVisualizacionLimitacionesHorarias();

                LogSafe("✅ LIMITACIONES: Visualización inicializada");
            }
            catch (Exception ex)
            {
                LogSafe($"❌ LIMITACIONES: Error en inicialización: {ex.Message}");
            }
        }

        /// <summary>
        /// Actualiza la visualización de limitaciones horarias
        /// </summary>
        private void ActualizarVisualizacionLimitacionesHorarias()
        {
            try
            {
                if (pnlHoras.InvokeRequired)
                {
                    pnlHoras.Invoke(new Action(ActualizarVisualizacionLimitacionesHorarias));
                    return;
                }

                if (gestorCorreo == null)
                    return;

                var limitaciones = gestorCorreo.ObtenerEstadosActuales();
                if (limitaciones == null)
                    return;

                int horaActual = DateTime.Now.Hour + 1; // Convertir a formato 1-24

                foreach (Control control in pnlHoras.Controls)
                {
                    if (control is Label lblHora && control.Tag is int hora)
                    {
                        bool hayLimitacion = limitaciones[hora - 1];
                        bool esHoraActual = hora == horaActual;

                        if (hayLimitacion)
                        {
                            lblHora.BackColor = esHoraActual ? Color.DarkRed : Color.Red;
                            lblHora.ForeColor = Color.White;
                        }
                        else
                        {
                            lblHora.BackColor = esHoraActual ? Color.DarkGreen : Color.Green;
                            lblHora.ForeColor = Color.White;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogSafe($"❌ LIMITACIONES: Error actualizando visualización: {ex.Message}");
            }
        }

        /// <summary>
        /// Actualiza el estado de los sistemas en la UI
        /// </summary>
        private void ActualizarEstadoSistemasUI()
        {
            try
            {
                if (lblEstadoSistemas.InvokeRequired)
                {
                    lblEstadoSistemas.Invoke(new Action(ActualizarEstadoSistemasUI));
                    return;
                }

                var (mqtt, modbus, correo) = ObtenerEstadoSistemas();

                string mqttIcon = mqtt ? "✅" : "❌";
                string modbusIcon = modbus ? "✅" : "❌";
                string correoIcon = correo ? "✅" : "❌";

                lblEstadoSistemas.Text = $"Sistemas: MQTT {mqttIcon} | Modbus {modbusIcon} | Email {correoIcon}";
                lblPrioridadActiva.Text = $"Prioridad: {prioridadActiva}";
                lblValorEnviado.Text = $"Valor: {(modoAutomaticoActivo ? valorModoAutomaticoCalculado.ToString() : "--")}";

                // Actualizar limitaciones horarias
                ActualizarVisualizacionLimitacionesHorarias();
            }
            catch (Exception ex)
            {
                LogSafe($"❌ MODO AUTO: Error actualizando UI: {ex.Message}");
            }
        }

        /// <summary>
        /// Evento del botón Activar Modo Automático
        /// </summary>
        private async void btnActivarModoAuto_Click(object sender, EventArgs e)
        {
            try
            {
                // Verificar conectividad de sistemas
                if (!VerificarConectividadSistemas())
                {
                    var (mqtt, modbus, correo) = ObtenerEstadoSistemas();
                    string sistemasDesconectados = "";
                    if (!mqtt) sistemasDesconectados += "• MQTT\n";
                    if (!modbus) sistemasDesconectados += "• Modbus\n";
                    if (!correo) sistemasDesconectados += "• Correo Electrónico\n";

                    MessageBox.Show(
                        $"❌ NO SE PUEDE ACTIVAR EL MODO AUTOMÁTICO\n\n" +
                        $"Los siguientes sistemas no están conectados:\n{sistemasDesconectados}\n" +
                        $"Conecte todos los sistemas antes de activar el modo automático.",
                        "🚫 SISTEMAS DESCONECTADOS",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                    return;
                }

                // Validar dirección Modbus
                if (!ValidarDireccionModbus())
                {
                    MessageBox.Show(
                        $"❌ DIRECCIÓN MODBUS INVÁLIDA\n\n" +
                        $"Configure una dirección Modbus válida en la pestaña Modbus antes de activar el modo automático.\n\n" +
                        $"Ejemplos válidos:\n• MW430\n• 430\n• 40431",
                        "🚫 CONFIGURACIÓN REQUERIDA",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning
                    );
                    return;
                }

                // Activar modo automático
                modoAutomaticoActivo = true;
                btnActivarModoAuto.Enabled = false;
                btnDesactivarModoAuto.Enabled = true;

                // Calcular y enviar valor inicial
                await CalcularYEnviarValorModoAutomatico();

                LogSafe("🤖 MODO AUTO: Activado exitosamente");
                MessageBox.Show(
                    "✅ MODO AUTOMÁTICO ACTIVADO\n\n" +
                    "El sistema ahora calculará y enviará valores automáticamente según las prioridades:\n" +
                    "1. Paradas SRAP\n" +
                    "2. Limitaciones de Email\n" +
                    "3. Valor de Consigna",
                    "🤖 MODO AUTOMÁTICO",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
            }
            catch (Exception ex)
            {
                LogSafe($"❌ MODO AUTO: Error activando: {ex.Message}");
                MessageBox.Show(
                    $"❌ ERROR ACTIVANDO MODO AUTOMÁTICO\n\n{ex.Message}",
                    "💥 ERROR",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// Evento del botón Desactivar Modo Automático
        /// </summary>
        private void btnDesactivarModoAuto_Click(object sender, EventArgs e)
        {
            try
            {
                modoAutomaticoActivo = false;
                btnActivarModoAuto.Enabled = true;
                btnDesactivarModoAuto.Enabled = false;

                prioridadActiva = "NINGUNA";
                valorModoAutomaticoCalculado = 0;

                ActualizarEstadoSistemasUI();

                LogSafe("🤖 MODO AUTO: Desactivado");
                MessageBox.Show(
                    "⏹️ MODO AUTOMÁTICO DESACTIVADO\n\n" +
                    "El sistema ya no enviará valores automáticamente.",
                    "🤖 MODO AUTOMÁTICO",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
            }
            catch (Exception ex)
            {
                LogSafe($"❌ MODO AUTO: Error desactivando: {ex.Message}");
            }
        }

        #endregion

        #region Event Handlers de Configuración del Modo Automático

        /// <summary>
        /// Event handler para cambios en el checkbox de limitaciones de email
        /// </summary>
        private void chkHabilitarLimitacionesEmail_CheckedChanged(object sender, EventArgs e)
        {
            limitacionesEmailHabilitadas = chkHabilitarLimitacionesEmail.Checked;
            LogSafe($"🔧 CONFIG: Limitaciones Email {(limitacionesEmailHabilitadas ? "HABILITADAS" : "DESHABILITADAS")}");

            // Recalcular valor si el modo automático está activo
            if (modoAutomaticoActivo)
            {
                Task.Run(async () => await CalcularYEnviarValorModoAutomatico());
            }
        }

        /// <summary>
        /// Event handler para cambios en el checkbox de paradas SRAP
        /// </summary>
        private void chkHabilitarParadasSRAP_CheckedChanged(object sender, EventArgs e)
        {
            paradasSRAPHabilitadas = chkHabilitarParadasSRAP.Checked;
            LogSafe($"🔧 CONFIG: Paradas SRAP {(paradasSRAPHabilitadas ? "HABILITADAS" : "DESHABILITADAS")}");

            // Recalcular valor si el modo automático está activo
            if (modoAutomaticoActivo)
            {
                Task.Run(async () => await CalcularYEnviarValorModoAutomatico());
            }
        }

        /// <summary>
        /// Event handler para cambios en el textbox de consigna manual
        /// </summary>
        private void txtConsignaManual_TextChanged(object sender, EventArgs e)
        {
            if (int.TryParse(txtConsignaManual.Text, out int valor))
            {
                consignaManual = valor;
                LogSafe($"🔧 CONFIG: Consigna Manual actualizada a {consignaManual}");

                // Recalcular valor si el modo automático NO está activo (modo manual)
                if (!modoAutomaticoActivo)
                {
                    Task.Run(async () => await CalcularYEnviarValorModoAutomatico());
                }
            }
        }

        /// <summary>
        /// Event handler para cambios en el textbox de valor CONSIGNAPA
        /// </summary>
        private void txtValorConsignaPa_TextChanged(object sender, EventArgs e)
        {
            if (int.TryParse(txtValorConsignaPa.Text, out int valor))
            {
                valorConsignaPa = valor;
                LogSafe($"🔧 CONFIG: Valor CONSIGNAPA actualizado a {valorConsignaPa}");

                // Recalcular valor si el modo automático está activo
                if (modoAutomaticoActivo)
                {
                    Task.Run(async () => await CalcularYEnviarValorModoAutomatico());
                }
            }
        }

        private void btnDesconectarCorreo_Click_1(object sender, EventArgs e)
        {

        }

        #endregion

        // FIN GESTIÓN DE LA NAVEGACIÓN ************************************************************************
    }
}
