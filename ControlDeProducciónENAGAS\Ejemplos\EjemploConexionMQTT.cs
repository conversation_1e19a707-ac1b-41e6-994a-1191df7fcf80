using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using ControlDeProducciónENAGAS.Clases;

namespace ControlDeProducciónENAGAS.Ejemplos
{
    /// <summary>
    /// Ejemplo de uso de la clase ConexionMQTT
    /// </summary>
    public static class EjemploConexionMQTT
    {
        /// <summary>
        /// Ejemplo básico de conexión y uso
        /// </summary>
        public static async Task EjemploBasico()
        {
            // Crear instancia de conexión MQTT
            var mqtt = new ConexionMQTT
            {
                Host = "test.mosquitto.org", // Broker público para pruebas
                Puerto = 1883,
                Protocolo = "mqtt://",
                ClientId = "ENAGAS_Test_001",
                Usuario = "", // Sin autenticación para broker público
                Password = ""
            };

            try
            {
                // Conectar
                Console.WriteLine("🔌 Conectando a MQTT...");
                bool conectado = await mqtt.ConectarAsync();
                
                if (conectado)
                {
                    Console.WriteLine("✅ Conectado exitosamente!");
                    Console.WriteLine($"📊 Estado: {mqtt.EstadoTexto}");

                    // Suscribirse a un topic
                    await mqtt.SuscribirTopic("enagas/produccion/test");
                    Console.WriteLine("📡 Suscrito a topic: enagas/produccion/test");

                    // Publicar un mensaje
                    await mqtt.EscribirTopic("enagas/produccion/test", "Hola desde ENAGAS! 🚀");
                    Console.WriteLine("📤 Mensaje publicado");

                    // Esperar un poco para recibir el mensaje
                    await Task.Delay(2000);

                    // Leer el valor del topic
                    string? valor = mqtt.LeerTopic("enagas/produccion/test");
                    Console.WriteLine($"📥 Valor recibido: {valor ?? "Sin datos"}");

                    // Desconectar
                    mqtt.Desconectar();
                    Console.WriteLine("🔌 Desconectado");
                }
                else
                {
                    Console.WriteLine("❌ No se pudo conectar");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Ejemplo con múltiples topics
        /// </summary>
        public static async Task EjemploMultiplesTopics()
        {
            var mqtt = new ConexionMQTT
            {
                Host = "localhost",
                Puerto = 1883,
                ClientId = "ENAGAS_MultiTopic"
            };

            try
            {
                if (await mqtt.ConectarAsync())
                {
                    // Suscribirse a varios topics
                    string[] topics = {
                        "enagas/temperatura",
                        "enagas/presion",
                        "enagas/caudal",
                        "enagas/estado"
                    };

                    foreach (var topic in topics)
                    {
                        await mqtt.SuscribirTopic(topic);
                        Console.WriteLine($"📡 Suscrito a: {topic}");
                    }

                    // Publicar datos simulados
                    await mqtt.EscribirTopic("enagas/temperatura", "25.5°C");
                    await mqtt.EscribirTopic("enagas/presion", "1.2 bar");
                    await mqtt.EscribirTopic("enagas/caudal", "150 m³/h");
                    await mqtt.EscribirTopic("enagas/estado", "OPERATIVO");

                    Console.WriteLine("📤 Datos publicados");

                    // Esperar y leer todos los valores
                    await Task.Delay(1000);

                    foreach (var topic in topics)
                    {
                        string? valor = mqtt.LeerTopic(topic);
                        Console.WriteLine($"📊 {topic}: {valor ?? "Sin datos"}");
                    }

                    mqtt.Desconectar();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Ejemplo de integración con formulario
        /// </summary>
        public static ConexionMQTT CrearDesdeFormulario(
            string protocolo, string host, string puerto, 
            string clientId, string usuario, string password, 
            bool usarSsl)
        {
            var mqtt = new ConexionMQTT
            {
                Protocolo = protocolo,
                Host = host,
                ClientId = clientId,
                Usuario = usuario,
                Password = password,
                UsarSslTls = usarSsl
            };

            // Convertir puerto
            if (int.TryParse(puerto, out int puertoNum))
                mqtt.Puerto = puertoNum;

            return mqtt;
        }

        /// <summary>
        /// Ejemplo de monitoreo continuo
        /// </summary>
        public static async Task EjemploMonitoreo()
        {
            var mqtt = new ConexionMQTT
            {
                Host = "localhost",
                Puerto = 1883,
                ClientId = "ENAGAS_Monitor"
            };

            try
            {
                if (await mqtt.ConectarAsync())
                {
                    // Suscribirse a topics de monitoreo
                    await mqtt.SuscribirTopic("enagas/alarmas");
                    await mqtt.SuscribirTopic("enagas/eventos");

                    Console.WriteLine("🔍 Iniciando monitoreo...");
                    Console.WriteLine("Presiona cualquier tecla para detener");

                    // Monitoreo continuo
                    var monitoreoTask = Task.Run(async () =>
                    {
                        while (mqtt.EstaConectado)
                        {
                            // Verificar alarmas
                            string? alarma = mqtt.LeerTopic("enagas/alarmas");
                            if (!string.IsNullOrEmpty(alarma))
                            {
                                Console.WriteLine($"🚨 ALARMA: {alarma}");
                            }

                            // Verificar eventos
                            string? evento = mqtt.LeerTopic("enagas/eventos");
                            if (!string.IsNullOrEmpty(evento))
                            {
                                Console.WriteLine($"📋 EVENTO: {evento}");
                            }

                            await Task.Delay(1000); // Verificar cada segundo
                        }
                    });

                    // Simular algunos eventos
                    _ = Task.Run(async () =>
                    {
                        await Task.Delay(3000);
                        await mqtt.EscribirTopic("enagas/eventos", "Sistema iniciado");
                        
                        await Task.Delay(5000);
                        await mqtt.EscribirTopic("enagas/alarmas", "Presión alta detectada");
                        
                        await Task.Delay(3000);
                        await mqtt.EscribirTopic("enagas/eventos", "Mantenimiento programado");
                    });

                    Console.ReadKey();
                    mqtt.Desconectar();
                    Console.WriteLine("\n🔌 Monitoreo detenido");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error en monitoreo: {ex.Message}");
            }
        }
    }
}
