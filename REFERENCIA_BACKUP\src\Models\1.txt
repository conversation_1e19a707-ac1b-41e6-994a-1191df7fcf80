using System;
using System.Drawing;
using System.Drawing.Drawing2D; // Asegúrate de que esta línea esté presente si usas GraphicsPath o similares
using System.Windows.Forms;

namespace prueba
{
    partial class Form1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        // --- INICIO: DECLARACIONES DE VARIABLES (Asegúrate de que estas estén aquí y no dentro de InitializeComponent) ---
        internal System.Windows.Forms.Panel panelMailContent;
        internal System.Windows.Forms.Button btnAjustesCorreo;
        internal System.Windows.Forms.Button btnClear;

        // Additional status indicators for Email and Modbus
        internal System.Windows.Forms.Panel circleStatusEmail;
        internal System.Windows.Forms.Label lblStatusEmail;
        internal System.Windows.Forms.Panel circleStatusModbus;
        internal System.Windows.Forms.Label lblStatusModbus;

        public System.Windows.Forms.TextBox txtServidor; // Ya era public
        public System.Windows.Forms.TextBox txtPuerto; // Ya era public
        public System.Windows.Forms.TextBox txtUsuario; // Ya era public
        public System.Windows.Forms.TextBox txtContraseña; // Ya era public
        public System.Windows.Forms.CheckBox chkPop3Ssl; // Ya era public
        internal System.Windows.Forms.Button btnProbar;
        internal System.Windows.Forms.Button btnProcesarEmails;
        public System.Windows.Forms.Panel panelEstado; // Ya era public
        internal System.Windows.Forms.Label lblServerPop3;
        internal System.Windows.Forms.Label lblPuertoPop3;
        internal System.Windows.Forms.Label lblUsuarioPop3;
        internal System.Windows.Forms.Label lblContraseñaPop3;

        // EWS Controls
        public System.Windows.Forms.CheckBox chkUseEws; // Ya era public
        public System.Windows.Forms.TextBox txtEwsServerUrl; // Ya era public
        public System.Windows.Forms.TextBox txtEwsUsername; // Ya era public
        public System.Windows.Forms.TextBox txtEwsPassword; // Ya era public
        public System.Windows.Forms.TextBox txtEwsDomain; // Ya era public
        public System.Windows.Forms.CheckBox chkEwsUseOAuth; // Ya era public
        internal System.Windows.Forms.Label lblEwsServerUrl;
        internal System.Windows.Forms.Label lblEwsUsername;
        internal System.Windows.Forms.Label lblEwsPassword;
        internal System.Windows.Forms.Label lblEwsDomain;
        internal System.Windows.Forms.Button btnEwsProbar;
        internal System.Windows.Forms.Button btnEwsProcesarEmails;
        internal System.Windows.Forms.Panel panelEwsEstado;

        // Email Notification Controls
        internal System.Windows.Forms.Label lblEmailNotifications;
        public System.Windows.Forms.TextBox txtEmailNotif1; // Ya era public
        public System.Windows.Forms.TextBox txtEmailNotif2; // Ya era public
        public System.Windows.Forms.TextBox txtEmailNotif3; // Ya era public
        public System.Windows.Forms.TextBox txtEmailNotif4; // Ya era public
        public System.Windows.Forms.TextBox txtEmailNotif5; // Ya era public

        public NumericUpDown nudCheckInterval; // Ya era public
        internal Label lblCheckInterval;
        internal Panel panelEstadoHora;
        internal System.Windows.Forms.Panel panelSinopticoTabContent;
        internal System.Windows.Forms.TextBox txtSinopticoFilter;
        internal System.Windows.Forms.DataGridView dgvSinopticoData;

        // Modbus Controls (Declarations only)
        internal System.Windows.Forms.Button btnNavModbus;
        internal System.Windows.Forms.Panel panelModbusContent;

        // Left Panel Controls (Connection Settings)
        internal System.Windows.Forms.Label lblModbusConfigSelectionLeft;
        internal System.Windows.Forms.ComboBox cmbModbusConfigLeft;
        internal System.Windows.Forms.TextBox txtModbusConfigNameLeft;
        internal System.Windows.Forms.Button btnModbusSaveConfigLeft;
        internal System.Windows.Forms.Button btnModbusNewConfigLeft;
        internal System.Windows.Forms.Button btnModbusDeleteConfigLeft;
        internal System.Windows.Forms.GroupBox grpModbusConnectionSettingsLeft;
        internal System.Windows.Forms.RadioButton rdoModbusTCPLeft;
        internal System.Windows.Forms.RadioButton rdoModbusRTULeft;
        internal System.Windows.Forms.Label lblModbusIPLeft;
        internal System.Windows.Forms.TextBox txtModbusIPLeft;
        internal System.Windows.Forms.Label lblModbusPortLeft;
        internal System.Windows.Forms.NumericUpDown nudModbusPortLeft;
        internal System.Windows.Forms.Label lblModbusComPortLeft;
        internal System.Windows.Forms.ComboBox cmbModbusComPortLeft;
        internal System.Windows.Forms.Label lblModbusBaudRateLeft;
        internal System.Windows.Forms.ComboBox cmbModbusBaudRateLeft;
        internal System.Windows.Forms.Label lblModbusParityLeft;
        internal System.Windows.Forms.ComboBox cmbModbusParityLeft;
        internal System.Windows.Forms.Label lblModbusDataBitsLeft;
        internal System.Windows.Forms.ComboBox cmbModbusDataBitsLeft;
        internal System.Windows.Forms.Label lblModbusStopBitsLeft;
        internal System.Windows.Forms.ComboBox cmbModbusStopBitsLeft;
        internal System.Windows.Forms.Label lblModbusDeviceIdLeft;
        internal System.Windows.Forms.NumericUpDown nudModbusDeviceIdLeft;
        internal System.Windows.Forms.Button btnModbusConnectLeft;
        internal System.Windows.Forms.Button btnModbusDisconnectLeft;
        internal System.Windows.Forms.Label lblModbusStatusLeft;
        internal System.Windows.Forms.GroupBox grpModbusOperationsLeft;
        internal System.Windows.Forms.Label lblModbusRegisterAddressLeft;
        internal System.Windows.Forms.TextBox txtModbusRegisterAddressLeft;
        internal System.Windows.Forms.Label lblModbusRegisterCountLeft;
        internal System.Windows.Forms.NumericUpDown nudModbusRegisterCountLeft;
        internal System.Windows.Forms.Label lblModbusWriteValueLeft;
        internal System.Windows.Forms.TextBox txtModbusWriteValueLeft;
        internal System.Windows.Forms.CheckBox chkModbusWriteCoilValueLeft;
        internal System.Windows.Forms.Button btnModbusReadHoldingRegistersLeft;
        internal System.Windows.Forms.Button btnModbusReadInputRegistersLeft;
        internal System.Windows.Forms.Button btnModbusReadCoilsLeft;
        internal System.Windows.Forms.Button btnModbusReadDiscreteInputsLeft;
        internal System.Windows.Forms.Button btnModbusWriteSingleRegisterLeft;
        internal System.Windows.Forms.Button btnModbusWriteSingleCoilLeft;
        internal System.Windows.Forms.Label lblModbusLastErrorLeft;
        internal System.Windows.Forms.DataGridView dgvModbusReadResultsLeft;
        internal System.Windows.Forms.SplitContainer splitContainerModbus;

        // Modbus Controls (Declarations only) - Right
        internal System.Windows.Forms.Label lblModbusConfigSelectionRight;
        internal System.Windows.Forms.ComboBox cmbModbusConfigRight;
        internal System.Windows.Forms.TextBox txtModbusConfigNameRight;
        internal System.Windows.Forms.Button btnModbusSaveConfigRight;
        internal System.Windows.Forms.Button btnModbusNewConfigRight;
        internal System.Windows.Forms.Button btnModbusDeleteConfigRight;
        internal System.Windows.Forms.GroupBox grpModbusConnectionSettingsRight;
        internal System.Windows.Forms.RadioButton rdoModbusTCPRight;
        internal System.Windows.Forms.RadioButton rdoModbusRTURight;
        internal System.Windows.Forms.Label lblModbusIPRight;
        internal System.Windows.Forms.TextBox txtModbusIPRight;
        internal System.Windows.Forms.Label lblModbusPortRight;
        internal System.Windows.Forms.NumericUpDown nudModbusPortRight;
        internal System.Windows.Forms.Label lblModbusComPortRight;
        internal System.Windows.Forms.ComboBox cmbModbusComPortRight;
        internal System.Windows.Forms.Label lblModbusBaudRateRight;
        internal System.Windows.Forms.ComboBox cmbModbusBaudRateRight;
        internal System.Windows.Forms.Label lblModbusParityRight;
        internal System.Windows.Forms.ComboBox cmbModbusParityRight;
        internal System.Windows.Forms.Label lblModbusDataBitsRight;
        internal System.Windows.Forms.ComboBox cmbModbusDataBitsRight;
        internal System.Windows.Forms.Label lblModbusStopBitsRight;
        internal System.Windows.Forms.ComboBox cmbModbusStopBitsRight;
        internal System.Windows.Forms.Label lblModbusDeviceIdRight;
        internal System.Windows.Forms.NumericUpDown nudModbusDeviceIdRight;
        internal System.Windows.Forms.Button btnModbusConnectRight;
        internal System.Windows.Forms.Button btnModbusDisconnectRight;
        internal System.Windows.Forms.Label lblModbusStatusRight;
        internal System.Windows.Forms.GroupBox grpModbusOperationsRight;
        internal System.Windows.Forms.Label lblModbusRegisterAddressRight;
        internal System.Windows.Forms.TextBox txtModbusRegisterAddressRight;
        internal System.Windows.Forms.Label lblModbusRegisterCountRight;
        internal System.Windows.Forms.NumericUpDown nudModbusRegisterCountRight;
        internal System.Windows.Forms.Label lblModbusWriteValueRight;
        internal System.Windows.Forms.TextBox txtModbusWriteValueRight;
        internal System.Windows.Forms.CheckBox chkModbusWriteCoilValueRight;
        internal System.Windows.Forms.Button btnModbusReadHoldingRegistersRight;
        internal System.Windows.Forms.Button btnModbusReadInputRegistersRight;
        internal System.Windows.Forms.Button btnModbusReadCoilsRight;
        internal System.Windows.Forms.Button btnModbusReadDiscreteInputsRight;
        internal System.Windows.Forms.Button btnModbusWriteSingleRegisterRight;
        internal System.Windows.Forms.Button btnModbusWriteSingleCoilRight;
        internal System.Windows.Forms.Label lblModbusLastErrorRight;
        internal System.Windows.Forms.DataGridView dgvModbusReadResultsRight;

        // Nuevos controles para la barra superior y navegación, necesarios para que el diseñador los vea.
        internal System.Windows.Forms.Panel panelTopBar;
        internal System.Windows.Forms.Label lblAppTitle;
        internal System.Windows.Forms.Panel circleStatus;
        internal System.Windows.Forms.Label lblStatus;
        internal System.Windows.Forms.Button btnClose;
        internal System.Windows.Forms.Panel panelNavigation;
        internal System.Windows.Forms.Button btnNavConnection;
        internal System.Windows.Forms.Button btnNavConfig;
        internal System.Windows.Forms.Button btnNavSinoptico;
        internal System.Windows.Forms.Button btnNavMail;
        internal System.Windows.Forms.Panel panelContent;
        internal System.Windows.Forms.Panel panelConnectionContent;
        internal System.Windows.Forms.FlowLayoutPanel panelProfiles;
        internal System.Windows.Forms.Label lblName;
        internal System.Windows.Forms.TextBox txtName;
        internal System.Windows.Forms.Label lblHost;
        internal System.Windows.Forms.ComboBox cmbHost;
        internal System.Windows.Forms.TextBox txtHostAddress;
        internal System.Windows.Forms.Label lblPort;
        internal System.Windows.Forms.TextBox txtPort;
        internal System.Windows.Forms.Label lblClientID;
        internal System.Windows.Forms.TextBox txtClientID;
        internal System.Windows.Forms.Label lblUsername;
        internal System.Windows.Forms.TextBox txtUsername;
        internal System.Windows.Forms.Label lblPassword;
        internal System.Windows.Forms.TextBox txtPassword;
        internal System.Windows.Forms.CheckBox chkSSL_TLS;
        internal System.Windows.Forms.RadioButton rdoCASigned;
        internal System.Windows.Forms.RadioButton rdoSelfSigned;
        internal System.Windows.Forms.Button btnConnect;
        internal System.Windows.Forms.Button btnDisconnect;
        internal System.Windows.Forms.Panel panelConfigContent;
        internal System.Windows.Forms.Label lblTopic;
        internal System.Windows.Forms.TextBox txtTopic;
        internal System.Windows.Forms.Label lblMessage;
        internal System.Windows.Forms.TextBox txtMessage;
        internal System.Windows.Forms.Button btnSend;
        internal System.Windows.Forms.Panel panelSinopticoContent;

        // --- FIN: DECLARACIONES DE VARIABLES ---


        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            panelMailContent = new Panel();
            btnAjustesCorreo = new Button();
            btnClear = new Button();
            circleStatusEmail = new Panel();
            lblStatusEmail = new Label();
            circleStatusModbus = new Panel();
            lblStatusModbus = new Label();
            txtServidor = new TextBox();
            txtPuerto = new TextBox();
            txtUsuario = new TextBox();
            txtContraseña = new TextBox();
            chkPop3Ssl = new CheckBox();
            btnProbar = new Button();
            btnProcesarEmails = new Button();
            panelEstado = new Panel();
            lblServerPop3 = new Label();
            lblPuertoPop3 = new Label();
            lblUsuarioPop3 = new Label();
            lblContraseñaPop3 = new Label();
            chkUseEws = new CheckBox();
            txtEwsServerUrl = new TextBox();
            txtEwsUsername = new TextBox();
            txtEwsPassword = new TextBox();
            txtEwsDomain = new TextBox();
            chkEwsUseOAuth = new CheckBox();
            lblEwsServerUrl = new Label();
            lblEwsUsername = new Label();
            lblEwsPassword = new Label();
            lblEwsDomain = new Label();
            btnEwsProbar = new Button();
            btnEwsProcesarEmails = new Button();
            panelEwsEstado = new Panel();
            lblEmailNotifications = new Label();
            txtEmailNotif1 = new TextBox();
            txtEmailNotif2 = new TextBox();
            txtEmailNotif3 = new TextBox();
            txtEmailNotif4 = new TextBox();
            txtEmailNotif5 = new TextBox();
            nudCheckInterval = new NumericUpDown();
            lblCheckInterval = new Label();
            panelEstadoHora = new Panel();
            panelSinopticoTabContent = new Panel();
            txtSinopticoFilter = new TextBox();
            dgvSinopticoData = new DataGridView();
            btnNavModbus = new Button();
            panelModbusContent = new Panel();
            lblModbusConfigSelectionLeft = new Label();
            cmbModbusConfigLeft = new ComboBox();
            txtModbusConfigNameLeft = new TextBox();
            btnModbusSaveConfigLeft = new Button();
            btnModbusNewConfigLeft = new Button();
            btnModbusDeleteConfigLeft = new Button();
            grpModbusConnectionSettingsLeft = new GroupBox();
            rdoModbusTCPLeft = new RadioButton();
            rdoModbusRTULeft = new RadioButton();
            lblModbusIPLeft = new Label();
            txtModbusIPLeft = new TextBox();
            lblModbusPortLeft = new Label();
            nudModbusPortLeft = new NumericUpDown();
            lblModbusComPortLeft = new Label();
            cmbModbusComPortLeft = new ComboBox();
            lblModbusBaudRateLeft = new Label();
            cmbModbusBaudRateLeft = new ComboBox();
            lblModbusParityLeft = new Label();
            cmbModbusParityLeft = new ComboBox();
            lblModbusDataBitsLeft = new Label();
            cmbModbusDataBitsLeft = new ComboBox();
            lblModbusStopBitsLeft = new Label();
            cmbModbusStopBitsLeft = new ComboBox();
            lblModbusDeviceIdLeft = new Label();
            nudModbusDeviceIdLeft = new NumericUpDown();
            btnModbusConnectLeft = new Button();
            btnModbusDisconnectLeft = new Button();
            lblModbusStatusLeft = new Label();
            grpModbusOperationsLeft = new GroupBox();
            lblModbusRegisterAddressLeft = new Label();
            txtModbusRegisterAddressLeft = new TextBox();
            lblModbusRegisterCountLeft = new Label();
            nudModbusRegisterCountLeft = new NumericUpDown();
            lblModbusWriteValueLeft = new Label();
            txtModbusWriteValueLeft = new TextBox();
            chkModbusWriteCoilValueLeft = new CheckBox();
            btnModbusReadHoldingRegistersLeft = new Button();
            btnModbusReadInputRegistersLeft = new Button();
            btnModbusReadCoilsLeft = new Button();
            btnModbusReadDiscreteInputsLeft = new Button();
            btnModbusWriteSingleRegisterLeft = new Button();
            btnModbusWriteSingleCoilLeft = new Button();
            lblModbusLastErrorLeft = new Label();
            dgvModbusReadResultsLeft = new DataGridView();
            splitContainerModbus = new SplitContainer();
            lblModbusConfigSelectionRight = new Label();
            cmbModbusConfigRight = new ComboBox();
            txtModbusConfigNameRight = new TextBox();
            btnModbusSaveConfigRight = new Button();
            btnModbusNewConfigRight = new Button();
            btnModbusDeleteConfigRight = new Button();
            grpModbusConnectionSettingsRight = new GroupBox();
            rdoModbusTCPRight = new RadioButton();
            rdoModbusRTURight = new RadioButton();
            lblModbusIPRight = new Label();
            txtModbusIPRight = new TextBox();
            lblModbusPortRight = new Label();
            nudModbusPortRight = new NumericUpDown();
            lblModbusComPortRight = new Label();
            cmbModbusComPortRight = new ComboBox();
            lblModbusBaudRateRight = new Label();
            cmbModbusBaudRateRight = new ComboBox();
            lblModbusParityRight = new Label();
            cmbModbusParityRight = new ComboBox();
            lblModbusDataBitsRight = new Label();
            cmbModbusDataBitsRight = new ComboBox();
            lblModbusStopBitsRight = new Label();
            cmbModbusStopBitsRight = new ComboBox();
            lblModbusDeviceIdRight = new Label();
            nudModbusDeviceIdRight = new NumericUpDown();
            btnModbusConnectRight = new Button();
            btnModbusDisconnectRight = new Button();
            lblModbusStatusRight = new Label();
            grpModbusOperationsRight = new GroupBox();
            lblModbusRegisterAddressRight = new Label();
            txtModbusRegisterAddressRight = new TextBox();
            lblModbusRegisterCountRight = new Label();
            nudModbusRegisterCountRight = new NumericUpDown();
            lblModbusWriteValueRight = new Label();
            txtModbusWriteValueRight = new TextBox();
            chkModbusWriteCoilValueRight = new CheckBox();
            btnModbusReadHoldingRegistersRight = new Button();
            btnModbusReadInputRegistersRight = new Button();
            btnModbusReadCoilsRight = new Button();
            btnModbusReadDiscreteInputsRight = new Button();
            btnModbusWriteSingleRegisterRight = new Button();
            btnModbusWriteSingleCoilRight = new Button();
            lblModbusLastErrorRight = new Label();
            dgvModbusReadResultsRight = new DataGridView();
            panelTopBar = new Panel();
            btnClose = new Button();
            lblStatus = new Label();
            circleStatus = new Panel();
            lblAppTitle = new Label();
            panelNavigation = new Panel();
            btnNavMail = new Button();
            btnNavSinoptico = new Button();
            btnNavConfig = new Button();
            btnNavConnection = new Button();
            panelContent = new Panel();
            panelConnectionContent = new Panel();
            btnDisconnect = new Button();
            btnConnect = new Button();
            rdoSelfSigned = new RadioButton();
            rdoCASigned = new RadioButton();
            chkSSL_TLS = new CheckBox();
            txtPassword = new TextBox();
            lblPassword = new Label();
            txtUsername = new TextBox();
            lblUsername = new Label();
            txtClientID = new TextBox();
            lblClientID = new Label();
            txtPort = new TextBox();
            lblPort = new Label();
            txtHostAddress = new TextBox();
            cmbHost = new ComboBox();
            lblHost = new Label();
            txtName = new TextBox();
            lblName = new Label();
            panelProfiles = new FlowLayoutPanel();
            panelSinopticoContent = new Panel();
            panelConfigContent = new Panel();
            btnSend = new Button();
            txtMessage = new TextBox();
            lblMessage = new Label();
            txtTopic = new TextBox();
            lblTopic = new Label();
            tabControl1 = new TabControl();
            Conexión = new TabPage();
            MQTT = new TabPage();
            Sinóptico = new TabPage();
            Correo = new TabPage();
            Modbus = new TabPage();
            ((System.ComponentModel.ISupportInitialize)nudCheckInterval).BeginInit();
            ((System.ComponentModel.ISupportInitialize)dgvSinopticoData).BeginInit();
            ((System.ComponentModel.ISupportInitialize)nudModbusPortLeft).BeginInit();
            ((System.ComponentModel.ISupportInitialize)nudModbusDeviceIdLeft).BeginInit();
            ((System.ComponentModel.ISupportInitialize)nudModbusRegisterCountLeft).BeginInit();
            ((System.ComponentModel.ISupportInitialize)dgvModbusReadResultsLeft).BeginInit();
            ((System.ComponentModel.ISupportInitialize)splitContainerModbus).BeginInit();
            splitContainerModbus.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)nudModbusPortRight).BeginInit();
            ((System.ComponentModel.ISupportInitialize)nudModbusDeviceIdRight).BeginInit();
            ((System.ComponentModel.ISupportInitialize)nudModbusRegisterCountRight).BeginInit();
            ((System.ComponentModel.ISupportInitialize)dgvModbusReadResultsRight).BeginInit();
            panelTopBar.SuspendLayout();
            panelNavigation.SuspendLayout();
            panelContent.SuspendLayout();
            panelConnectionContent.SuspendLayout();
            panelSinopticoContent.SuspendLayout();
            panelConfigContent.SuspendLayout();
            tabControl1.SuspendLayout();
            SuspendLayout();
            // 
            // panelMailContent
            // 
            panelMailContent.BackColor = Color.FromArgb(45, 45, 48);
            panelMailContent.Location = new Point(0, 0);
            panelMailContent.Margin = new Padding(3, 2, 3, 2);
            panelMailContent.Name = "panelMailContent";
            panelMailContent.Padding = new Padding(26, 22, 26, 22);
            panelMailContent.Size = new Size(1044, 728);
            panelMailContent.TabIndex = 0;
            panelMailContent.Visible = false;
            // 
            // btnAjustesCorreo
            // 
            btnAjustesCorreo.Location = new Point(0, 0);
            btnAjustesCorreo.Name = "btnAjustesCorreo";
            btnAjustesCorreo.Size = new Size(75, 23);
            btnAjustesCorreo.TabIndex = 0;
            btnAjustesCorreo.Text = "Ajustes de Correo";
            btnAjustesCorreo.UseVisualStyleBackColor = true;
            // 
            // btnClear
            // 
            btnClear.Location = new Point(0, 0);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(75, 23);
            btnClear.TabIndex = 0;
            btnClear.Text = "Clear";
            btnClear.UseVisualStyleBackColor = true;
            // 
            // circleStatusEmail
            // 
            circleStatusEmail.BackColor = Color.Gray;
            circleStatusEmail.Location = new Point(1116, 9);
            circleStatusEmail.Margin = new Padding(3, 2, 3, 2);
            circleStatusEmail.Name = "circleStatusEmail";
            circleStatusEmail.Size = new Size(14, 12);
            circleStatusEmail.TabIndex = 0;
            // 
            // lblStatusEmail
            // 
            lblStatusEmail.AutoSize = true;
            lblStatusEmail.Font = new Font("Segoe UI", 9F);
            lblStatusEmail.ForeColor = Color.Silver;
            lblStatusEmail.Location = new Point(1138, 9);
            lblStatusEmail.Name = "lblStatusEmail";
            lblStatusEmail.Size = new Size(124, 15);
            lblStatusEmail.TabIndex = 0;
            lblStatusEmail.Text = "Correo: Desconectado";
            // 
            // circleStatusModbus
            // 
            circleStatusModbus.BackColor = Color.Gray;
            circleStatusModbus.Location = new Point(1444, 9);
            circleStatusModbus.Margin = new Padding(3, 2, 3, 2);
            circleStatusModbus.Name = "circleStatusModbus";
            circleStatusModbus.Size = new Size(14, 12);
            circleStatusModbus.TabIndex = 0;
            // 
            // lblStatusModbus
            // 
            lblStatusModbus.AutoSize = true;
            lblStatusModbus.Font = new Font("Segoe UI", 9F);
            lblStatusModbus.ForeColor = Color.Silver;
            lblStatusModbus.Location = new Point(1466, 9);
            lblStatusModbus.Name = "lblStatusModbus";
            lblStatusModbus.Size = new Size(132, 15);
            lblStatusModbus.TabIndex = 0;
            lblStatusModbus.Text = "Modbus: Desconectado";
            // 
            // txtServidor
            // 
            txtServidor.BackColor = Color.FromArgb(60, 63, 65);
            txtServidor.BorderStyle = BorderStyle.FixedSingle;
            txtServidor.Font = new Font("Segoe UI", 11F);
            txtServidor.ForeColor = Color.White;
            txtServidor.Location = new Point(210, 70);
            txtServidor.Name = "txtServidor";
            txtServidor.Size = new Size(380, 27);
            txtServidor.TabIndex = 0;
            // 
            // txtPuerto
            // 
            txtPuerto.BackColor = Color.FromArgb(60, 63, 65);
            txtPuerto.BorderStyle = BorderStyle.FixedSingle;
            txtPuerto.Font = new Font("Segoe UI", 11F);
            txtPuerto.ForeColor = Color.White;
            txtPuerto.Location = new Point(210, 115);
            txtPuerto.Name = "txtPuerto";
            txtPuerto.Size = new Size(380, 27);
            txtPuerto.TabIndex = 1;
            // 
            // txtUsuario
            // 
            txtUsuario.BackColor = Color.FromArgb(60, 63, 65);
            txtUsuario.BorderStyle = BorderStyle.FixedSingle;
            txtUsuario.Font = new Font("Segoe UI", 11F);
            txtUsuario.ForeColor = Color.White;
            txtUsuario.Location = new Point(210, 160);
            txtUsuario.Name = "txtUsuario";
            txtUsuario.Size = new Size(380, 27);
            txtUsuario.TabIndex = 2;
            // 
            // txtContraseña
            // 
            txtContraseña.BackColor = Color.FromArgb(60, 63, 65);
            txtContraseña.BorderStyle = BorderStyle.FixedSingle;
            txtContraseña.Font = new Font("Segoe UI", 11F);
            txtContraseña.ForeColor = Color.White;
            txtContraseña.Location = new Point(210, 205);
            txtContraseña.Name = "txtContraseña";
            txtContraseña.PasswordChar = '*';
            txtContraseña.Size = new Size(380, 27);
            txtContraseña.TabIndex = 3;
            // 
            // chkPop3Ssl
            // 
            chkPop3Ssl.AutoSize = true;
            chkPop3Ssl.Font = new Font("Segoe UI", 11F);
            chkPop3Ssl.ForeColor = Color.White;
            chkPop3Ssl.Location = new Point(210, 250);
            chkPop3Ssl.Name = "chkPop3Ssl";
            chkPop3Ssl.Size = new Size(123, 29);
            chkPop3Ssl.TabIndex = 4;
            chkPop3Ssl.Text = "Usar SSL/TLS";
            chkPop3Ssl.UseVisualStyleBackColor = true;
            // 
            // btnProbar
            // 
            btnProbar.BackColor = Color.FromArgb(0, 122, 204);
            btnProbar.FlatAppearance.BorderSize = 0;
            btnProbar.FlatStyle = FlatStyle.Flat;
            btnProbar.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnProbar.ForeColor = Color.White;
            btnProbar.Location = new Point(210, 295);
            btnProbar.Name = "btnProbar";
            btnProbar.Size = new Size(100, 40);
            btnProbar.TabIndex = 5;
            btnProbar.Text = "Probar";
            btnProbar.UseVisualStyleBackColor = false;
            // 
            // btnProcesarEmails
            // 
            btnProcesarEmails.BackColor = Color.FromArgb(0, 122, 204);
            btnProcesarEmails.FlatAppearance.BorderSize = 0;
            btnProcesarEmails.FlatStyle = FlatStyle.Flat;
            btnProcesarEmails.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnProcesarEmails.ForeColor = Color.White;
            btnProcesarEmails.Location = new Point(320, 295);
            btnProcesarEmails.Name = "btnProcesarEmails";
            btnProcesarEmails.Size = new Size(170, 40);
            btnProcesarEmails.TabIndex = 6;
            btnProcesarEmails.Text = "Procesar Emails";
            btnProcesarEmails.UseVisualStyleBackColor = false;
            // 
            // panelEstado
            // 
            panelEstado.BackColor = Color.DarkGray;
            panelEstado.Location = new Point(40, 345);
            panelEstado.Name = "panelEstado";
            panelEstado.Size = new Size(15, 15);
            panelEstado.TabIndex = 7;
            // 
            // lblServerPop3
            // 
            lblServerPop3.AutoSize = true;
            lblServerPop3.Font = new Font("Segoe UI", 11F);
            lblServerPop3.ForeColor = Color.White;
            lblServerPop3.Location = new Point(40, 70);
            lblServerPop3.Name = "lblServerPop3";
            lblServerPop3.Size = new Size(140, 25);
            lblServerPop3.TabIndex = 8;
            lblServerPop3.Text = "Servidor POP3:";
            // 
            // lblPuertoPop3
            // 
            lblPuertoPop3.AutoSize = true;
            lblPuertoPop3.Font = new Font("Segoe UI", 11F);
            lblPuertoPop3.ForeColor = Color.White;
            lblPuertoPop3.Location = new Point(40, 115);
            lblPuertoPop3.Name = "lblPuertoPop3";
            lblPuertoPop3.Size = new Size(140, 25);
            lblPuertoPop3.TabIndex = 9;
            lblPuertoPop3.Text = "Puerto POP3:";
            // 
            // lblUsuarioPop3
            // 
            lblUsuarioPop3.AutoSize = true;
            lblUsuarioPop3.Font = new Font("Segoe UI", 11F);
            lblUsuarioPop3.ForeColor = Color.White;
            lblUsuarioPop3.Location = new Point(40, 160);
            lblUsuarioPop3.Name = "lblUsuarioPop3";
            lblUsuarioPop3.Size = new Size(140, 25);
            lblUsuarioPop3.TabIndex = 10;
            lblUsuarioPop3.Text = "Usuario POP3:";
            // 
            // lblContraseñaPop3
            // 
            lblContraseñaPop3.AutoSize = true;
            lblContraseñaPop3.Font = new Font("Segoe UI", 11F);
            lblContraseñaPop3.ForeColor = Color.White;
            lblContraseñaPop3.Location = new Point(40, 205);
            lblContraseñaPop3.Name = "lblContraseñaPop3";
            lblContraseñaPop3.Size = new Size(140, 25);
            lblContraseñaPop3.TabIndex = 11;
            lblContraseñaPop3.Text = "Contraseña:";
            // 
            // chkUseEws
            // 
            chkUseEws.AutoSize = true;
            chkUseEws.Font = new Font("Segoe UI", 11F);
            chkUseEws.ForeColor = Color.White;
            chkUseEws.Location = new Point(570, 30);
            chkUseEws.Name = "chkUseEws";
            chkUseEws.Size = new Size(117, 29);
            chkUseEws.TabIndex = 12;
            chkUseEws.Text = "Usar EWS";
            chkUseEws.UseVisualStyleBackColor = true;
            // 
            // txtEwsServerUrl
            // 
            txtEwsServerUrl.BackColor = Color.FromArgb(60, 63, 65);
            txtEwsServerUrl.BorderStyle = BorderStyle.FixedSingle;
            txtEwsServerUrl.Font = new Font("Segoe UI", 11F);
            txtEwsServerUrl.ForeColor = Color.White;
            txtEwsServerUrl.Location = new Point(740, 70);
            txtEwsServerUrl.Name = "txtEwsServerUrl";
            txtEwsServerUrl.Size = new Size(380, 27);
            txtEwsServerUrl.TabIndex = 13;
            // 
            // txtEwsUsername
            // 
            txtEwsUsername.BackColor = Color.FromArgb(60, 63, 65);
            txtEwsUsername.BorderStyle = BorderStyle.FixedSingle;
            txtEwsUsername.Font = new Font("Segoe UI", 11F);
            txtEwsUsername.ForeColor = Color.White;
            txtEwsUsername.Location = new Point(740, 115);
            txtEwsUsername.Name = "txtEwsUsername";
            txtEwsUsername.Size = new Size(380, 27);
            txtEwsUsername.TabIndex = 14;
            // 
            // txtEwsPassword
            // 
            txtEwsPassword.BackColor = Color.FromArgb(60, 63, 65);
            txtEwsPassword.BorderStyle = BorderStyle.FixedSingle;
            txtEwsPassword.Font = new Font("Segoe UI", 11F);
            txtEwsPassword.ForeColor = Color.White;
            txtEwsPassword.Location = new Point(740, 160);
            txtEwsPassword.Name = "txtEwsPassword";
            txtEwsPassword.PasswordChar = '*';
            txtEwsPassword.Size = new Size(380, 27);
            txtEwsPassword.TabIndex = 15;
            // 
            // txtEwsDomain
            // 
            txtEwsDomain.BackColor = Color.FromArgb(60, 63, 65);
            txtEwsDomain.BorderStyle = BorderStyle.FixedSingle;
            txtEwsDomain.Font = new Font("Segoe UI", 11F);
            txtEwsDomain.ForeColor = Color.White;
            txtEwsDomain.Location = new Point(740, 205);
            txtEwsDomain.Name = "txtEwsDomain";
            txtEwsDomain.Size = new Size(380, 27);
            txtEwsDomain.TabIndex = 16;
            // 
            // chkEwsUseOAuth
            // 
            chkEwsUseOAuth.AutoSize = true;
            chkEwsUseOAuth.Font = new Font("Segoe UI", 11F);
            chkEwsUseOAuth.ForeColor = Color.White;
            chkEwsUseOAuth.Location = new Point(740, 250);
            chkEwsUseOAuth.Name = "chkEwsUseOAuth";
            chkEwsUseOAuth.Size = new Size(130, 29);
            chkEwsUseOAuth.TabIndex = 17;
            chkEwsUseOAuth.Text = "Usar OAuth";
            chkEwsUseOAuth.UseVisualStyleBackColor = true;
            // 
            // lblEwsServerUrl
            // 
            lblEwsServerUrl.AutoSize = true;
            lblEwsServerUrl.Font = new Font("Segoe UI", 11F);
            lblEwsServerUrl.ForeColor = Color.White;
            lblEwsServerUrl.Location = new Point(570, 70);
            lblEwsServerUrl.Name = "lblEwsServerUrl";
            lblEwsServerUrl.Size = new Size(140, 25);
            lblEwsServerUrl.TabIndex = 18;
            lblEwsServerUrl.Text = "URL Servidor:";
            // 
            // lblEwsUsername
            // 
            lblEwsUsername.AutoSize = true;
            lblEwsUsername.Font = new Font("Segoe UI", 11F);
            lblEwsUsername.ForeColor = Color.White;
            lblEwsUsername.Location = new Point(570, 115);
            lblEwsUsername.Name = "lblEwsUsername";
            lblEwsUsername.Size = new Size(81, 25);
            lblEwsUsername.TabIndex = 19;
            lblEwsUsername.Text = "Usuario:";
            // 
            // lblEwsPassword
            // 
            lblEwsPassword.AutoSize = true;
            lblEwsPassword.Font = new Font("Segoe UI", 11F);
            lblEwsPassword.ForeColor = Color.White;
            lblEwsPassword.Location = new Point(570, 160);
            lblEwsPassword.Name = "lblEwsPassword";
            lblEwsPassword.Size = new Size(110, 25);
            lblEwsPassword.TabIndex = 20;
            lblEwsPassword.Text = "Contraseña:";
            // 
            // lblEwsDomain
            // 
            lblEwsDomain.AutoSize = true;
            lblEwsDomain.Font = new Font("Segoe UI", 11F);
            lblEwsDomain.ForeColor = Color.White;
            lblEwsDomain.Location = new Point(570, 205);
            lblEwsDomain.Name = "lblEwsDomain";
            lblEwsDomain.Size = new Size(87, 25);
            lblEwsDomain.TabIndex = 21;
            lblEwsDomain.Text = "Dominio:";
            // 
            // btnEwsProbar
            // 
            btnEwsProbar.BackColor = Color.FromArgb(0, 122, 204);
            btnEwsProbar.FlatAppearance.BorderSize = 0;
            btnEwsProbar.FlatStyle = FlatStyle.Flat;
            btnEwsProbar.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnEwsProbar.ForeColor = Color.White;
            btnEwsProbar.Location = new Point(740, 295);
            btnEwsProbar.Name = "btnEwsProbar";
            btnEwsProbar.Size = new Size(100, 40);
            btnEwsProbar.TabIndex = 22;
            btnEwsProbar.Text = "Probar";
            btnEwsProbar.UseVisualStyleBackColor = false;
            // 
            // btnEwsProcesarEmails
            // 
            btnEwsProcesarEmails.BackColor = Color.FromArgb(0, 122, 204);
            btnEwsProcesarEmails.FlatAppearance.BorderSize = 0;
            btnEwsProcesarEmails.FlatStyle = FlatStyle.Flat;
            btnEwsProcesarEmails.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnEwsProcesarEmails.ForeColor = Color.White;
            btnEwsProcesarEmails.Location = new Point(850, 295);
            btnEwsProcesarEmails.Name = "btnEwsProcesarEmails";
            btnEwsProcesarEmails.Size = new Size(170, 40);
            btnEwsProcesarEmails.TabIndex = 23;
            btnEwsProcesarEmails.Text = "Procesar Emails";
            btnEwsProcesarEmails.UseVisualStyleBackColor = false;
            // 
            // panelEwsEstado
            // 
            panelEwsEstado.BackColor = Color.DarkGray;
            panelEwsEstado.Location = new Point(570, 345);
            panelEwsEstado.Name = "panelEwsEstado";
            panelEwsEstado.Size = new Size(15, 15);
            panelEwsEstado.TabIndex = 24;
            // 
            // lblEmailNotifications
            // 
            lblEmailNotifications.AutoSize = true;
            lblEmailNotifications.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            lblEmailNotifications.ForeColor = Color.White;
            lblEmailNotifications.Location = new Point(40, 400);
            lblEmailNotifications.Name = "lblEmailNotifications";
            lblEmailNotifications.Size = new Size(207, 25);
            lblEmailNotifications.TabIndex = 25;
            lblEmailNotifications.Text = "Emails de Notificación:";
            // 
            // txtEmailNotif1
            // 
            txtEmailNotif1.BackColor = Color.FromArgb(60, 63, 65);
            txtEmailNotif1.BorderStyle = BorderStyle.FixedSingle;
            txtEmailNotif1.Font = new Font("Segoe UI", 11F);
            txtEmailNotif1.ForeColor = Color.White;
            txtEmailNotif1.Location = new Point(40, 435);
            txtEmailNotif1.Name = "txtEmailNotif1";
            txtEmailNotif1.Size = new Size(450, 27);
            txtEmailNotif1.TabIndex = 26;
            // 
            // txtEmailNotif2
            // 
            txtEmailNotif2.BackColor = Color.FromArgb(60, 63, 65);
            txtEmailNotif2.BorderStyle = BorderStyle.FixedSingle;
            txtEmailNotif2.Font = new Font("Segoe UI", 11F);
            txtEmailNotif2.ForeColor = Color.White;
            txtEmailNotif2.Location = new Point(40, 480);
            txtEmailNotif2.Name = "txtEmailNotif2";
            txtEmailNotif2.Size = new Size(450, 27);
            txtEmailNotif2.TabIndex = 27;
            // 
            // txtEmailNotif3
            // 
            txtEmailNotif3.BackColor = Color.FromArgb(60, 63, 65);
            txtEmailNotif3.BorderStyle = BorderStyle.FixedSingle;
            txtEmailNotif3.Font = new Font("Segoe UI", 11F);
            txtEmailNotif3.ForeColor = Color.White;
            txtEmailNotif3.Location = new Point(40, 525);
            txtEmailNotif3.Name = "txtEmailNotif3";
            txtEmailNotif3.Size = new Size(450, 27);
            txtEmailNotif3.TabIndex = 28;
            // 
            // txtEmailNotif4
            // 
            txtEmailNotif4.BackColor = Color.FromArgb(60, 63, 65);
            txtEmailNotif4.BorderStyle = BorderStyle.FixedSingle;
            txtEmailNotif4.Font = new Font("Segoe UI", 11F);
            txtEmailNotif4.ForeColor = Color.White;
            txtEmailNotif4.Location = new Point(40, 570);
            txtEmailNotif4.Name = "txtEmailNotif4";
            txtEmailNotif4.Size = new Size(450, 27);
            txtEmailNotif4.TabIndex = 29;
            // 
            // txtEmailNotif5
            // 
            txtEmailNotif5.BackColor = Color.FromArgb(60, 63, 65);
            txtEmailNotif5.BorderStyle = BorderStyle.FixedSingle;
            txtEmailNotif5.Font = new Font("Segoe UI", 11F);
            txtEmailNotif5.ForeColor = Color.White;
            txtEmailNotif5.Location = new Point(40, 615);
            txtEmailNotif5.Name = "txtEmailNotif5";
            txtEmailNotif5.Size = new Size(450, 27);
            txtEmailNotif5.TabIndex = 30;
            // 
            // nudCheckInterval
            // 
            nudCheckInterval.Location = new Point(0, 0);
            nudCheckInterval.Name = "nudCheckInterval";
            nudCheckInterval.Size = new Size(120, 23);
            nudCheckInterval.TabIndex = 0;
            // 
            // lblCheckInterval
            // 
            lblCheckInterval.Location = new Point(0, 0);
            lblCheckInterval.Name = "lblCheckInterval";
            lblCheckInterval.Size = new Size(100, 23);
            lblCheckInterval.TabIndex = 0;
            // 
            // panelEstadoHora
            // 
            panelEstadoHora.Location = new Point(0, 0);
            panelEstadoHora.Name = "panelEstadoHora";
            panelEstadoHora.Size = new Size(200, 100);
            panelEstadoHora.TabIndex = 0;
            // 
            // panelSinopticoTabContent
            // 
            panelSinopticoTabContent.Location = new Point(0, 0);
            panelSinopticoTabContent.Name = "panelSinopticoTabContent";
            panelSinopticoTabContent.Size = new Size(200, 100);
            panelSinopticoTabContent.TabIndex = 0;
            // 
            // txtSinopticoFilter
            // 
            txtSinopticoFilter.Location = new Point(0, 0);
            txtSinopticoFilter.Margin = new Padding(3, 2, 3, 2);
            txtSinopticoFilter.Name = "txtSinopticoFilter";
            txtSinopticoFilter.Size = new Size(88, 23);
            txtSinopticoFilter.TabIndex = 0;
            // 
            // dgvSinopticoData
            // 
            dgvSinopticoData.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvSinopticoData.Location = new Point(0, 0);
            dgvSinopticoData.Margin = new Padding(3, 2, 3, 2);
            dgvSinopticoData.Name = "dgvSinopticoData";
            dgvSinopticoData.RowHeadersWidth = 51;
            dgvSinopticoData.Size = new Size(210, 112);
            dgvSinopticoData.TabIndex = 0;
            // 
            // btnNavModbus
            // 
            btnNavModbus.BackColor = Color.FromArgb(60, 63, 65);
            btnNavModbus.Cursor = Cursors.Hand;
            btnNavModbus.FlatAppearance.BorderSize = 0;
            btnNavModbus.FlatStyle = FlatStyle.Flat;
            btnNavModbus.Font = new Font("Segoe UI", 12F);
            btnNavModbus.ForeColor = Color.White;
            btnNavModbus.Location = new Point(1326, 8);
            btnNavModbus.Margin = new Padding(3, 2, 3, 2);
            btnNavModbus.Name = "btnNavModbus";
            btnNavModbus.Size = new Size(324, 38);
            btnNavModbus.TabIndex = 5;
            btnNavModbus.Text = "Modbus";
            btnNavModbus.UseVisualStyleBackColor = false;
            btnNavModbus.Click += BtnNavModbus_Click;
            // 
            // panelModbusContent
            // 
            panelModbusContent.BackColor = Color.FromArgb(45, 45, 48);
            panelModbusContent.Location = new Point(0, 0);
            panelModbusContent.Margin = new Padding(3, 2, 3, 2);
            panelModbusContent.Name = "panelModbusContent";
            panelModbusContent.Padding = new Padding(18, 15, 18, 15);
            panelModbusContent.Size = new Size(1044, 728);
            panelModbusContent.TabIndex = 0;
            panelModbusContent.Visible = false;
            // 
            // lblModbusConfigSelectionLeft
            // 
            lblModbusConfigSelectionLeft.Location = new Point(0, 0);
            lblModbusConfigSelectionLeft.Name = "lblModbusConfigSelectionLeft";
            lblModbusConfigSelectionLeft.Size = new Size(100, 23);
            lblModbusConfigSelectionLeft.TabIndex = 0;
            // 
            // cmbModbusConfigLeft
            // 
            cmbModbusConfigLeft.Location = new Point(0, 0);
            cmbModbusConfigLeft.Name = "cmbModbusConfigLeft";
            cmbModbusConfigLeft.Size = new Size(121, 23);
            cmbModbusConfigLeft.TabIndex = 0;
            // 
            // txtModbusConfigNameLeft
            // 
            txtModbusConfigNameLeft.Location = new Point(0, 0);
            txtModbusConfigNameLeft.Name = "txtModbusConfigNameLeft";
            txtModbusConfigNameLeft.Size = new Size(100, 23);
            txtModbusConfigNameLeft.TabIndex = 0;
            // 
            // btnModbusSaveConfigLeft
            // 
            btnModbusSaveConfigLeft.Location = new Point(0, 0);
            btnModbusSaveConfigLeft.Name = "btnModbusSaveConfigLeft";
            btnModbusSaveConfigLeft.Size = new Size(75, 23);
            btnModbusSaveConfigLeft.TabIndex = 0;
            btnModbusSaveConfigLeft.Text = "button1";
            btnModbusSaveConfigLeft.UseVisualStyleBackColor = true;
            // 
            // btnModbusNewConfigLeft
            // 
            btnModbusNewConfigLeft.Location = new Point(0, 0);
            btnModbusNewConfigLeft.Name = "btnModbusNewConfigLeft";
            btnModbusNewConfigLeft.Size = new Size(75, 23);
            btnModbusNewConfigLeft.TabIndex = 0;
            btnModbusNewConfigLeft.Text = "button1";
            btnModbusNewConfigLeft.UseVisualStyleBackColor = true;
            // 
            // btnModbusDeleteConfigLeft
            // 
            btnModbusDeleteConfigLeft.Location = new Point(0, 0);
            btnModbusDeleteConfigLeft.Name = "btnModbusDeleteConfigLeft";
            btnModbusDeleteConfigLeft.Size = new Size(75, 23);
            btnModbusDeleteConfigLeft.TabIndex = 0;
            btnModbusDeleteConfigLeft.Text = "button1";
            btnModbusDeleteConfigLeft.UseVisualStyleBackColor = true;
            // 
            // grpModbusConnectionSettingsLeft
            // 
            grpModbusConnectionSettingsLeft.Location = new Point(0, 0);
            grpModbusConnectionSettingsLeft.Name = "grpModbusConnectionSettingsLeft";
            grpModbusConnectionSettingsLeft.Size = new Size(200, 100);
            grpModbusConnectionSettingsLeft.TabIndex = 0;
            grpModbusConnectionSettingsLeft.TabStop = false;
            grpModbusConnectionSettingsLeft.Text = "groupBox1";
            // 
            // rdoModbusTCPLeft
            // 
            rdoModbusTCPLeft.Location = new Point(0, 0);
            rdoModbusTCPLeft.Name = "rdoModbusTCPLeft";
            rdoModbusTCPLeft.Size = new Size(103, 24);
            rdoModbusTCPLeft.TabIndex = 0;
            rdoModbusTCPLeft.TabStop = true;
            rdoModbusTCPLeft.Text = "radioButton1";
            rdoModbusTCPLeft.UseVisualStyleBackColor = true;
            // 
            // rdoModbusRTULeft
            // 
            rdoModbusRTULeft.Location = new Point(0, 0);
            rdoModbusRTULeft.Name = "rdoModbusRTULeft";
            rdoModbusRTULeft.Size = new Size(103, 24);
            rdoModbusRTULeft.TabIndex = 0;
            rdoModbusRTULeft.TabStop = true;
            rdoModbusRTULeft.Text = "radioButton1";
            rdoModbusRTULeft.UseVisualStyleBackColor = true;
            // 
            // lblModbusIPLeft
            // 
            lblModbusIPLeft.Location = new Point(0, 0);
            lblModbusIPLeft.Name = "lblModbusIPLeft";
            lblModbusIPLeft.Size = new Size(100, 23);
            lblModbusIPLeft.TabIndex = 0;
            // 
            // txtModbusIPLeft
            // 
            txtModbusIPLeft.Location = new Point(0, 0);
            txtModbusIPLeft.Name = "txtModbusIPLeft";
            txtModbusIPLeft.Size = new Size(100, 23);
            txtModbusIPLeft.TabIndex = 0;
            // 
            // lblModbusPortLeft
            // 
            lblModbusPortLeft.Location = new Point(0, 0);
            lblModbusPortLeft.Name = "lblModbusPortLeft";
            lblModbusPortLeft.Size = new Size(100, 23);
            lblModbusPortLeft.TabIndex = 0;
            // 
            // nudModbusPortLeft
            // 
            nudModbusPortLeft.Location = new Point(0, 0);
            nudModbusPortLeft.Maximum = new decimal(new int[] { 9999, 0, 0, 0 });
            nudModbusPortLeft.Name = "nudModbusPortLeft";
            nudModbusPortLeft.Size = new Size(120, 23);
            nudModbusPortLeft.TabIndex = 0;
            // 
            // lblModbusComPortLeft
            // 
            lblModbusComPortLeft.Location = new Point(0, 0);
            lblModbusComPortLeft.Name = "lblModbusComPortLeft";
            lblModbusComPortLeft.Size = new Size(100, 23);
            lblModbusComPortLeft.TabIndex = 0;
            // 
            // cmbModbusComPortLeft
            // 
            cmbModbusComPortLeft.Location = new Point(0, 0);
            cmbModbusComPortLeft.Name = "cmbModbusComPortLeft";
            cmbModbusComPortLeft.Size = new Size(121, 23);
            cmbModbusComPortLeft.TabIndex = 0;
            // 
            // lblModbusBaudRateLeft
            // 
            lblModbusBaudRateLeft.Location = new Point(0, 0);
            lblModbusBaudRateLeft.Name = "lblModbusBaudRateLeft";
            lblModbusBaudRateLeft.Size = new Size(100, 23);
            lblModbusBaudRateLeft.TabIndex = 0;
            // 
            // cmbModbusBaudRateLeft
            // 
            cmbModbusBaudRateLeft.Location = new Point(0, 0);
            cmbModbusBaudRateLeft.Name = "cmbModbusBaudRateLeft";
            cmbModbusBaudRateLeft.Size = new Size(121, 23);
            cmbModbusBaudRateLeft.TabIndex = 0;
            // 
            // lblModbusParityLeft
            // 
            lblModbusParityLeft.Location = new Point(0, 0);
            lblModbusParityLeft.Name = "lblModbusParityLeft";
            lblModbusParityLeft.Size = new Size(100, 23);
            lblModbusParityLeft.TabIndex = 0;
            // 
            // cmbModbusParityLeft
            // 
            cmbModbusParityLeft.Location = new Point(0, 0);
            cmbModbusParityLeft.Name = "cmbModbusParityLeft";
            cmbModbusParityLeft.Size = new Size(121, 23);
            cmbModbusParityLeft.TabIndex = 0;
            // 
            // lblModbusDataBitsLeft
            // 
            lblModbusDataBitsLeft.Location = new Point(0, 0);
            lblModbusDataBitsLeft.Name = "lblModbusDataBitsLeft";
            lblModbusDataBitsLeft.Size = new Size(100, 23);
            lblModbusDataBitsLeft.TabIndex = 0;
            // 
            // cmbModbusDataBitsLeft
            // 
            cmbModbusDataBitsLeft.Location = new Point(0, 0);
            cmbModbusDataBitsLeft.Name = "cmbModbusDataBitsLeft";
            cmbModbusDataBitsLeft.Size = new Size(121, 23);
            cmbModbusDataBitsLeft.TabIndex = 0;
            // 
            // lblModbusStopBitsLeft
            // 
            lblModbusStopBitsLeft.Location = new Point(0, 0);
            lblModbusStopBitsLeft.Name = "lblModbusStopBitsLeft";
            lblModbusStopBitsLeft.Size = new Size(100, 23);
            lblModbusStopBitsLeft.TabIndex = 0;
            // 
            // cmbModbusStopBitsLeft
            // 
            cmbModbusStopBitsLeft.Location = new Point(0, 0);
            cmbModbusStopBitsLeft.Name = "cmbModbusStopBitsLeft";
            cmbModbusStopBitsLeft.Size = new Size(121, 23);
            cmbModbusStopBitsLeft.TabIndex = 0;
            // 
            // lblModbusDeviceIdLeft
            // 
            lblModbusDeviceIdLeft.Location = new Point(0, 0);
            lblModbusDeviceIdLeft.Name = "lblModbusDeviceIdLeft";
            lblModbusDeviceIdLeft.Size = new Size(100, 23);
            lblModbusDeviceIdLeft.TabIndex = 0;
            // 
            // nudModbusDeviceIdLeft
            // 
            nudModbusDeviceIdLeft.Location = new Point(0, 0);
            nudModbusDeviceIdLeft.Name = "nudModbusDeviceIdLeft";
            nudModbusDeviceIdLeft.Size = new Size(120, 23);
            nudModbusDeviceIdLeft.TabIndex = 0;
            // 
            // btnModbusConnectLeft
            // 
            btnModbusConnectLeft.Location = new Point(0, 0);
            btnModbusConnectLeft.Name = "btnModbusConnectLeft";
            btnModbusConnectLeft.Size = new Size(75, 23);
            btnModbusConnectLeft.TabIndex = 0;
            btnModbusConnectLeft.Text = "button1";
            btnModbusConnectLeft.UseVisualStyleBackColor = true;
            // 
            // btnModbusDisconnectLeft
            // 
            btnModbusDisconnectLeft.Location = new Point(0, 0);
            btnModbusDisconnectLeft.Name = "btnModbusDisconnectLeft";
            btnModbusDisconnectLeft.Size = new Size(75, 23);
            btnModbusDisconnectLeft.TabIndex = 0;
            btnModbusDisconnectLeft.Text = "button1";
            btnModbusDisconnectLeft.UseVisualStyleBackColor = true;
            // 
            // lblModbusStatusLeft
            // 
            lblModbusStatusLeft.Location = new Point(0, 0);
            lblModbusStatusLeft.Name = "lblModbusStatusLeft";
            lblModbusStatusLeft.Size = new Size(100, 23);
            lblModbusStatusLeft.TabIndex = 0;
            // 
            // grpModbusOperationsLeft
            // 
            grpModbusOperationsLeft.Location = new Point(0, 0);
            grpModbusOperationsLeft.Name = "grpModbusOperationsLeft";
            grpModbusOperationsLeft.Size = new Size(200, 100);
            grpModbusOperationsLeft.TabIndex = 0;
            grpModbusOperationsLeft.TabStop = false;
            grpModbusOperationsLeft.Text = "groupBox1";
            // 
            // lblModbusRegisterAddressLeft
            // 
            lblModbusRegisterAddressLeft.Location = new Point(0, 0);
            lblModbusRegisterAddressLeft.Name = "lblModbusRegisterAddressLeft";
            lblModbusRegisterAddressLeft.Size = new Size(100, 23);
            lblModbusRegisterAddressLeft.TabIndex = 0;
            // 
            // txtModbusRegisterAddressLeft
            // 
            txtModbusRegisterAddressLeft.Location = new Point(0, 0);
            txtModbusRegisterAddressLeft.Name = "txtModbusRegisterAddressLeft";
            txtModbusRegisterAddressLeft.Size = new Size(100, 23);
            txtModbusRegisterAddressLeft.TabIndex = 0;
            // 
            // lblModbusRegisterCountLeft
            // 
            lblModbusRegisterCountLeft.Location = new Point(0, 0);
            lblModbusRegisterCountLeft.Name = "lblModbusRegisterCountLeft";
            lblModbusRegisterCountLeft.Size = new Size(100, 23);
            lblModbusRegisterCountLeft.TabIndex = 0;
            // 
            // nudModbusRegisterCountLeft
            // 
            nudModbusRegisterCountLeft.Location = new Point(0, 0);
            nudModbusRegisterCountLeft.Name = "nudModbusRegisterCountLeft";
            nudModbusRegisterCountLeft.Size = new Size(120, 23);
            nudModbusRegisterCountLeft.TabIndex = 0;
            // 
            // lblModbusWriteValueLeft
            // 
            lblModbusWriteValueLeft.Location = new Point(0, 0);
            lblModbusWriteValueLeft.Name = "lblModbusWriteValueLeft";
            lblModbusWriteValueLeft.Size = new Size(100, 23);
            lblModbusWriteValueLeft.TabIndex = 0;
            // 
            // txtModbusWriteValueLeft
            // 
            txtModbusWriteValueLeft.Location = new Point(0, 0);
            txtModbusWriteValueLeft.Name = "txtModbusWriteValueLeft";
            txtModbusWriteValueLeft.Size = new Size(100, 23);
            txtModbusWriteValueLeft.TabIndex = 0;
            // 
            // chkModbusWriteCoilValueLeft
            // 
            chkModbusWriteCoilValueLeft.Location = new Point(0, 0);
            chkModbusWriteCoilValueLeft.Name = "chkModbusWriteCoilValueLeft";
            chkModbusWriteCoilValueLeft.Size = new Size(104, 24);
            chkModbusWriteCoilValueLeft.TabIndex = 0;
            chkModbusWriteCoilValueLeft.Text = "checkBox1";
            chkModbusWriteCoilValueLeft.UseVisualStyleBackColor = true;
            // 
            // btnModbusReadHoldingRegistersLeft
            // 
            btnModbusReadHoldingRegistersLeft.Location = new Point(0, 0);
            btnModbusReadHoldingRegistersLeft.Name = "btnModbusReadHoldingRegistersLeft";
            btnModbusReadHoldingRegistersLeft.Size = new Size(75, 23);
            btnModbusReadHoldingRegistersLeft.TabIndex = 0;
            btnModbusReadHoldingRegistersLeft.Text = "button1";
            btnModbusReadHoldingRegistersLeft.UseVisualStyleBackColor = true;
            // 
            // btnModbusReadInputRegistersLeft
            // 
            btnModbusReadInputRegistersLeft.Location = new Point(0, 0);
            btnModbusReadInputRegistersLeft.Name = "btnModbusReadInputRegistersLeft";
            btnModbusReadInputRegistersLeft.Size = new Size(75, 23);
            btnModbusReadInputRegistersLeft.TabIndex = 0;
            btnModbusReadInputRegistersLeft.Text = "button1";
            btnModbusReadInputRegistersLeft.UseVisualStyleBackColor = true;
            // 
            // btnModbusReadCoilsLeft
            // 
            btnModbusReadCoilsLeft.Location = new Point(0, 0);
            btnModbusReadCoilsLeft.Name = "btnModbusReadCoilsLeft";
            btnModbusReadCoilsLeft.Size = new Size(75, 23);
            btnModbusReadCoilsLeft.TabIndex = 0;
            btnModbusReadCoilsLeft.Text = "button1";
            btnModbusReadCoilsLeft.UseVisualStyleBackColor = true;
            // 
            // btnModbusReadDiscreteInputsLeft
            // 
            btnModbusReadDiscreteInputsLeft.Location = new Point(0, 0);
            btnModbusReadDiscreteInputsLeft.Name = "btnModbusReadDiscreteInputsLeft";
            btnModbusReadDiscreteInputsLeft.Size = new Size(75, 23);
            btnModbusReadDiscreteInputsLeft.TabIndex = 0;
            btnModbusReadDiscreteInputsLeft.Text = "button1";
            btnModbusReadDiscreteInputsLeft.UseVisualStyleBackColor = true;
            // 
            // btnModbusWriteSingleRegisterLeft
            // 
            btnModbusWriteSingleRegisterLeft.Location = new Point(0, 0);
            btnModbusWriteSingleRegisterLeft.Name = "btnModbusWriteSingleRegisterLeft";
            btnModbusWriteSingleRegisterLeft.Size = new Size(75, 23);
            btnModbusWriteSingleRegisterLeft.TabIndex = 0;
            btnModbusWriteSingleRegisterLeft.Text = "button1";
            btnModbusWriteSingleRegisterLeft.UseVisualStyleBackColor = true;
            // 
            // btnModbusWriteSingleCoilLeft
            // 
            btnModbusWriteSingleCoilLeft.Location = new Point(0, 0);
            btnModbusWriteSingleCoilLeft.Name = "btnModbusWriteSingleCoilLeft";
            btnModbusWriteSingleCoilLeft.Size = new Size(75, 23);
            btnModbusWriteSingleCoilLeft.TabIndex = 0;
            btnModbusWriteSingleCoilLeft.Text = "button1";
            btnModbusWriteSingleCoilLeft.UseVisualStyleBackColor = true;
            // 
            // lblModbusLastErrorLeft
            // 
            lblModbusLastErrorLeft.Location = new Point(0, 0);
            lblModbusLastErrorLeft.Name = "lblModbusLastErrorLeft";
            lblModbusLastErrorLeft.Size = new Size(100, 23);
            lblModbusLastErrorLeft.TabIndex = 0;
            // 
            // dgvModbusReadResultsLeft
            // 
            dgvModbusReadResultsLeft.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvModbusReadResultsLeft.Location = new Point(0, 0);
            dgvModbusReadResultsLeft.Name = "dgvModbusReadResultsLeft";
            dgvModbusReadResultsLeft.RowHeadersWidth = 51;
            dgvModbusReadResultsLeft.Size = new Size(240, 150);
            dgvModbusReadResultsLeft.TabIndex = 0;
            // 
            // splitContainerModbus
            // 
            splitContainerModbus.Location = new Point(0, 0);
            splitContainerModbus.Name = "splitContainerModbus";
            splitContainerModbus.Size = new Size(150, 100);
            splitContainerModbus.TabIndex = 0;
            // 
            // lblModbusConfigSelectionRight
            // 
            lblModbusConfigSelectionRight.Location = new Point(0, 0);
            lblModbusConfigSelectionRight.Name = "lblModbusConfigSelectionRight";
            lblModbusConfigSelectionRight.Size = new Size(100, 23);
            lblModbusConfigSelectionRight.TabIndex = 0;
            // 
            // cmbModbusConfigRight
            // 
            cmbModbusConfigRight.Location = new Point(0, 0);
            cmbModbusConfigRight.Name = "cmbModbusConfigRight";
            cmbModbusConfigRight.Size = new Size(121, 23);
            cmbModbusConfigRight.TabIndex = 0;
            // 
            // txtModbusConfigNameRight
            // 
            txtModbusConfigNameRight.Location = new Point(0, 0);
            txtModbusConfigNameRight.Name = "txtModbusConfigNameRight";
            txtModbusConfigNameRight.Size = new Size(100, 23);
            txtModbusConfigNameRight.TabIndex = 0;
            // 
            // btnModbusSaveConfigRight
            // 
            btnModbusSaveConfigRight.Location = new Point(0, 0);
            btnModbusSaveConfigRight.Name = "btnModbusSaveConfigRight";
            btnModbusSaveConfigRight.Size = new Size(75, 23);
            btnModbusSaveConfigRight.TabIndex = 0;
            btnModbusSaveConfigRight.Text = "button1";
            btnModbusSaveConfigRight.UseVisualStyleBackColor = true;
            // 
            // btnModbusNewConfigRight
            // 
            btnModbusNewConfigRight.Location = new Point(0, 0);
            btnModbusNewConfigRight.Name = "btnModbusNewConfigRight";
            btnModbusNewConfigRight.Size = new Size(75, 23);
            btnModbusNewConfigRight.TabIndex = 0;
            btnModbusNewConfigRight.Text = "button1";
            btnModbusNewConfigRight.UseVisualStyleBackColor = true;
            // 
            // btnModbusDeleteConfigRight
            // 
            btnModbusDeleteConfigRight.Location = new Point(0, 0);
            btnModbusDeleteConfigRight.Name = "btnModbusDeleteConfigRight";
            btnModbusDeleteConfigRight.Size = new Size(75, 23);
            btnModbusDeleteConfigRight.TabIndex = 0;
            btnModbusDeleteConfigRight.Text = "button1";
            btnModbusDeleteConfigRight.UseVisualStyleBackColor = true;
            // 
            // grpModbusConnectionSettingsRight
            // 
            grpModbusConnectionSettingsRight.Location = new Point(0, 0);
            grpModbusConnectionSettingsRight.Name = "grpModbusConnectionSettingsRight";
            grpModbusConnectionSettingsRight.Size = new Size(200, 100);
            grpModbusConnectionSettingsRight.TabIndex = 0;
            grpModbusConnectionSettingsRight.TabStop = false;
            grpModbusConnectionSettingsRight.Text = "groupBox1";
            // 
            // rdoModbusTCPRight
            // 
            rdoModbusTCPRight.Location = new Point(0, 0);
            rdoModbusTCPRight.Name = "rdoModbusTCPRight";
            rdoModbusTCPRight.Size = new Size(103, 24);
            rdoModbusTCPRight.TabIndex = 0;
            rdoModbusTCPRight.TabStop = true;
            rdoModbusTCPRight.Text = "radioButton1";
            rdoModbusTCPRight.UseVisualStyleBackColor = true;
            // 
            // rdoModbusRTURight
            // 
            rdoModbusRTURight.Location = new Point(0, 0);
            rdoModbusRTURight.Name = "rdoModbusRTURight";
            rdoModbusRTURight.Size = new Size(103, 24);
            rdoModbusRTURight.TabIndex = 0;
            rdoModbusRTURight.TabStop = true;
            rdoModbusRTURight.Text = "radioButton1";
            rdoModbusRTURight.UseVisualStyleBackColor = true;
            // 
            // lblModbusIPRight
            // 
            lblModbusIPRight.Location = new Point(0, 0);
            lblModbusIPRight.Name = "lblModbusIPRight";
            lblModbusIPRight.Size = new Size(100, 23);
            lblModbusIPRight.TabIndex = 0;
            // 
            // txtModbusIPRight
            // 
            txtModbusIPRight.Location = new Point(0, 0);
            txtModbusIPRight.Name = "txtModbusIPRight";
            txtModbusIPRight.Size = new Size(100, 23);
            txtModbusIPRight.TabIndex = 0;
            // 
            // lblModbusPortRight
            // 
            lblModbusPortRight.Location = new Point(0, 0);
            lblModbusPortRight.Name = "lblModbusPortRight";
            lblModbusPortRight.Size = new Size(100, 23);
            lblModbusPortRight.TabIndex = 0;
            // 
            // nudModbusPortRight
            // 
            nudModbusPortRight.Location = new Point(0, 0);
            nudModbusPortRight.Maximum = new decimal(new int[] { 9999, 0, 0, 0 });
            nudModbusPortRight.Name = "nudModbusPortRight";
            nudModbusPortRight.Size = new Size(120, 23);
            nudModbusPortRight.TabIndex = 0;
            // 
            // lblModbusComPortRight
            // 
            lblModbusComPortRight.Location = new Point(0, 0);
            lblModbusComPortRight.Name = "lblModbusComPortRight";
            lblModbusComPortRight.Size = new Size(100, 23);
            lblModbusComPortRight.TabIndex = 0;
            // 
            // cmbModbusComPortRight
            // 
            cmbModbusComPortRight.Location = new Point(0, 0);
            cmbModbusComPortRight.Name = "cmbModbusComPortRight";
            cmbModbusComPortRight.Size = new Size(121, 23);
            cmbModbusComPortRight.TabIndex = 0;
            // 
            // lblModbusBaudRateRight
            // 
            lblModbusBaudRateRight.Location = new Point(0, 0);
            lblModbusBaudRateRight.Name = "lblModbusBaudRateRight";
            lblModbusBaudRateRight.Size = new Size(100, 23);
            lblModbusBaudRateRight.TabIndex = 0;
            // 
            // cmbModbusBaudRateRight
            // 
            cmbModbusBaudRateRight.Location = new Point(0, 0);
            cmbModbusBaudRateRight.Name = "cmbModbusBaudRateRight";
            cmbModbusBaudRateRight.Size = new Size(121, 23);
            cmbModbusBaudRateRight.TabIndex = 0;
            // 
            // lblModbusParityRight
            // 
            lblModbusParityRight.Location = new Point(0, 0);
            lblModbusParityRight.Name = "lblModbusParityRight";
            lblModbusParityRight.Size = new Size(100, 23);
            lblModbusParityRight.TabIndex = 0;
            // 
            // cmbModbusParityRight
            // 
            cmbModbusParityRight.Location = new Point(0, 0);
            cmbModbusParityRight.Name = "cmbModbusParityRight";
            cmbModbusParityRight.Size = new Size(121, 23);
            cmbModbusParityRight.TabIndex = 0;
            // 
            // lblModbusDataBitsRight
            // 
            lblModbusDataBitsRight.Location = new Point(0, 0);
            lblModbusDataBitsRight.Name = "lblModbusDataBitsRight";
            lblModbusDataBitsRight.Size = new Size(100, 23);
            lblModbusDataBitsRight.TabIndex = 0;
            // 
            // cmbModbusDataBitsRight
            // 
            cmbModbusDataBitsRight.Location = new Point(0, 0);
            cmbModbusDataBitsRight.Name = "cmbModbusDataBitsRight";
            cmbModbusDataBitsRight.Size = new Size(121, 23);
            cmbModbusDataBitsRight.TabIndex = 0;
            // 
            // lblModbusStopBitsRight
            // 
            lblModbusStopBitsRight.Location = new Point(0, 0);
            lblModbusStopBitsRight.Name = "lblModbusStopBitsRight";
            lblModbusStopBitsRight.Size = new Size(100, 23);
            lblModbusStopBitsRight.TabIndex = 0;
            // 
            // cmbModbusStopBitsRight
            // 
            cmbModbusStopBitsRight.Location = new Point(0, 0);
            cmbModbusStopBitsRight.Name = "cmbModbusStopBitsRight";
            cmbModbusStopBitsRight.Size = new Size(121, 23);
            cmbModbusStopBitsRight.TabIndex = 0;
            // 
            // lblModbusDeviceIdRight
            // 
            lblModbusDeviceIdRight.Location = new Point(0, 0);
            lblModbusDeviceIdRight.Name = "lblModbusDeviceIdRight";
            lblModbusDeviceIdRight.Size = new Size(100, 23);
            lblModbusDeviceIdRight.TabIndex = 0;
            // 
            // nudModbusDeviceIdRight
            // 
            nudModbusDeviceIdRight.Location = new Point(0, 0);
            nudModbusDeviceIdRight.Name = "nudModbusDeviceIdRight";
            nudModbusDeviceIdRight.Size = new Size(120, 23);
            nudModbusDeviceIdRight.TabIndex = 0;
            // 
            // btnModbusConnectRight
            // 
            btnModbusConnectRight.Location = new Point(0, 0);
            btnModbusConnectRight.Name = "btnModbusConnectRight";
            btnModbusConnectRight.Size = new Size(75, 23);
            btnModbusConnectRight.TabIndex = 0;
            btnModbusConnectRight.Text = "button1";
            btnModbusConnectRight.UseVisualStyleBackColor = true;
            // 
            // btnModbusDisconnectRight
            // 
            btnModbusDisconnectRight.Location = new Point(0, 0);
            btnModbusDisconnectRight.Name = "btnModbusDisconnectRight";
            btnModbusDisconnectRight.Size = new Size(75, 23);
            btnModbusDisconnectRight.TabIndex = 0;
            btnModbusDisconnectRight.Text = "button1";
            btnModbusDisconnectRight.UseVisualStyleBackColor = true;
            // 
            // lblModbusStatusRight
            // 
            lblModbusStatusRight.Location = new Point(0, 0);
            lblModbusStatusRight.Name = "lblModbusStatusRight";
            lblModbusStatusRight.Size = new Size(100, 23);
            lblModbusStatusRight.TabIndex = 0;
            // 
            // grpModbusOperationsRight
            // 
            grpModbusOperationsRight.Location = new Point(0, 0);
            grpModbusOperationsRight.Name = "grpModbusOperationsRight";
            grpModbusOperationsRight.Size = new Size(200, 100);
            grpModbusOperationsRight.TabIndex = 0;
            grpModbusOperationsRight.TabStop = false;
            grpModbusOperationsRight.Text = "groupBox1";
            // 
            // lblModbusRegisterAddressRight
            // 
            lblModbusRegisterAddressRight.Location = new Point(0, 0);
            lblModbusRegisterAddressRight.Name = "lblModbusRegisterAddressRight";
            lblModbusRegisterAddressRight.Size = new Size(100, 23);
            lblModbusRegisterAddressRight.TabIndex = 0;
            // 
            // txtModbusRegisterAddressRight
            // 
            txtModbusRegisterAddressRight.Location = new Point(0, 0);
            txtModbusRegisterAddressRight.Name = "txtModbusRegisterAddressRight";
            txtModbusRegisterAddressRight.Size = new Size(100, 23);
            txtModbusRegisterAddressRight.TabIndex = 0;
            // 
            // lblModbusRegisterCountRight
            // 
            lblModbusRegisterCountRight.Location = new Point(0, 0);
            lblModbusRegisterCountRight.Name = "lblModbusRegisterCountRight";
            lblModbusRegisterCountRight.Size = new Size(100, 23);
            lblModbusRegisterCountRight.TabIndex = 0;
            // 
            // nudModbusRegisterCountRight
            // 
            nudModbusRegisterCountRight.Location = new Point(0, 0);
            nudModbusRegisterCountRight.Name = "nudModbusRegisterCountRight";
            nudModbusRegisterCountRight.Size = new Size(120, 23);
            nudModbusRegisterCountRight.TabIndex = 0;
            // 
            // lblModbusWriteValueRight
            // 
            lblModbusWriteValueRight.Location = new Point(0, 0);
            lblModbusWriteValueRight.Name = "lblModbusWriteValueRight";
            lblModbusWriteValueRight.Size = new Size(100, 23);
            lblModbusWriteValueRight.TabIndex = 0;
            // 
            // txtModbusWriteValueRight
            // 
            txtModbusWriteValueRight.Location = new Point(0, 0);
            txtModbusWriteValueRight.Name = "txtModbusWriteValueRight";
            txtModbusWriteValueRight.Size = new Size(100, 23);
            txtModbusWriteValueRight.TabIndex = 0;
            // 
            // chkModbusWriteCoilValueRight
            // 
            chkModbusWriteCoilValueRight.Location = new Point(0, 0);
            chkModbusWriteCoilValueRight.Name = "chkModbusWriteCoilValueRight";
            chkModbusWriteCoilValueRight.Size = new Size(104, 24);
            chkModbusWriteCoilValueRight.TabIndex = 0;
            chkModbusWriteCoilValueRight.Text = "checkBox1";
            chkModbusWriteCoilValueRight.UseVisualStyleBackColor = true;
            // 
            // btnModbusReadHoldingRegistersRight
            // 
            btnModbusReadHoldingRegistersRight.Location = new Point(0, 0);
            btnModbusReadHoldingRegistersRight.Name = "btnModbusReadHoldingRegistersRight";
            btnModbusReadHoldingRegistersRight.Size = new Size(75, 23);
            btnModbusReadHoldingRegistersRight.TabIndex = 0;
            btnModbusReadHoldingRegistersRight.Text = "button1";
            btnModbusReadHoldingRegistersRight.UseVisualStyleBackColor = true;
            // 
            // btnModbusReadInputRegistersRight
            // 
            btnModbusReadInputRegistersRight.Location = new Point(0, 0);
            btnModbusReadInputRegistersRight.Name = "btnModbusReadInputRegistersRight";
            btnModbusReadInputRegistersRight.Size = new Size(75, 23);
            btnModbusReadInputRegistersRight.TabIndex = 0;
            btnModbusReadInputRegistersRight.Text = "button1";
            btnModbusReadInputRegistersRight.UseVisualStyleBackColor = true;
            // 
            // btnModbusReadCoilsRight
            // 
            btnModbusReadCoilsRight.Location = new Point(0, 0);
            btnModbusReadCoilsRight.Name = "btnModbusReadCoilsRight";
            btnModbusReadCoilsRight.Size = new Size(75, 23);
            btnModbusReadCoilsRight.TabIndex = 0;
            btnModbusReadCoilsRight.Text = "button1";
            btnModbusReadCoilsRight.UseVisualStyleBackColor = true;
            // 
            // btnModbusReadDiscreteInputsRight
            // 
            btnModbusReadDiscreteInputsRight.Location = new Point(0, 0);
            btnModbusReadDiscreteInputsRight.Name = "btnModbusReadDiscreteInputsRight";
            btnModbusReadDiscreteInputsRight.Size = new Size(75, 23);
            btnModbusReadDiscreteInputsRight.TabIndex = 0;
            btnModbusReadDiscreteInputsRight.Text = "button1";
            btnModbusReadDiscreteInputsRight.UseVisualStyleBackColor = true;
            // 
            // btnModbusWriteSingleRegisterRight
            // 
            btnModbusWriteSingleRegisterRight.Location = new Point(0, 0);
            btnModbusWriteSingleRegisterRight.Name = "btnModbusWriteSingleRegisterRight";
            btnModbusWriteSingleRegisterRight.Size = new Size(75, 23);
            btnModbusWriteSingleRegisterRight.TabIndex = 0;
            btnModbusWriteSingleRegisterRight.Text = "button1";
            btnModbusWriteSingleRegisterRight.UseVisualStyleBackColor = true;
            // 
            // btnModbusWriteSingleCoilRight
            // 
            btnModbusWriteSingleCoilRight.Location = new Point(0, 0);
            btnModbusWriteSingleCoilRight.Name = "btnModbusWriteSingleCoilRight";
            btnModbusWriteSingleCoilRight.Size = new Size(75, 23);
            btnModbusWriteSingleCoilRight.TabIndex = 0;
            btnModbusWriteSingleCoilRight.Text = "button1";
            btnModbusWriteSingleCoilRight.UseVisualStyleBackColor = true;
            // 
            // lblModbusLastErrorRight
            // 
            lblModbusLastErrorRight.Location = new Point(0, 0);
            lblModbusLastErrorRight.Name = "lblModbusLastErrorRight";
            lblModbusLastErrorRight.Size = new Size(100, 23);
            lblModbusLastErrorRight.TabIndex = 0;
            // 
            // dgvModbusReadResultsRight
            // 
            dgvModbusReadResultsRight.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvModbusReadResultsRight.Location = new Point(0, 0);
            dgvModbusReadResultsRight.Name = "dgvModbusReadResultsRight";
            dgvModbusReadResultsRight.RowHeadersWidth = 51;
            dgvModbusReadResultsRight.Size = new Size(240, 150);
            dgvModbusReadResultsRight.TabIndex = 0;
            // 
            // panelTopBar
            // 
            panelTopBar.BackColor = Color.FromArgb(30, 30, 30);
            panelTopBar.Controls.Add(btnClose);
            panelTopBar.Controls.Add(lblStatusModbus);
            panelTopBar.Controls.Add(circleStatusModbus);
            panelTopBar.Controls.Add(lblStatusEmail);
            panelTopBar.Controls.Add(circleStatusEmail);
            panelTopBar.Controls.Add(lblStatus);
            panelTopBar.Controls.Add(circleStatus);
            panelTopBar.Controls.Add(lblAppTitle);
            panelTopBar.Location = new Point(0, 0);
            panelTopBar.Margin = new Padding(3, 2, 3, 2);
            panelTopBar.Name = "panelTopBar";
            panelTopBar.Size = new Size(1680, 30);
            panelTopBar.TabIndex = 0;
            // 
            // btnClose
            // 
            btnClose.BackColor = Color.FromArgb(60, 60, 60);
            btnClose.FlatAppearance.BorderColor = Color.FromArgb(100, 100, 100);
            btnClose.FlatAppearance.MouseDownBackColor = Color.FromArgb(241, 112, 122);
            btnClose.FlatAppearance.MouseOverBackColor = Color.FromArgb(232, 17, 35);
            btnClose.FlatStyle = FlatStyle.Flat;
            btnClose.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            btnClose.ForeColor = Color.White;
            btnClose.Location = new Point(1641, 0);
            btnClose.Margin = new Padding(3, 2, 3, 2);
            btnClose.Name = "btnClose";
            btnClose.Size = new Size(39, 30);
            btnClose.TabIndex = 3;
            btnClose.Text = "✕";
            btnClose.UseVisualStyleBackColor = false;
            btnClose.Click += BtnClose_Click;
            // 
            // lblStatus
            // 
            lblStatus.AutoSize = true;
            lblStatus.Font = new Font("Segoe UI", 9F);
            lblStatus.ForeColor = Color.Silver;
            lblStatus.Location = new Point(153, 9);
            lblStatus.Name = "lblStatus";
            lblStatus.Size = new Size(119, 15);
            lblStatus.TabIndex = 2;
            lblStatus.Text = "MQTT: Desconectado";
            // 
            // circleStatus
            // 
            circleStatus.BackColor = Color.Gray;
            circleStatus.Location = new Point(131, 9);
            circleStatus.Margin = new Padding(3, 2, 3, 2);
            circleStatus.Name = "circleStatus";
            circleStatus.Size = new Size(14, 12);
            circleStatus.TabIndex = 1;
            // 
            // lblAppTitle
            // 
            lblAppTitle.AutoSize = true;
            lblAppTitle.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblAppTitle.ForeColor = Color.White;
            lblAppTitle.Location = new Point(709, 8);
            lblAppTitle.Name = "lblAppTitle";
            lblAppTitle.Size = new Size(265, 21);
            lblAppTitle.TabIndex = 0;
            lblAppTitle.Text = "Gestión de Restricciones by Auser";
            // 
            // panelNavigation
            // 
            panelNavigation.BackColor = Color.FromArgb(35, 35, 38);
            panelNavigation.Controls.Add(btnNavModbus);
            panelNavigation.Controls.Add(btnNavMail);
            panelNavigation.Controls.Add(btnNavSinoptico);
            panelNavigation.Controls.Add(btnNavConfig);
            panelNavigation.Controls.Add(btnNavConnection);
            panelNavigation.Location = new Point(0, 30);
            panelNavigation.Margin = new Padding(3, 2, 3, 2);
            panelNavigation.Name = "panelNavigation";
            panelNavigation.Padding = new Padding(13, 11, 13, 11);
            panelNavigation.Size = new Size(1680, 52);
            panelNavigation.TabIndex = 1;
            // 
            // btnNavMail
            // 
            btnNavMail.BackColor = Color.FromArgb(60, 63, 65);
            btnNavMail.Cursor = Cursors.Hand;
            btnNavMail.FlatAppearance.BorderSize = 0;
            btnNavMail.FlatStyle = FlatStyle.Flat;
            btnNavMail.Font = new Font("Segoe UI", 12F);
            btnNavMail.ForeColor = Color.White;
            btnNavMail.Location = new Point(998, 8);
            btnNavMail.Margin = new Padding(3, 2, 3, 2);
            btnNavMail.Name = "btnNavMail";
            btnNavMail.Size = new Size(324, 38);
            btnNavMail.TabIndex = 3;
            btnNavMail.Text = "Correo";
            btnNavMail.UseVisualStyleBackColor = false;
            btnNavMail.Click += BtnNavMail_Click;
            // 
            // btnNavSinoptico
            // 
            btnNavSinoptico.BackColor = Color.FromArgb(60, 63, 65);
            btnNavSinoptico.Cursor = Cursors.Hand;
            btnNavSinoptico.FlatAppearance.BorderSize = 0;
            btnNavSinoptico.FlatStyle = FlatStyle.Flat;
            btnNavSinoptico.Font = new Font("Segoe UI", 12F);
            btnNavSinoptico.ForeColor = Color.White;
            btnNavSinoptico.Location = new Point(669, 8);
            btnNavSinoptico.Margin = new Padding(3, 2, 3, 2);
            btnNavSinoptico.Name = "btnNavSinoptico";
            btnNavSinoptico.Size = new Size(324, 38);
            btnNavSinoptico.TabIndex = 2;
            btnNavSinoptico.Text = "Sinóptico";
            btnNavSinoptico.UseVisualStyleBackColor = false;
            btnNavSinoptico.Click += BtnNavSubscribe_Click;
            // 
            // btnNavConfig
            // 
            btnNavConfig.BackColor = Color.FromArgb(60, 63, 65);
            btnNavConfig.Cursor = Cursors.Hand;
            btnNavConfig.FlatAppearance.BorderSize = 0;
            btnNavConfig.FlatStyle = FlatStyle.Flat;
            btnNavConfig.Font = new Font("Segoe UI", 12F);
            btnNavConfig.ForeColor = Color.White;
            btnNavConfig.Location = new Point(341, 8);
            btnNavConfig.Margin = new Padding(3, 2, 3, 2);
            btnNavConfig.Name = "btnNavConfig";
            btnNavConfig.Size = new Size(324, 38);
            btnNavConfig.TabIndex = 1;
            btnNavConfig.Text = "Configuración MQTT";
            btnNavConfig.UseVisualStyleBackColor = false;
            btnNavConfig.Click += BtnNavPublish_Click;
            // 
            // btnNavConnection
            // 
            btnNavConnection.BackColor = Color.FromArgb(0, 122, 204);
            btnNavConnection.Cursor = Cursors.Hand;
            btnNavConnection.FlatAppearance.BorderSize = 0;
            btnNavConnection.FlatStyle = FlatStyle.Flat;
            btnNavConnection.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnNavConnection.ForeColor = Color.White;
            btnNavConnection.Location = new Point(13, 8);
            btnNavConnection.Margin = new Padding(3, 2, 3, 2);
            btnNavConnection.Name = "btnNavConnection";
            btnNavConnection.Size = new Size(324, 38);
            btnNavConnection.TabIndex = 0;
            btnNavConnection.Text = "Conexión";
            btnNavConnection.UseVisualStyleBackColor = false;
            btnNavConnection.Click += BtnNavConnection_Click;
            // 
            // panelContent
            // 
            panelContent.BackColor = Color.FromArgb(45, 45, 48);
            panelContent.Controls.Add(panelConnectionContent);
            panelContent.Controls.Add(panelModbusContent);
            panelContent.Controls.Add(panelMailContent);
            panelContent.Controls.Add(panelSinopticoContent);
            panelContent.Controls.Add(panelConfigContent);
            panelContent.Location = new Point(0, 82);
            panelContent.Margin = new Padding(3, 2, 3, 2);
            panelContent.Name = "panelContent";
            panelContent.Size = new Size(1045, 728);
            panelContent.TabIndex = 2;
            // 
            // panelConnectionContent
            // 
            panelConnectionContent.BackColor = Color.FromArgb(45, 45, 48);
            panelConnectionContent.Controls.Add(panelProfiles);
            panelConnectionContent.Controls.Add(btnDisconnect);
            panelConnectionContent.Controls.Add(btnConnect);
            panelConnectionContent.Controls.Add(rdoSelfSigned);
            panelConnectionContent.Controls.Add(rdoCASigned);
            panelConnectionContent.Controls.Add(chkSSL_TLS);
            panelConnectionContent.Controls.Add(txtPassword);
            panelConnectionContent.Controls.Add(lblPassword);
            panelConnectionContent.Controls.Add(txtUsername);
            panelConnectionContent.Controls.Add(lblUsername);
            panelConnectionContent.Controls.Add(txtClientID);
            panelConnectionContent.Controls.Add(lblClientID);
            panelConnectionContent.Controls.Add(txtPort);
            panelConnectionContent.Controls.Add(lblPort);
            panelConnectionContent.Controls.Add(txtHostAddress);
            panelConnectionContent.Controls.Add(cmbHost);
            panelConnectionContent.Controls.Add(lblHost);
            panelConnectionContent.Controls.Add(txtName);
            panelConnectionContent.Controls.Add(lblName);
            panelConnectionContent.Location = new Point(0, 0);
            panelConnectionContent.Margin = new Padding(3, 2, 3, 2);
            panelConnectionContent.Name = "panelConnectionContent";
            panelConnectionContent.Padding = new Padding(18, 15, 18, 15);
            panelConnectionContent.Size = new Size(1262, 728);
            panelConnectionContent.TabIndex = 0;
            // 
            // btnDisconnect
            // 
            btnDisconnect.BackColor = Color.FromArgb(209, 17, 65);
            btnDisconnect.FlatAppearance.BorderSize = 0;
            btnDisconnect.FlatStyle = FlatStyle.Flat;
            btnDisconnect.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnDisconnect.ForeColor = Color.White;
            btnDisconnect.Location = new Point(293, 351);
            btnDisconnect.Margin = new Padding(3, 2, 3, 2);
            btnDisconnect.Name = "btnDisconnect";
            btnDisconnect.Size = new Size(105, 30);
            btnDisconnect.TabIndex = 18;
            btnDisconnect.Text = "Desconectar";
            btnDisconnect.UseVisualStyleBackColor = false;
            // 
            // btnConnect
            // 
            btnConnect.BackColor = Color.FromArgb(0, 122, 204);
            btnConnect.FlatAppearance.BorderSize = 0;
            btnConnect.FlatStyle = FlatStyle.Flat;
            btnConnect.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnConnect.ForeColor = Color.White;
            btnConnect.Location = new Point(131, 351);
            btnConnect.Margin = new Padding(3, 2, 3, 2);
            btnConnect.Name = "btnConnect";
            btnConnect.Size = new Size(105, 30);
            btnConnect.TabIndex = 17;
            btnConnect.Text = "Conectar";
            btnConnect.UseVisualStyleBackColor = false;
            // 
            // rdoSelfSigned
            // 
            rdoSelfSigned.AutoSize = true;
            rdoSelfSigned.Font = new Font("Segoe UI", 11F);
            rdoSelfSigned.ForeColor = Color.White;
            rdoSelfSigned.Location = new Point(280, 311);
            rdoSelfSigned.Margin = new Padding(3, 2, 3, 2);
            rdoSelfSigned.Name = "rdoSelfSigned";
            rdoSelfSigned.Size = new Size(118, 24);
            rdoSelfSigned.TabIndex = 16;
            rdoSelfSigned.Text = "Auto-firmado";
            rdoSelfSigned.UseVisualStyleBackColor = true;
            // 
            // rdoCASigned
            // 
            rdoCASigned.AutoSize = true;
            rdoCASigned.Checked = true;
            rdoCASigned.Font = new Font("Segoe UI", 11F);
            rdoCASigned.ForeColor = Color.White;
            rdoCASigned.Location = new Point(149, 311);
            rdoCASigned.Margin = new Padding(3, 2, 3, 2);
            rdoCASigned.Name = "rdoCASigned";
            rdoCASigned.Size = new Size(123, 24);
            rdoCASigned.TabIndex = 15;
            rdoCASigned.TabStop = true;
            rdoCASigned.Text = "Certificado CA";
            rdoCASigned.UseVisualStyleBackColor = true;
            // 
            // chkSSL_TLS
            // 
            chkSSL_TLS.AutoSize = true;
            chkSSL_TLS.Font = new Font("Segoe UI", 11F);
            chkSSL_TLS.ForeColor = Color.White;
            chkSSL_TLS.Location = new Point(149, 289);
            chkSSL_TLS.Margin = new Padding(3, 2, 3, 2);
            chkSSL_TLS.Name = "chkSSL_TLS";
            chkSSL_TLS.Size = new Size(113, 24);
            chkSSL_TLS.TabIndex = 14;
            chkSSL_TLS.Text = "Usar SSL/TLS";
            chkSSL_TLS.UseVisualStyleBackColor = true;
            // 
            // txtPassword
            // 
            txtPassword.BackColor = Color.FromArgb(60, 63, 65);
            txtPassword.BorderStyle = BorderStyle.FixedSingle;
            txtPassword.Font = new Font("Segoe UI", 11F);
            txtPassword.ForeColor = Color.White;
            txtPassword.Location = new Point(131, 242);
            txtPassword.Margin = new Padding(3, 2, 3, 2);
            txtPassword.Name = "txtPassword";
            txtPassword.PasswordChar = '*';
            txtPassword.Size = new Size(306, 27);
            txtPassword.TabIndex = 13;
            // 
            // lblPassword
            // 
            lblPassword.AutoSize = true;
            lblPassword.Font = new Font("Segoe UI", 11F);
            lblPassword.ForeColor = Color.White;
            lblPassword.Location = new Point(35, 259);
            lblPassword.Name = "lblPassword";
            lblPassword.Size = new Size(86, 20);
            lblPassword.TabIndex = 12;
            lblPassword.Text = "Contraseña:";
            // 
            // txtUsername
            // 
            txtUsername.BackColor = Color.FromArgb(60, 63, 65);
            txtUsername.BorderStyle = BorderStyle.FixedSingle;
            txtUsername.Font = new Font("Segoe UI", 11F);
            txtUsername.ForeColor = Color.White;
            txtUsername.Location = new Point(131, 212);
            txtUsername.Margin = new Padding(3, 2, 3, 2);
            txtUsername.Name = "txtUsername";
            txtUsername.Size = new Size(306, 27);
            txtUsername.TabIndex = 11;
            // 
            // lblUsername
            // 
            lblUsername.AutoSize = true;
            lblUsername.Font = new Font("Segoe UI", 11F);
            lblUsername.ForeColor = Color.White;
            lblUsername.Location = new Point(35, 229);
            lblUsername.Name = "lblUsername";
            lblUsername.Size = new Size(62, 20);
            lblUsername.TabIndex = 10;
            lblUsername.Text = "Usuario:";
            // 
            // txtClientID
            // 
            txtClientID.BackColor = Color.FromArgb(60, 63, 65);
            txtClientID.BorderStyle = BorderStyle.FixedSingle;
            txtClientID.Font = new Font("Segoe UI", 11F);
            txtClientID.ForeColor = Color.White;
            txtClientID.Location = new Point(131, 182);
            txtClientID.Margin = new Padding(3, 2, 3, 2);
            txtClientID.Name = "txtClientID";
            txtClientID.Size = new Size(306, 27);
            txtClientID.TabIndex = 9;
            // 
            // lblClientID
            // 
            lblClientID.AutoSize = true;
            lblClientID.Font = new Font("Segoe UI", 11F);
            lblClientID.ForeColor = Color.White;
            lblClientID.Location = new Point(35, 199);
            lblClientID.Name = "lblClientID";
            lblClientID.Size = new Size(69, 20);
            lblClientID.TabIndex = 8;
            lblClientID.Text = "Client ID:";
            // 
            // txtPort
            // 
            txtPort.BackColor = Color.FromArgb(60, 63, 65);
            txtPort.BorderStyle = BorderStyle.FixedSingle;
            txtPort.Font = new Font("Segoe UI", 11F);
            txtPort.ForeColor = Color.White;
            txtPort.Location = new Point(131, 152);
            txtPort.Margin = new Padding(3, 2, 3, 2);
            txtPort.Name = "txtPort";
            txtPort.Size = new Size(105, 27);
            txtPort.TabIndex = 7;
            txtPort.Text = "1883";
            // 
            // lblPort
            // 
            lblPort.AutoSize = true;
            lblPort.Font = new Font("Segoe UI", 11F);
            lblPort.ForeColor = Color.White;
            lblPort.Location = new Point(35, 169);
            lblPort.Name = "lblPort";
            lblPort.Size = new Size(55, 20);
            lblPort.TabIndex = 6;
            lblPort.Text = "Puerto:";
            // 
            // txtHostAddress
            // 
            txtHostAddress.BackColor = Color.FromArgb(60, 63, 65);
            txtHostAddress.BorderStyle = BorderStyle.FixedSingle;
            txtHostAddress.Font = new Font("Segoe UI", 11F);
            txtHostAddress.ForeColor = Color.White;
            txtHostAddress.Location = new Point(228, 122);
            txtHostAddress.Margin = new Padding(3, 2, 3, 2);
            txtHostAddress.Name = "txtHostAddress";
            txtHostAddress.Size = new Size(219, 27);
            txtHostAddress.TabIndex = 5;
            // 
            // cmbHost
            // 
            cmbHost.BackColor = Color.FromArgb(60, 63, 65);
            cmbHost.FlatStyle = FlatStyle.Flat;
            cmbHost.Font = new Font("Segoe UI", 11F);
            cmbHost.ForeColor = Color.White;
            cmbHost.FormattingEnabled = true;
            cmbHost.Items.AddRange(new object[] { "mqtt://", "ws://" });
            cmbHost.Location = new Point(131, 122);
            cmbHost.Margin = new Padding(3, 2, 3, 2);
            cmbHost.Name = "cmbHost";
            cmbHost.Size = new Size(88, 28);
            cmbHost.TabIndex = 4;
            cmbHost.Text = "mqtt://";
            // 
            // lblHost
            // 
            lblHost.AutoSize = true;
            lblHost.Font = new Font("Segoe UI", 11F);
            lblHost.ForeColor = Color.White;
            lblHost.Location = new Point(35, 139);
            lblHost.Name = "lblHost";
            lblHost.Size = new Size(43, 20);
            lblHost.TabIndex = 3;
            lblHost.Text = "Host:";
            // 
            // txtName
            // 
            txtName.BackColor = Color.FromArgb(60, 63, 65);
            txtName.BorderStyle = BorderStyle.FixedSingle;
            txtName.Font = new Font("Segoe UI", 11F);
            txtName.ForeColor = Color.White;
            txtName.Location = new Point(131, 92);
            txtName.Margin = new Padding(3, 2, 3, 2);
            txtName.Name = "txtName";
            txtName.Size = new Size(306, 27);
            txtName.TabIndex = 2;
            // 
            // lblName
            // 
            lblName.AutoSize = true;
            lblName.Font = new Font("Segoe UI", 11F);
            lblName.ForeColor = Color.White;
            lblName.Location = new Point(35, 109);
            lblName.Name = "lblName";
            lblName.Size = new Size(67, 20);
            lblName.TabIndex = 1;
            lblName.Text = "Nombre:";
            // 
            // panelProfiles
            // 
            panelProfiles.BackColor = Color.FromArgb(35, 35, 38);
            panelProfiles.Location = new Point(18, 15);
            panelProfiles.Margin = new Padding(3, 2, 3, 2);
            panelProfiles.Name = "panelProfiles";
            panelProfiles.Padding = new Padding(13, 6, 13, 6);
            panelProfiles.Size = new Size(1645, 71);
            panelProfiles.TabIndex = 0;
            panelProfiles.WrapContents = false;
            // 
            // panelSinopticoContent
            // 
            panelSinopticoContent.BackColor = Color.FromArgb(45, 45, 48);
            panelSinopticoContent.Controls.Add(dgvSinopticoData);
            panelSinopticoContent.Controls.Add(txtSinopticoFilter);
            panelSinopticoContent.Location = new Point(0, 0);
            panelSinopticoContent.Margin = new Padding(3, 2, 3, 2);
            panelSinopticoContent.Name = "panelSinopticoContent";
            panelSinopticoContent.Padding = new Padding(18, 15, 18, 15);
            panelSinopticoContent.Size = new Size(1044, 728);
            panelSinopticoContent.TabIndex = 2;
            panelSinopticoContent.Visible = false;
            // 
            // panelConfigContent
            // 
            panelConfigContent.AutoScroll = true;
            panelConfigContent.BackColor = Color.FromArgb(45, 45, 48);
            panelConfigContent.Controls.Add(btnSend);
            panelConfigContent.Controls.Add(txtMessage);
            panelConfigContent.Controls.Add(lblMessage);
            panelConfigContent.Controls.Add(txtTopic);
            panelConfigContent.Controls.Add(lblTopic);
            panelConfigContent.Location = new Point(0, 0);
            panelConfigContent.Margin = new Padding(3, 2, 3, 2);
            panelConfigContent.Name = "panelConfigContent";
            panelConfigContent.Padding = new Padding(18, 15, 18, 15);
            panelConfigContent.Size = new Size(1044, 728);
            panelConfigContent.TabIndex = 1;
            panelConfigContent.Visible = false;
            // 
            // btnSend
            // 
            btnSend.BackColor = Color.FromArgb(0, 122, 204);
            btnSend.FlatAppearance.BorderSize = 0;
            btnSend.FlatStyle = FlatStyle.Flat;
            btnSend.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnSend.ForeColor = Color.White;
            btnSend.Location = new Point(131, 165);
            btnSend.Margin = new Padding(3, 2, 3, 2);
            btnSend.Name = "btnSend";
            btnSend.Size = new Size(105, 30);
            btnSend.TabIndex = 4;
            btnSend.Text = "Enviar";
            btnSend.UseVisualStyleBackColor = false;
            // 
            // txtMessage
            // 
            txtMessage.BackColor = Color.FromArgb(60, 63, 65);
            txtMessage.BorderStyle = BorderStyle.FixedSingle;
            txtMessage.Font = new Font("Segoe UI", 11F);
            txtMessage.ForeColor = Color.White;
            txtMessage.Location = new Point(131, 44);
            txtMessage.Margin = new Padding(3, 2, 3, 2);
            txtMessage.Multiline = true;
            txtMessage.Name = "txtMessage";
            txtMessage.Size = new Size(613, 113);
            txtMessage.TabIndex = 3;
            // 
            // lblMessage
            // 
            lblMessage.AutoSize = true;
            lblMessage.Font = new Font("Segoe UI", 11F);
            lblMessage.ForeColor = Color.White;
            lblMessage.Location = new Point(35, 60);
            lblMessage.Name = "lblMessage";
            lblMessage.Size = new Size(67, 20);
            lblMessage.TabIndex = 2;
            lblMessage.Text = "Mensaje:";
            // 
            // txtTopic
            // 
            txtTopic.BackColor = Color.FromArgb(60, 63, 65);
            txtTopic.BorderStyle = BorderStyle.FixedSingle;
            txtTopic.Font = new Font("Segoe UI", 11F);
            txtTopic.ForeColor = Color.White;
            txtTopic.Location = new Point(131, 14);
            txtTopic.Margin = new Padding(3, 2, 3, 2);
            txtTopic.Name = "txtTopic";
            txtTopic.Size = new Size(613, 27);
            txtTopic.TabIndex = 1;
            // 
            // lblTopic
            // 
            lblTopic.AutoSize = true;
            lblTopic.Font = new Font("Segoe UI", 11F);
            lblTopic.ForeColor = Color.White;
            lblTopic.Location = new Point(35, 30);
            lblTopic.Name = "lblTopic";
            lblTopic.Size = new Size(48, 20);
            lblTopic.TabIndex = 0;
            lblTopic.Text = "Topic:";
            // 
            // tabControl1
            // 
            tabControl1.Controls.Add(Conexión);
            tabControl1.Controls.Add(MQTT);
            tabControl1.Controls.Add(Sinóptico);
            tabControl1.Controls.Add(Correo);
            tabControl1.Controls.Add(Modbus);
            tabControl1.Location = new Point(1116, 221);
            tabControl1.Name = "tabControl1";
            tabControl1.SelectedIndex = 0;
            tabControl1.Size = new Size(505, 322);
            tabControl1.TabIndex = 3;
            // 
            // Conexión
            // 
            Conexión.BackColor = Color.FromArgb(45, 45, 48);
            Conexión.Location = new Point(4, 24);
            Conexión.Name = "Conexión";
            Conexión.Padding = new Padding(3);
            Conexión.Size = new Size(497, 294);
            Conexión.TabIndex = 0;
            Conexión.Text = "Conexión";
            // 
            // MQTT
            // 
            MQTT.BackColor = Color.FromArgb(45, 45, 48);
            MQTT.Location = new Point(4, 24);
            MQTT.Name = "MQTT";
            MQTT.Padding = new Padding(3);
            MQTT.Size = new Size(497, 294);
            MQTT.TabIndex = 1;
            MQTT.Text = "MQTT";
            // 
            // Sinóptico
            // 
            Sinóptico.BackColor = Color.FromArgb(45, 45, 48);
            Sinóptico.Location = new Point(4, 24);
            Sinóptico.Name = "Sinóptico";
            Sinóptico.Size = new Size(497, 294);
            Sinóptico.TabIndex = 2;
            Sinóptico.Text = "Sinóptico";
            // 
            // Correo
            // 
            Correo.BackColor = Color.FromArgb(45, 45, 48);
            Correo.Location = new Point(4, 24);
            Correo.Name = "Correo";
            Correo.Size = new Size(497, 294);
            Correo.TabIndex = 3;
            Correo.Text = "Correo";
            // 
            // Modbus
            // 
            Modbus.BackColor = Color.FromArgb(45, 45, 48);
            Modbus.Location = new Point(4, 24);
            Modbus.Name = "Modbus";
            Modbus.Size = new Size(497, 294);
            Modbus.TabIndex = 4;
            Modbus.Text = "Modbus";
            // 
            // Form1
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1680, 810);
            ControlBox = false;
            Controls.Add(tabControl1);
            Controls.Add(panelContent);
            Controls.Add(panelNavigation);
            Controls.Add(panelTopBar);
            FormBorderStyle = FormBorderStyle.None;
            Margin = new Padding(3, 2, 3, 2);
            Name = "Form1";
            Text = "Form1";
            ((System.ComponentModel.ISupportInitialize)nudCheckInterval).EndInit();
            ((System.ComponentModel.ISupportInitialize)dgvSinopticoData).EndInit();
            ((System.ComponentModel.ISupportInitialize)nudModbusPortLeft).EndInit();
            ((System.ComponentModel.ISupportInitialize)nudModbusDeviceIdLeft).EndInit();
            ((System.ComponentModel.ISupportInitialize)nudModbusRegisterCountLeft).EndInit();
            ((System.ComponentModel.ISupportInitialize)dgvModbusReadResultsLeft).EndInit();
            ((System.ComponentModel.ISupportInitialize)splitContainerModbus).EndInit();
            splitContainerModbus.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)nudModbusPortRight).EndInit();
            ((System.ComponentModel.ISupportInitialize)nudModbusDeviceIdRight).EndInit();
            ((System.ComponentModel.ISupportInitialize)nudModbusRegisterCountRight).EndInit();
            ((System.ComponentModel.ISupportInitialize)dgvModbusReadResultsRight).EndInit();
            panelTopBar.ResumeLayout(false);
            panelTopBar.PerformLayout();
            panelNavigation.ResumeLayout(false);
            panelContent.ResumeLayout(false);
            panelConnectionContent.ResumeLayout(false);
            panelConnectionContent.PerformLayout();
            panelSinopticoContent.ResumeLayout(false);
            panelSinopticoContent.PerformLayout();
            panelConfigContent.ResumeLayout(false);
            panelConfigContent.PerformLayout();
            tabControl1.ResumeLayout(false);
            ResumeLayout(false);

        }

        #endregion

        private TabControl tabControl1;
        private TabPage Conexión;
        private TabPage MQTT;
        private TabPage Sinóptico;
        private TabPage Correo;
        private TabPage Modbus;
    }
}