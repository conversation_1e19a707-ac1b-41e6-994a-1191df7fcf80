using Microsoft.Exchange.WebServices.Data;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using prueba;

namespace prueba.Services
{
    /// <summary>
    /// Service for processing emails using Exchange Web Services (EWS)
    /// </summary>
    public class EwsEmailService
    {
        private readonly string serverUrl;
        private readonly string username;
        private readonly string password;
        private readonly string domain;
        private readonly bool useOAuth;
        private readonly string rutaCarpeta;
        private readonly string archivoCorreosProcesados;
        private readonly Form1 form;
        private ExchangeService exchangeService;

        public EwsEmailService(string serverUrl, string username, string password, string domain = "", bool useOAuth = false, string rutaCarpeta = "", Form1 form = null)
        {
            this.serverUrl = serverUrl;
            this.username = username;
            this.password = password;
            this.domain = domain;
            this.useOAuth = useOAuth;
            this.form = form;
            this.rutaCarpeta = string.IsNullOrEmpty(rutaCarpeta) ? Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "CorreosDescargados") : rutaCarpeta;
            this.archivoCorreosProcesados = Path.Combine(this.rutaCarpeta, "correos_procesados.txt");

            // Ensure directory exists
            if (!Directory.Exists(this.rutaCarpeta))
            {
                Directory.CreateDirectory(this.rutaCarpeta);
            }

            // Ensure processed emails file exists
            if (!File.Exists(archivoCorreosProcesados))
            {
                File.Create(archivoCorreosProcesados).Close();
            }
        }

        /// <summary>
        /// Tests the EWS connection with timeout
        /// </summary>
        /// <returns>True if connection is successful, false otherwise</returns>
        public async System.Threading.Tasks.Task<bool> TestConnectionAsync()
        {
            try
            {
                // Set a shorter timeout for connection testing to prevent hanging (20 seconds)
                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(20)))
                {
                    // Initialize service with timeout
                    await InitializeExchangeServiceAsync(cts.Token).ConfigureAwait(false);

                    // Try to access the inbox to verify connection with timeout
                    var inbox = await System.Threading.Tasks.Task.Run(() =>
                    {
                        cts.Token.ThrowIfCancellationRequested();

                        // Set timeout for the EWS service operations
                        if (exchangeService != null)
                        {
                            exchangeService.Timeout = 8000; // 8 seconds timeout for individual operations
                        }

                        return Folder.Bind(exchangeService, WellKnownFolderName.Inbox);
                    }, cts.Token).ConfigureAwait(false);

                    Logger.Log($"Prueba de conexión EWS exitosa. Nombre de bandeja de entrada: {inbox.DisplayName}", LogLevel.Info);
                    return true;
                }
            }
            catch (OperationCanceledException)
            {
                Logger.Log("Prueba de conexión EWS expiró después de 20 segundos", LogLevel.Error);
                return false;
            }
            catch (Exception ex)
            {
                Logger.Log($"Prueba de conexión EWS falló: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// Processes emails from Exchange server using EWS
        /// </summary>
        /// <returns>List of processed email states</returns>
        public async Task<List<string>> ProcessEmailsAsync()
        {
            var states = new List<string>();

            try
            {
                // Use timeout for the entire operation
                using (var cts = new CancellationTokenSource(TimeSpan.FromMinutes(5)))
                {
                    await InitializeExchangeServiceAsync(cts.Token);

                    // Load processed emails list
                    var processedEmails = new List<string>();
                    if (File.Exists(archivoCorreosProcesados))
                    {
                        processedEmails = File.ReadAllLines(archivoCorreosProcesados).ToList();
                    }

                    // Get inbox folder with timeout
                    var inbox = await System.Threading.Tasks.Task.Run(() =>
                    {
                        cts.Token.ThrowIfCancellationRequested();
                        return Folder.Bind(exchangeService, WellKnownFolderName.Inbox);
                    }, cts.Token).ConfigureAwait(false);

                    // Create search filter for unread emails
                    var searchFilter = new SearchFilter.SearchFilterCollection(LogicalOperator.And,
                        new SearchFilter.IsEqualTo(EmailMessageSchema.IsRead, false));

                    // Define item view (get latest 50 emails)
                    var itemView = new ItemView(50)
                    {
                        OrderBy = { { ItemSchema.DateTimeReceived, SortDirection.Descending } }
                    };

                    // Find items with timeout
                    var findResults = await System.Threading.Tasks.Task.Run(() =>
                    {
                        cts.Token.ThrowIfCancellationRequested();
                        return inbox.FindItems(searchFilter, itemView);
                    }, cts.Token).ConfigureAwait(false);

                    Logger.Log($"EWS: {findResults.Items.Count} correos no leídos encontrados", LogLevel.Info);

                    foreach (var item in findResults.Items)
                    {
                        cts.Token.ThrowIfCancellationRequested();

                        if (item is EmailMessage email)
                        {
                            try
                            {
                                // Load email properties with timeout
                                await System.Threading.Tasks.Task.Run(() =>
                                {
                                    cts.Token.ThrowIfCancellationRequested();
                                    email.Load(new PropertySet(BasePropertySet.FirstClassProperties, EmailMessageSchema.Body));
                                }, cts.Token).ConfigureAwait(false);

                                string emailId = email.Id.UniqueId;

                                // Skip if already processed
                                if (processedEmails.Contains(emailId))
                                {
                                    continue;
                                }

                                Logger.Log($"EWS: Procesando '{email.Subject}' de {email.From?.Address}", LogLevel.Info);

                                // Process email content (similar to existing email processing logic)
                                string emailContent = GetEmailTextContent(email);
                                var emailStates = ProcessEmailContent(emailContent, email.Subject);
                                states.AddRange(emailStates);

                                // Save email to file
                                await SaveEmailToFileAsync(email, emailId);

                                // Mark as processed
                                processedEmails.Add(emailId);

                                // Mark as read on server with timeout
                                await System.Threading.Tasks.Task.Run(() =>
                                {
                                    cts.Token.ThrowIfCancellationRequested();
                                    email.IsRead = true;
                                    email.Update(ConflictResolutionMode.AutoResolve);
                                }, cts.Token).ConfigureAwait(false);

                                Logger.Log($"EWS: Correo procesado exitosamente - {email.Subject}", LogLevel.Success);
                            }
                            catch (OperationCanceledException)
                            {
                                Logger.Log("Procesamiento de correo EWS cancelado por tiempo de espera", LogLevel.Warning);
                                throw;
                            }
                            catch (Exception ex)
                            {
                                Logger.Log($"Error procesando correo EWS individual: {ex.Message}", LogLevel.Error);
                            }
                        }
                    }

                    // Save updated processed emails list
                    File.WriteAllLines(archivoCorreosProcesados, processedEmails);

                    Logger.Log($"Procesamiento de correos EWS completado. Procesados {states.Count} estados.", LogLevel.Info);
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error en procesamiento de correos EWS: {ex.Message}", LogLevel.Error);
                throw;
            }

            return states;
        }

        /// <summary>
        /// Resets the EWS service to force reinitialization
        /// </summary>
        public void ResetService()
        {
            try
            {
                // ExchangeService doesn't implement IDisposable, so we just null it
                exchangeService = null;
                Logger.Log("Servicio EWS reiniciado exitosamente", LogLevel.Info);
            }
            catch (Exception ex)
            {
                Logger.Log($"Error reiniciando servicio EWS: {ex.Message}", LogLevel.Warning);
                exchangeService = null;
            }
        }

        /// <summary>
        /// Initializes the Exchange service with authentication
        /// </summary>
        private async System.Threading.Tasks.Task InitializeExchangeServiceAsync(CancellationToken cancellationToken = default)
        {
            if (exchangeService != null)
                return;

            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                exchangeService = new ExchangeService(ExchangeVersion.Exchange2013_SP1);

                // Set timeout for all EWS operations (shorter timeout to prevent hanging)
                exchangeService.Timeout = 10000; // 10 seconds - reduced from 15 to prevent hanging

                if (useOAuth)
                {
                    // OAuth authentication (for future implementation)
                    throw new NotImplementedException("OAuth authentication is not yet implemented. Please use basic authentication.");
                }
                else
                {
                    // Basic authentication
                    if (!string.IsNullOrEmpty(domain))
                    {
                        exchangeService.Credentials = new NetworkCredential(username, password, domain);
                    }
                    else
                    {
                        exchangeService.Credentials = new NetworkCredential(username, password);
                    }
                }

                cancellationToken.ThrowIfCancellationRequested();

                // Set the URL if provided, otherwise use autodiscover
                if (!string.IsNullOrEmpty(serverUrl))
                {
                    exchangeService.Url = new Uri(serverUrl);
                    Logger.Log($"EWS: Servicio inicializado con URL {serverUrl}", LogLevel.Success);
                }
                else
                {
                    // Use autodiscover with timeout
                    await System.Threading.Tasks.Task.Run(() =>
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        exchangeService.AutodiscoverUrl(username, RedirectionUrlValidationCallback);
                    }, cancellationToken).ConfigureAwait(false);

                    Logger.Log($"EWS: Servicio inicializado con autodiscover para {username}", LogLevel.Success);
                }
            }
            catch (OperationCanceledException)
            {
                Logger.Log("EWS: Inicialización cancelada por tiempo de espera", LogLevel.Error);
                throw;
            }
            catch (Exception ex)
            {
                Logger.Log($"Error inicializando servicio EWS: {ex.Message}", LogLevel.Error);
                throw;
            }
        }

        /// <summary>
        /// Validation callback for autodiscover redirection
        /// </summary>
        private static bool RedirectionUrlValidationCallback(string redirectionUrl)
        {
            // Validate that the redirection URL is HTTPS
            var uri = new Uri(redirectionUrl);
            return uri.Scheme == "https";
        }

        /// <summary>
        /// Extracts text content from email message
        /// </summary>
        private string GetEmailTextContent(EmailMessage email)
        {
            try
            {
                if (email.Body.BodyType == BodyType.Text)
                {
                    return email.Body.Text;
                }
                else if (email.Body.BodyType == BodyType.HTML)
                {
                    // Simple HTML to text conversion (remove tags)
                    return System.Text.RegularExpressions.Regex.Replace(email.Body.Text, "<.*?>", string.Empty);
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error extrayendo contenido del correo: {ex.Message}", LogLevel.Warning);
            }

            return string.Empty;
        }

        /// <summary>
        /// Processes email content to extract states (reuses existing logic)
        /// </summary>
        private List<string> ProcessEmailContent(string content, string subject)
        {
            var states = new List<string>();

            try
            {
                // This should match the existing email processing logic from GestorDeCorreo
                // For now, implementing a basic version
                var lines = content.Split('\n', StringSplitOptions.RemoveEmptyEntries);

                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();
                    if (!string.IsNullOrEmpty(trimmedLine) && trimmedLine.Contains("="))
                    {
                        states.Add(trimmedLine);
                    }
                }


            }
            catch (Exception ex)
            {
                Logger.Log($"Error procesando contenido del correo: {ex.Message}", LogLevel.Warning);
            }

            return states;
        }

        /// <summary>
        /// Saves email to file
        /// </summary>
        private async System.Threading.Tasks.Task SaveEmailToFileAsync(EmailMessage email, string emailId)
        {
            try
            {
                string fileName = $"email_{DateTime.Now:yyyyMMdd_HHmmss}_{emailId.Substring(0, 8)}.txt";
                string filePath = Path.Combine(rutaCarpeta, fileName);

                var content = $"From: {email.From?.Address}\n" +
                             $"Subject: {email.Subject}\n" +
                             $"Date: {email.DateTimeReceived}\n" +
                             $"Body:\n{GetEmailTextContent(email)}";

                await File.WriteAllTextAsync(filePath, content);
            }
            catch (Exception ex)
            {
                Logger.Log($"EWS: Error guardando correo en archivo - {ex.Message}", LogLevel.Warning);
            }
        }

        /// <summary>
        /// Gets raw email data for processing by the unified email pipeline (synchronous version to avoid deadlocks)
        /// </summary>
        public List<RawEmailData> GetRawEmails()
        {
            var rawEmails = new List<RawEmailData>();

            try
            {
                if (exchangeService == null)
                {
                    // Initialize synchronously to avoid deadlock issues
                    InitializeExchangeService();
                }

                Logger.Log("Obteniendo correos sin procesar de EWS para procesamiento unificado", LogLevel.Info);

                // Set timeout for EWS operations
                if (exchangeService != null)
                {
                    exchangeService.Timeout = 15000; // 15 seconds timeout
                }

                // Find emails in inbox (direct synchronous call) - Get ALL emails, not just unread
                var findResults = exchangeService.FindItems(WellKnownFolderName.Inbox, new ItemView(50));

                Logger.Log($"EWS: {findResults.Items.Count} correos totales en bandeja", LogLevel.Info);

                foreach (var item in findResults.Items.OfType<EmailMessage>())
                {
                    try
                    {
                        // Load email properties (direct synchronous call)
                        item.Load(new PropertySet(BasePropertySet.FirstClassProperties, ItemSchema.Body));

                        // Create raw email data object
                        var rawEmail = new RawEmailData
                        {
                            MessageId = item.Id?.UniqueId ?? $"EWS_{DateTime.Now.Ticks}_{rawEmails.Count}",
                            Subject = item.Subject ?? "",
                            HtmlContent = GetEmailHtmlContent(item),
                            Sender = item.From?.Address ?? "",
                            IsRead = item.IsRead
                        };

                        rawEmails.Add(rawEmail);
                        // Removed verbose email retrieval logging for production
                    }
                    catch (Exception ex)
                    {
                        Logger.Log($"Error cargando elemento de correo: {ex.Message}", LogLevel.Warning);
                    }
                }

                Logger.Log($"Obtenidos {rawEmails.Count} correos sin procesar de EWS", LogLevel.Info);
            }
            catch (Exception ex)
            {
                Logger.Log($"Error obteniendo correos sin procesar de EWS: {ex.Message}", LogLevel.Error);
                throw;
            }

            return rawEmails;
        }

        /// <summary>
        /// Synchronous version of InitializeExchangeService to avoid deadlocks
        /// </summary>
        private void InitializeExchangeService()
        {
            try
            {
                exchangeService = new ExchangeService(ExchangeVersion.Exchange2013_SP1);

                // Set timeout for all EWS operations
                exchangeService.Timeout = 15000; // 15 seconds

                if (useOAuth)
                {
                    // OAuth authentication (for future implementation)
                    throw new NotImplementedException("OAuth authentication is not yet implemented. Please use basic authentication.");
                }
                else
                {
                    // Basic authentication
                    if (!string.IsNullOrEmpty(domain))
                    {
                        exchangeService.Credentials = new NetworkCredential(username, password, domain);
                    }
                    else
                    {
                        exchangeService.Credentials = new NetworkCredential(username, password);
                    }
                }

                // Set the URL if provided, otherwise use autodiscover
                if (!string.IsNullOrEmpty(serverUrl))
                {
                    exchangeService.Url = new Uri(serverUrl);
                    Logger.Log($"Servicio EWS inicializado con URL: {serverUrl}", LogLevel.Info);
                }
                else
                {
                    // Use autodiscover with timeout to prevent hanging
                    using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)))
                    {
                        var autodiscoverTask = System.Threading.Tasks.Task.Run(() =>
                            exchangeService.AutodiscoverUrl(username, RedirectionUrlValidationCallback), cts.Token);

                        try
                        {
                            autodiscoverTask.Wait(cts.Token);
                            Logger.Log($"Servicio EWS inicializado con autodiscover para: {username}", LogLevel.Info);
                        }
                        catch (OperationCanceledException)
                        {
                            Logger.Log("Autodiscover EWS expiró después de 30 segundos", LogLevel.Error);
                            throw new TimeoutException("EWS autodiscover operation timed out");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error inicializando servicio EWS: {ex.Message}", LogLevel.Error);
                throw;
            }
        }

        /// <summary>
        /// Extracts HTML content from email message (prefers HTML over text)
        /// </summary>
        private string GetEmailHtmlContent(EmailMessage email)
        {
            try
            {
                Logger.Log($"GetEmailHtmlContent: Processing email '{email.Subject}', BodyType: {email.Body.BodyType}", LogLevel.Debug);

                if (email.Body.BodyType == BodyType.HTML)
                {
                    // Return HTML content directly (like POP3 HtmlBody)
                    string htmlContent = email.Body.Text ?? "";
                    Logger.Log($"GetEmailHtmlContent: Returning HTML content (length: {htmlContent.Length})", LogLevel.Debug);
                    return htmlContent;
                }
                else if (email.Body.BodyType == BodyType.Text)
                {
                    // Convert plain text to basic HTML (like POP3 fallback)
                    string textContent = email.Body.Text ?? "";
                    string convertedHtml = $"<html><body><pre>{System.Net.WebUtility.HtmlEncode(textContent)}</pre></body></html>";

                    return convertedHtml;
                }
                else
                {
                    Logger.Log($"GetEmailHtmlContent: Tipo de cuerpo desconocido: {email.Body.BodyType}", LogLevel.Warning);
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error extrayendo contenido HTML del correo: {ex.Message}", LogLevel.Warning);
            }

            Logger.Log("GetEmailHtmlContent: Devolviendo cadena vacía", LogLevel.Warning);
            return "";
        }

        /// <summary>
        /// Marks an email as read on the server (synchronous version to avoid deadlocks)
        /// </summary>
        public void MarkEmailAsRead(string messageId)
        {
            try
            {
                if (exchangeService == null)
                {
                    Logger.Log("No se puede marcar correo como leído - servicio EWS no inicializado", LogLevel.Warning);
                    return;
                }

                // Set timeout for EWS operations
                exchangeService.Timeout = 10000; // 10 seconds timeout

                // Find the email by ID and mark as read (direct synchronous call)
                var findResults = exchangeService.FindItems(WellKnownFolderName.Inbox, new ItemView(100));
                var emailToMark = findResults.Items.OfType<EmailMessage>()
                    .FirstOrDefault(e => e.Id?.UniqueId == messageId);

                if (emailToMark != null)
                {
                    emailToMark.IsRead = true;
                    emailToMark.Update(ConflictResolutionMode.AutoResolve);
                    Logger.Log($"Correo marcado como leído: {messageId}", LogLevel.Debug);
                }
                else
                {
                    Logger.Log($"Correo no encontrado para marcar como leído: {messageId}", LogLevel.Debug);
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error marcando correo como leído: {ex.Message}", LogLevel.Warning);
            }
        }

        /// <summary>
        /// Sends an email with analyzed data using EWS
        /// </summary>
        /// <param name="analyzedData">The analyzed hourly restrictions data</param>
        /// <param name="originalSubject">Subject of the original email that was analyzed</param>
        /// <param name="originalSender">Sender of the original email</param>
        /// <returns>True if email was sent successfully, false otherwise</returns>
        public async Task<bool> SendAnalyzedDataEmailAsync(List<string> analyzedData, string originalSubject, string originalSender)
        {
            try
            {
                // Get recipient email addresses from the current profile configuration
                var recipients = GetConfiguredEmailNotificationAddresses();

                // Ensure EWS service is initialized
                if (exchangeService == null)
                {
                    await InitializeExchangeServiceAsync();
                }

                // Create the email message
                var email = new EmailMessage(exchangeService);

                // Set recipients
                foreach (var recipient in recipients)
                {
                    email.ToRecipients.Add(recipient);
                }

                // Set subject with "Feedback" prefix to prevent infinite loops
                email.Subject = $"Feedback - Análisis de Restricciones Horarias - {DateTime.Now:yyyy-MM-dd HH:mm}";

                // Create email body with analyzed data
                var bodyContent = CreateAnalyzedDataEmailBody(analyzedData, originalSubject, originalSender);
                email.Body = new MessageBody(BodyType.HTML, bodyContent);

                // Set importance
                email.Importance = Importance.Normal;

                // Send the email with timeout
                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)))
                {
                    await System.Threading.Tasks.Task.Run(() =>
                    {
                        cts.Token.ThrowIfCancellationRequested();
                        email.Send();
                    }, cts.Token);
                }

                Logger.Log($"Correo con datos analizados enviado exitosamente a {recipients.Count} destinatarios", LogLevel.Info);
                return true;
            }
            catch (OperationCanceledException)
            {
                Logger.Log("Envío de correo con datos analizados expiró después de 30 segundos", LogLevel.Error);
                return false;
            }
            catch (Exception ex)
            {
                Logger.Log($"Error enviando correo con datos analizados: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// Gets the configured email notification addresses from the current profile
        /// </summary>
        /// <returns>List of email addresses to send notifications to</returns>
        private List<string> GetConfiguredEmailNotificationAddresses()
        {
            var recipients = new List<string>();

            // Get the current profile from the form
            if (form?.currentActiveProfile != null)
            {
                // Add non-empty email notification addresses from the profile
                if (!string.IsNullOrWhiteSpace(form.currentActiveProfile.EmailNotification1))
                    recipients.Add(form.currentActiveProfile.EmailNotification1.Trim());
                if (!string.IsNullOrWhiteSpace(form.currentActiveProfile.EmailNotification2))
                    recipients.Add(form.currentActiveProfile.EmailNotification2.Trim());
                if (!string.IsNullOrWhiteSpace(form.currentActiveProfile.EmailNotification3))
                    recipients.Add(form.currentActiveProfile.EmailNotification3.Trim());
                if (!string.IsNullOrWhiteSpace(form.currentActiveProfile.EmailNotification4))
                    recipients.Add(form.currentActiveProfile.EmailNotification4.Trim());
                if (!string.IsNullOrWhiteSpace(form.currentActiveProfile.EmailNotification5))
                    recipients.Add(form.currentActiveProfile.EmailNotification5.Trim());
            }

            // If no email addresses are configured, fall back to default
            if (recipients.Count == 0)
            {
                recipients.Add("<EMAIL>");
                Logger.Log("No email notification addresses configured, using default recipient", LogLevel.Warning);
            }

            return recipients;
        }

        /// <summary>
        /// Creates the HTML body content for the analyzed data email
        /// </summary>
        /// <param name="analyzedData">The analyzed hourly restrictions data</param>
        /// <param name="originalSubject">Subject of the original email</param>
        /// <param name="originalSender">Sender of the original email</param>
        /// <returns>HTML formatted email body</returns>
        private string CreateAnalyzedDataEmailBody(List<string> analyzedData, string originalSubject, string originalSender)
        {
            var html = new System.Text.StringBuilder();

            html.AppendLine("<html>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='UTF-8'>");
            html.AppendLine("<title>Análisis de Restricciones Horarias</title>");
            html.AppendLine("<style>");
            html.AppendLine("body { font-family: Arial, sans-serif; margin: 20px; }");
            html.AppendLine("table { border-collapse: collapse; width: 100%; margin: 20px 0; }");
            html.AppendLine("th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }");
            html.AppendLine("th { background-color: #f2f2f2; }");
            html.AppendLine(".restricted { background-color: #ffcccc; color: #cc0000; font-weight: bold; }");
            html.AppendLine(".available { background-color: #ccffcc; color: #006600; }");
            html.AppendLine(".info-section { background-color: #f9f9f9; padding: 15px; margin: 10px 0; border-left: 4px solid #007acc; }");
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");

            // Header
            html.AppendLine("<h1>📊 Análisis de Restricciones Horarias</h1>");
            html.AppendLine($"<p><strong>Fecha y Hora del Análisis:</strong> {DateTime.Now:dddd, dd MMMM yyyy HH:mm:ss}</p>");

            // Original email information
            html.AppendLine("<div class='info-section'>");
            html.AppendLine("<h2>📧 Información del Correo Analizado</h2>");
            html.AppendLine($"<p><strong>Asunto:</strong> {System.Net.WebUtility.HtmlEncode(originalSubject ?? "N/A")}</p>");
            html.AppendLine($"<p><strong>Remitente:</strong> {System.Net.WebUtility.HtmlEncode(originalSender ?? "N/A")}</p>");
            html.AppendLine("</div>");

            // Analyzed data summary
            html.AppendLine("<div class='info-section'>");
            html.AppendLine("<h2>📈 Resumen del Análisis</h2>");

            if (analyzedData != null && analyzedData.Count > 0)
            {
                var restrictedHours = analyzedData.Where(h => h.StartsWith("H", StringComparison.OrdinalIgnoreCase)).ToList();
                html.AppendLine($"<p><strong>Total de Horas con Restricciones:</strong> {restrictedHours.Count}</p>");
                html.AppendLine($"<p><strong>Horas Disponibles:</strong> {24 - restrictedHours.Count}</p>");

                if (restrictedHours.Count > 0)
                {
                    html.AppendLine("<p><strong>Horas Restringidas:</strong> ");
                    html.AppendLine(string.Join(", ", restrictedHours.Select(h => h.Substring(1))));
                    html.AppendLine("</p>");
                }
            }
            else
            {
                html.AppendLine("<p><strong>Estado:</strong> No se encontraron restricciones horarias en el correo analizado.</p>");
            }
            html.AppendLine("</div>");

            // Hourly restrictions table
            html.AppendLine("<h2>🕐 Tabla de Restricciones por Hora</h2>");
            html.AppendLine("<table>");
            html.AppendLine("<tr>");
            html.AppendLine("<th>Hora</th>");
            html.AppendLine("<th>Estado</th>");
            html.AppendLine("<th>Descripción</th>");
            html.AppendLine("</tr>");

            // Create 24-hour table
            for (int hour = 1; hour <= 24; hour++)
            {
                string hourCode = $"H{hour}";
                bool isRestricted = analyzedData?.Contains(hourCode, StringComparer.OrdinalIgnoreCase) == true;
                string cssClass = isRestricted ? "restricted" : "available";
                string status = isRestricted ? "RESTRINGIDA" : "Disponible";
                string description = isRestricted ? "Hora con restricciones activas" : "Hora sin restricciones";

                html.AppendLine("<tr>");
                html.AppendLine($"<td>{hour:00}:00</td>");
                html.AppendLine($"<td class='{cssClass}'>{status}</td>");
                html.AppendLine($"<td>{description}</td>");
                html.AppendLine("</tr>");
            }

            html.AppendLine("</table>");

            // Footer
            html.AppendLine("<div class='info-section'>");
            html.AppendLine("<h3>ℹ️ Información Adicional</h3>");
            html.AppendLine("<p>Este correo ha sido generado automáticamente por el sistema de análisis de restricciones horarias.</p>");
            html.AppendLine("<p>Los datos mostrados corresponden a las restricciones extraídas del correo recibido.</p>");
            html.AppendLine($"<p><strong>Generado por:</strong> Sistema de Análisis de Correos v1.0</p>");
            html.AppendLine("</div>");

            html.AppendLine("</body>");
            html.AppendLine("</html>");

            return html.ToString();
        }

        /// <summary>
        /// Disposes the Exchange service
        /// </summary>
        public void Dispose()
        {
            exchangeService = null;
        }
    }

    /// <summary>
    /// Raw email data structure for unified processing
    /// </summary>
    public class RawEmailData
    {
        public string MessageId { get; set; } = "";
        public string Subject { get; set; } = "";
        public string HtmlContent { get; set; } = "";
        public string Sender { get; set; } = "";
        public bool IsRead { get; set; }
    }
}
