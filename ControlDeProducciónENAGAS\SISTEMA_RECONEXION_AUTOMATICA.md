# 🔄 Sistema de Reconexión Automática - Control de Producción ENAGAS

## 📋 Descripción General

Se ha implementado un sistema completo de reconexión automática para MQTT y Modbus que garantiza la continuidad del servicio ante desconexiones inesperadas o reinicios de la aplicación.

## ⚙️ Características Principales

### 🔧 Configuración Automática
- **Carga automática**: Las configuraciones se cargan automáticamente al iniciar la aplicación
- **Persistencia**: Las configuraciones válidas se guardan en archivos JSON para supervivencia entre reinicios
- **Validación**: Solo se guardan configuraciones que han sido probadas y funcionan correctamente

### 🔄 Reconexión Inteligente
- **Detección automática**: El sistema detecta desconexiones cada segundo a través del timer1
- **Intervalos configurables**: 
  - MQTT: Intenta reconectar cada 30 segundos
  - Modbus: Intenta reconectar cada 10 segundos (configurable)
- **Contadores**: Lleva registro del número de intentos de reconexión
- **Logs detallados**: Registra todos los intentos y resultados en el log de la aplicación

### 🎛️ Control Manual
- **Desconexión manual**: Al desconectar manualmente, se deshabilita temporalmente la reconexión automática
- **Conexión manual**: Al conectar manualmente, se rehabilita la reconexión automática
- **Reset de contadores**: Los contadores se resetean al conectar exitosamente

## 📁 Archivos de Configuración

### 🔗 MQTT - `config_mqtt.json`
```json
{
  "mqtt": {
    "name": "ALMENDRALEJO",
    "protocolo": "mqtts://",
    "host": "mqtt.greeneaglesolutions.com",
    "puerto": 8883,
    "clientId": "ENAGAS_ALMENDRALEJO_XXXXXXXX",
    "usuario": "enagashuelva",
    "password": "ZQwz6AKVZU8O1iFLH-CC",
    "usarSslTls": true,
    "certificadoCA": true
  }
}
```

### 🔌 Modbus - `config_modbus.json`
```json
{
  "modbus": {
    "ip": "*************",
    "puerto": 502,
    "deviceId": 1,
    "timeout": 5000,
    "reconexionAutomatica": true,
    "intervaloReconexion": 10
  }
}
```

## 🔄 Flujo de Funcionamiento

### 1. **Inicio de Aplicación**
```
1. Carga configuración MQTT desde archivo
2. Carga configuración Modbus desde archivo
3. Inicializa sistema de reconexión
4. Configura timer1 (1000ms) para verificaciones periódicas
```

### 2. **Conexión Exitosa**
```
1. Usuario conecta manualmente
2. Se guarda configuración válida
3. Se habilita reconexión automática
4. Se resetean contadores
5. Se registra en log
```

### 3. **Detección de Desconexión**
```
1. Timer1 verifica estado cada segundo
2. Detecta desconexión (mqtt == null o !Connected)
3. Verifica si ha pasado el intervalo mínimo
4. Inicia proceso de reconexión asíncrona
5. Incrementa contador de intentos
```

### 4. **Proceso de Reconexión**
```
1. Crea nueva instancia de conexión
2. Usa configuración guardada válida
3. Intenta conectar con timeout
4. Si exitoso: actualiza UI y rehabilita servicios
5. Si falla: registra error y espera próximo intento
```

### 5. **Desconexión Manual**
```
1. Usuario presiona desconectar
2. Se deshabilita reconexión automática temporalmente
3. Se actualiza UI
4. Se registra en log
```

## 🎯 Componentes Implementados

### 📝 Clases Nuevas
- **`ConfiguracionModbus.cs`**: Manejo de configuración Modbus con persistencia JSON

### 🔧 Variables de Control
```csharp
// Configuraciones válidas guardadas
private ConfiguracionMQTT? ultimaConfigMQTT;
private ConfiguracionModbus? ultimaConfigModbus;

// Control de intervalos
private DateTime ultimoIntentoReconexionMQTT;
private DateTime ultimoIntentoReconexionModbus;

// Estados de reconexión
private bool reconexionMQTTHabilitada = true;
private bool reconexionModbusHabilitada = true;

// Contadores de intentos
private int contadorReconexionesMQTT = 0;
private int contadorReconexionesModbus = 0;
```

### 🔄 Funciones Principales
- **`VerificarYReconectarServicios()`**: Función principal llamada por timer1
- **`VerificarReconexionMQTT()`**: Verifica y programa reconexión MQTT
- **`VerificarReconexionModbus()`**: Verifica y programa reconexión Modbus
- **`IntentarReconexionMQTT()`**: Ejecuta reconexión MQTT asíncrona
- **`IntentarReconexionModbus()`**: Ejecuta reconexión Modbus asíncrona
- **`GuardarConfiguracionMQTTValida()`**: Guarda configuración MQTT válida
- **`GuardarConfiguracionModbusValida()`**: Guarda configuración Modbus válida

## 🎮 Uso del Sistema

### ✅ Funcionamiento Automático
1. **Al iniciar la aplicación**: Se cargan automáticamente las últimas configuraciones válidas
2. **Durante operación**: El sistema monitorea conexiones cada segundo
3. **Ante desconexión**: Intenta reconectar automáticamente usando la última configuración válida
4. **Al reconectar**: Restaura todos los servicios (suscripciones MQTT, habilitación de controles)

### 🎛️ Control Manual
- **Conectar**: Habilita reconexión automática y guarda configuración válida
- **Desconectar**: Deshabilita temporalmente la reconexión automática
- **Logs**: Todos los eventos se registran en el panel de log con timestamps

## 🔍 Monitoreo y Logs

El sistema proporciona logs detallados de todas las operaciones:

```
12:34:56 🔄 RECONEXIÓN: Sistema de reconexión automática inicializado
12:34:56 ⏱️ RECONEXIÓN: Timer configurado cada 1000ms
12:35:10 ✅ MQTT: Conectado a mqtt.greeneaglesolutions.com:8883
12:35:10 💾 MQTT: Configuración válida guardada para reconexión automática
12:35:10 🔄 Reconexión automática MQTT habilitada
12:37:45 🔄 MQTT: Intento de reconexión #1
12:37:47 ✅ MQTT: Reconectado exitosamente (intento #1)
```

## 🛡️ Características de Seguridad

- **Intervalos mínimos**: Evita spam de reconexiones
- **Timeouts configurables**: Evita bloqueos indefinidos
- **Manejo de errores**: Captura y registra errores sin afectar la aplicación
- **Ejecución asíncrona**: No bloquea la interfaz de usuario
- **Validación de configuraciones**: Solo usa configuraciones probadas

## 🎉 Beneficios

1. **Continuidad del servicio**: Reconexión automática ante fallos de red
2. **Supervivencia a reinicios**: Configuraciones persistentes
3. **Operación desatendida**: Funciona sin intervención del usuario
4. **Monitoreo completo**: Logs detallados de todas las operaciones
5. **Control granular**: Posibilidad de deshabilitar reconexión cuando sea necesario
