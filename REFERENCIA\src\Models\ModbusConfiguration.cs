using prueba.Services;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text.Json;

namespace prueba.Models
{
    /// <summary>
    /// Configuration model for Modbus communication settings.
    /// Implements INotifyPropertyChanged for data binding.
    /// </summary>
    public class ModbusConfiguration : INotifyPropertyChanged
    {
        #region Private Fields

        private string _name = "Default Configuration";
        private ModbusService.ModbusConnectionType _connectionType = ModbusService.ModbusConnectionType.TCP;
        private byte _deviceId = 1;

        // TCP settings
        public string _ipAddress = "127.0.0.1";
        public int _port = 502;

        // RTU settings
        private string _comPort = "COM1";
        private int _baudRate = 9600;
        private Parity _parity = Parity.None;
        private int _dataBits = 8;
        private StopBits _stopBits = StopBits.One;

        // General settings
        private int _timeoutMs = 1000;
        private int _retryCount = 3;
        private int _retryDelayMs = 100;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the configuration name
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// Gets or sets the Modbus connection type (TCP or RTU)
        /// </summary>
        public ModbusService.ModbusConnectionType ConnectionType
        {
            get => _connectionType;
            set => SetProperty(ref _connectionType, value);
        }

        /// <summary>
        /// Gets or sets the Modbus device ID (slave address)
        /// </summary>
        public byte DeviceId
        {
            get => _deviceId;
            set => SetProperty(ref _deviceId, value);
        }

        /// <summary>
        /// Gets or sets the IP address or hostname for TCP connections
        /// </summary>
        public string IpAddress
        {
            get => _ipAddress;
            set => SetProperty(ref _ipAddress, value);
        }

        /// <summary>
        /// Gets or sets the TCP port number (default is 502)
        /// </summary>
        public int Port
        {
            get => _port;
            set => SetProperty(ref _port, value);
        }

        /// <summary>
        /// Gets or sets the COM port name for RTU connections (e.g., "COM1")
        /// </summary>
        public string ComPort
        {
            get => _comPort;
            set => SetProperty(ref _comPort, value);
        }

        /// <summary>
        /// Gets or sets the baud rate for RTU connections
        /// </summary>
        public int BaudRate
        {
            get => _baudRate;
            set => SetProperty(ref _baudRate, value);
        }

        /// <summary>
        /// Gets or sets the parity for RTU connections
        /// </summary>
        public Parity Parity
        {
            get => _parity;
            set => SetProperty(ref _parity, value);
        }

        /// <summary>
        /// Gets or sets the data bits for RTU connections
        /// </summary>
        public int DataBits
        {
            get => _dataBits;
            set => SetProperty(ref _dataBits, value);
        }

        /// <summary>
        /// Gets or sets the stop bits for RTU connections
        /// </summary>
        public StopBits StopBits
        {
            get => _stopBits;
            set => SetProperty(ref _stopBits, value);
        }

        /// <summary>
        /// Gets or sets the communication timeout in milliseconds
        /// </summary>
        public int TimeoutMs
        {
            get => _timeoutMs;
            set => SetProperty(ref _timeoutMs, value);
        }

        /// <summary>
        /// Gets or sets the number of retry attempts for failed communications
        /// </summary>
        public int RetryCount
        {
            get => _retryCount;
            set => SetProperty(ref _retryCount, value);
        }

        /// <summary>
        /// Gets or sets the delay between retry attempts in milliseconds
        /// </summary>
        public int RetryDelayMs
        {
            get => _retryDelayMs;
            set => SetProperty(ref _retryDelayMs, value);
        }

        #endregion

        #region Methods

        /// <summary>
        /// Creates a ModbusService configured with this configuration's settings
        /// </summary>
        /// <returns>Configured ModbusService instance</returns>
        public ModbusService CreateService()
        {
            ModbusService service;
            Logger.Log($"ModbusConfiguration '{Name}': Creating ModbusService. Type: {ConnectionType}", LogLevel.Debug);
            if (ConnectionType == ModbusService.ModbusConnectionType.TCP)
            {
                service = new ModbusService(IpAddress, Port, DeviceId);
            }
            else
            {
                service = new ModbusService(ComPort, BaudRate, DeviceId, Parity, DataBits, StopBits);
            }

            // Set general properties
            service.TimeoutMs = TimeoutMs;
            service.RetryCount = RetryCount;
            service.RetryDelayMs = RetryDelayMs;

            Logger.Log($"ModbusConfiguration '{Name}': ModbusService created and configured.", LogLevel.Info);
            return service;
        }

        /// <summary>
        /// Saves the configuration to a JSON file
        /// </summary>
        /// <param name="filePath">Path to save the configuration</param>
        public void SaveToFile(string filePath)
        {
            Logger.Log($"ModbusConfiguration '{Name}': Saving to file: {filePath}", LogLevel.Debug);
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() } // Ensure enums are saved as strings
                };

                string jsonString = JsonSerializer.Serialize(this, options);
                File.WriteAllText(filePath, jsonString);
                Logger.Log($"ModbusConfiguration '{Name}': Successfully saved to {filePath}", LogLevel.Info);
            }
            catch (Exception ex)
            {
                Logger.Log($"ModbusConfiguration '{Name}': Error saving to file {filePath}. Error: {ex.Message}", LogLevel.Error);
                throw; // Re-throw to allow higher level handling
            }
        }

        /// <summary>
        /// Loads a configuration from a JSON file
        /// </summary>
        /// <param name="filePath">Path to load the configuration from</param>
        /// <returns>Loaded ModbusConfiguration object</returns>
        public static ModbusConfiguration LoadFromFile(string filePath)
        {
            Logger.Log($"ModbusConfiguration: Attempting to load from file: {filePath}", LogLevel.Debug);
            if (!File.Exists(filePath))
            {
                Logger.Log($"ModbusConfiguration: File not found: {filePath}", LogLevel.Warning);
                throw new FileNotFoundException($"Configuration file not found: {filePath}");
            }

            try
            {
                string jsonString = File.ReadAllText(filePath);
                var options = new JsonSerializerOptions
                {
                    Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() } // Ensure enums are read as strings
                };
                var loadedConfig = JsonSerializer.Deserialize<ModbusConfiguration>(jsonString, options);
                if (loadedConfig == null)
                {
                    Logger.Log($"ModbusConfiguration: Deserialization of {filePath} resulted in null. Creating default.", LogLevel.Warning);
                    return new ModbusConfiguration { Name = Path.GetFileNameWithoutExtension(filePath) }; // Return a default with the filename as name
                }
                Logger.Log($"ModbusConfiguration: Successfully loaded '{loadedConfig.Name}' from {filePath}", LogLevel.Info);
                return loadedConfig;
            }
            catch (Exception ex)
            {
                Logger.Log($"ModbusConfiguration: Error loading from file {filePath}. Error: {ex.Message}", LogLevel.Error);
                throw; // Re-throw for higher level handling
            }
        }

        /// <summary>
        /// Creates a deep copy of this configuration
        /// </summary>
        /// <returns>A new ModbusConfiguration instance with the same settings</returns>
        public ModbusConfiguration Clone()
        {
            return new ModbusConfiguration
            {
                Name = Name,
                ConnectionType = ConnectionType,
                DeviceId = DeviceId,
                IpAddress = IpAddress,
                Port = Port,
                ComPort = ComPort,
                BaudRate = BaudRate,
                Parity = Parity,
                DataBits = DataBits,
                StopBits = StopBits,
                TimeoutMs = TimeoutMs,
                RetryCount = RetryCount,
                RetryDelayMs = RetryDelayMs
            };
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Event triggered when a property value changes
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        /// <param name="propertyName">Name of the property that changed</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Sets a property value and raises the PropertyChanged event if the value changed
        /// </summary>
        /// <typeparam name="T">Type of the property</typeparam>
        /// <param name="storage">Reference to the backing field</param>
        /// <param name="value">New value</param>
        /// <param name="propertyName">Name of the property (auto-filled by compiler)</param>
        /// <returns>True if the value changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(storage, value))
            {
                return false;
            }

            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }

    /// <summary>
    /// Manages a collection of Modbus configurations
    /// </summary>
    public class ModbusConfigurationManager
    {
        /// <summary>
        /// Default directory for storing configuration files
        /// </summary>
        public const string DEFAULT_CONFIG_DIRECTORY = "ModbusConfigs";

        /// <summary>
        /// Default file extension for configuration files
        /// </summary>
        public const string CONFIG_FILE_EXTENSION = ".modbuscfg";

        /// <summary>
        /// Collection of available configurations
        /// </summary>
        public List<ModbusConfiguration> Configurations { get; private set; } = new List<ModbusConfiguration>();

        /// <summary>
        /// Directory path for loading/saving configurations
        /// </summary>
        public string ConfigDirectory { get; set; }

        /// <summary>
        /// Initializes a new instance of the ModbusConfigurationManager
        /// </summary>
        /// <param name="configDirectory">Directory for loading/saving configurations (default: ModbusConfigs)</param>
        public ModbusConfigurationManager(string configDirectory = DEFAULT_CONFIG_DIRECTORY)
        {
            ConfigDirectory = Path.GetFullPath(configDirectory); // Ensure absolute path
            Logger.Log($"ModbusConfigurationManager: Initialized. Config directory: {ConfigDirectory}", LogLevel.Info);
            LoadAllConfigurations();
        }

        /// <summary>
        /// Loads all configurations from the configuration directory
        /// </summary>
        public void LoadAllConfigurations()
        {
            Logger.Log("ModbusConfigurationManager: Loading all configurations.", LogLevel.Debug);
            EnsureConfigDirectoryExists();
            Configurations.Clear();

            try
            {
                string[] configFiles = Directory.GetFiles(ConfigDirectory, $"*{CONFIG_FILE_EXTENSION}");
                Logger.Log($"ModbusConfigurationManager: Found {configFiles.Length} configuration files in {ConfigDirectory}.", LogLevel.Info);

                foreach (string filePath in configFiles)
                {
                    try
                    {
                        var config = ModbusConfiguration.LoadFromFile(filePath);
                        Configurations.Add(config);
                        Logger.Log($"ModbusConfigurationManager: Loaded configuration '{config.Name}' from {Path.GetFileName(filePath)}.", LogLevel.Debug);
                    }
                    catch (Exception ex)
                    {
                        Logger.Log($"ModbusConfigurationManager: Failed to load configuration from {Path.GetFileName(filePath)}. Error: {ex.Message}", LogLevel.Error);
                        // Optionally, add a placeholder or skip the corrupted file
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"ModbusConfigurationManager: Error reading configuration directory {ConfigDirectory}. Error: {ex.Message}", LogLevel.Error);
                // Handle directory access errors, etc.
            }

            if (!Configurations.Any())
            {
                Logger.Log("ModbusConfigurationManager: No configurations found or loaded. Creating a default configuration.", LogLevel.Info);
                var defaultConfig = new ModbusConfiguration { Name = "Default TCP" };
                Configurations.Add(defaultConfig);
                SaveConfiguration(defaultConfig); // Save it so it exists for next time
            }
            Logger.Log($"ModbusConfigurationManager: Finished loading configurations. Total: {Configurations.Count}", LogLevel.Info);
        }

        /// <summary>
        /// Saves a configuration to the configuration directory
        /// </summary>
        /// <param name="config">Configuration to save</param>
        public void SaveConfiguration(ModbusConfiguration config)
        {
            if (config == null)
            {
                Logger.Log("ModbusConfigurationManager: SaveConfiguration called with null config.", LogLevel.Warning);
                return;
            }

            Logger.Log($"ModbusConfigurationManager: Saving configuration '{config.Name}'.", LogLevel.Debug);
            EnsureConfigDirectoryExists();
            string fileName = $"{SanitizeFileName(config.Name)}{CONFIG_FILE_EXTENSION}";
            string filePath = Path.Combine(ConfigDirectory, fileName);

            try
            {
                config.SaveToFile(filePath);
                Logger.Log($"ModbusConfigurationManager: Configuration '{config.Name}' saved to {filePath}.", LogLevel.Info);

                // If this is a new config or name changed, ensure it's in our list
                var existingConfig = Configurations.FirstOrDefault(c => c.Name.Equals(config.Name, StringComparison.OrdinalIgnoreCase));
                if (existingConfig == null)
                {
                    Configurations.Add(config);
                    Logger.Log($"ModbusConfigurationManager: New configuration '{config.Name}' added to internal list.", LogLevel.Debug);
                }
                else if (existingConfig != config) // If it is a different instance with the same name, replace
                {
                    int index = Configurations.IndexOf(existingConfig);
                    Configurations[index] = config;
                    Logger.Log($"ModbusConfigurationManager: Configuration '{config.Name}' updated in internal list.", LogLevel.Debug);
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"ModbusConfigurationManager: Failed to save configuration '{config.Name}' to {filePath}. Error: {ex.Message}", LogLevel.Error);
                // Handle file save errors
            }
        }

        /// <summary>
        /// Deletes a configuration
        /// </summary>
        /// <param name="config">Configuration to delete</param>
        public void DeleteConfiguration(ModbusConfiguration config)
        {
            if (config == null)
            {
                Logger.Log("ModbusConfigurationManager: DeleteConfiguration called with null config.", LogLevel.Warning);
                return;
            }

            string fileName = $"{SanitizeFileName(config.Name)}{CONFIG_FILE_EXTENSION}";
            string filePath = Path.Combine(ConfigDirectory, fileName);

            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    Logger.Log($"ModbusConfigurationManager: Deleted configuration file {filePath}.", LogLevel.Info);
                }
                else
                {
                    Logger.Log($"ModbusConfigurationManager: Configuration file not found for deletion: {filePath}. Removing from list anyway.", LogLevel.Warning);
                }

                Configurations.Remove(config);
                Logger.Log($"ModbusConfigurationManager: Configuration '{config.Name}' removed from internal list.", LogLevel.Info);
            }
            catch (Exception ex)
            {
                Logger.Log($"ModbusConfigurationManager: Failed to delete configuration file {filePath} or remove from list. Error: {ex.Message}", LogLevel.Error);
                // Handle file deletion errors
            }
        }

        /// <summary>
        /// Creates the configuration directory if it doesn't exist
        /// </summary>
        private void EnsureConfigDirectoryExists()
        {
            if (!Directory.Exists(ConfigDirectory))
            {
                try
                {
                    Directory.CreateDirectory(ConfigDirectory);
                    Logger.Log($"ModbusConfigurationManager: Created configuration directory: {ConfigDirectory}", LogLevel.Info);
                }
                catch (Exception ex)
                {
                    Logger.Log($"ModbusConfigurationManager: Failed to create configuration directory {ConfigDirectory}. Error: {ex.Message}", LogLevel.Error);
                    throw; // Critical if we can't create it
                }
            }
        }

        /// <summary>
        /// Sanitizes a file name by removing invalid characters
        /// </summary>
        /// <param name="fileName">Original file name</param>
        /// <returns>Sanitized file name</returns>
        private string SanitizeFileName(string fileName)
        {
            char[] invalidChars = Path.GetInvalidFileNameChars();
            return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries)).TrimEnd('.');
        }
    }
}