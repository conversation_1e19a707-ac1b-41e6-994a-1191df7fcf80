﻿using System;
using System.Drawing;
using System.Windows.Forms;

#pragma warning disable IDE0090
#pragma warning disable CA1416

namespace prueba
{
    /// <summary>
    /// Manages the navigation logic between different panels in the main form.
    /// </summary>
    public class NavigationManager
    {
        private readonly Button _btnNavConnection;
        private readonly Button _btnNavConfig;
        private readonly Button _btnNavSinoptico;
        private readonly Button _btnNavMail;
        private readonly Button _btnNavModbus;
        private readonly Panel _panelConnectionContent;
        private readonly Panel _panelConfigContent;
        private readonly Panel _panelSinopticoContent;
        private readonly Panel _panelMailContent;
        private readonly Panel _panelModbusContent;
        private readonly Font _regularFont = new Font("Segoe UI", 9.75F, FontStyle.Regular);
        private readonly Font _boldFont = new Font("Segoe UI", 9.75F, FontStyle.Bold);
        private readonly Color _activeColor = Color.FromArgb(0, 122, 204);
        private readonly Color _inactiveColor = Color.FromArgb(60, 63, 65);

        /// <summary>
        /// Initializes a new instance of the <see cref="NavigationManager"/> class.
        /// </summary>
        /// <param name="btnNavConnection">The connection navigation button.</param>
        /// <param name="btnNavConfig">The configuration navigation button.</param>
        /// <param name="btnNavSinoptico">The sinoptico navigation button.</param>
        /// <param name="btnNavMail">The mail navigation button.</param>
        /// <param name="btnNavModbus">The modbus navigation button.</param>
        /// <param name="panelConnectionContent">The connection content panel.</param>
        /// <param name="panelConfigContent">The configuration content panel.</param>
        /// <param name="panelSinopticoContent">The sinoptico content panel.</param>
        /// <param name="panelMailContent">The mail content panel.</param>
        /// <param name="panelModbusContent">The modbus content panel.</param>
        public NavigationManager(
            Button btnNavConnection, Button btnNavConfig, Button btnNavSinoptico, Button btnNavMail, Button btnNavModbus,
            Panel panelConnectionContent, Panel panelConfigContent, Panel panelSinopticoContent, Panel panelMailContent, Panel panelModbusContent)
        {
            _btnNavConnection = btnNavConnection ?? throw new ArgumentNullException(nameof(btnNavConnection));
            _btnNavConfig = btnNavConfig ?? throw new ArgumentNullException(nameof(btnNavConfig));
            _btnNavSinoptico = btnNavSinoptico ?? throw new ArgumentNullException(nameof(btnNavSinoptico));
            _btnNavMail = btnNavMail ?? throw new ArgumentNullException(nameof(btnNavMail));
            _btnNavModbus = btnNavModbus ?? throw new ArgumentNullException(nameof(btnNavModbus));

            _panelConnectionContent = panelConnectionContent ?? throw new ArgumentNullException(nameof(panelConnectionContent));
            _panelConfigContent = panelConfigContent ?? throw new ArgumentNullException(nameof(panelConfigContent));
            _panelSinopticoContent = panelSinopticoContent ?? throw new ArgumentNullException(nameof(panelSinopticoContent));
            _panelMailContent = panelMailContent ?? throw new ArgumentNullException(nameof(panelMailContent));
            _panelModbusContent = panelModbusContent ?? throw new ArgumentNullException(nameof(panelModbusContent));
        }

        /// <summary>
        /// Switches the visible panel based on the provided panel name.
        /// </summary>
        /// <param name="panelName">Name of the panel to switch to. Expected values: "Connection", "Config", "Sinoptico", "Mail", "Modbus".</param>
        public void SwitchToPanel(string panelName)
        {
            // Hide all panels
            _panelConnectionContent.Visible = false;
            _panelConfigContent.Visible = false;
            _panelSinopticoContent.Visible = false;
            _panelMailContent.Visible = false;
            _panelModbusContent.Visible = false;

            // Reset all button styles
            ResetButtonStyles(_btnNavConnection);
            ResetButtonStyles(_btnNavConfig);
            ResetButtonStyles(_btnNavSinoptico);
            ResetButtonStyles(_btnNavMail);
            ResetButtonStyles(_btnNavModbus);

            Panel panelToShow = null;
            Button buttonToHighlight = null;

            switch (panelName?.ToLowerInvariant())
            {
                case "connection":
                    panelToShow = _panelConnectionContent;
                    buttonToHighlight = _btnNavConnection;
                    break;
                case "config": // Corresponds to original BtnNavPublish_Click
                    panelToShow = _panelConfigContent;
                    buttonToHighlight = _btnNavConfig;
                    break;
                case "sinoptico": // Corresponds to original BtnNavSubscribe_Click
                    panelToShow = _panelSinopticoContent;
                    buttonToHighlight = _btnNavSinoptico;
                    break;
                case "mail":
                    panelToShow = _panelMailContent;
                    buttonToHighlight = _btnNavMail;
                    break;
                case "modbus":
                    panelToShow = _panelModbusContent;
                    buttonToHighlight = _btnNavModbus;
                    break;
                default:
                    // Optional: Log an error or throw an exception for unknown panel name
                    // For now, we'll just log to console if something unexpected happens.
                    Logger.Log($"NavigationManager: Unknown panel name '{panelName}' requested.", LogLevel.Warning);
                    return;
            }

            if (panelToShow != null)
            {
                panelToShow.Visible = true;
                panelToShow.BringToFront();
                if (buttonToHighlight != null)
                {
                    HighlightButton(buttonToHighlight);
                }
            }
        }

        private void ResetButtonStyles(Button btn)
        {
            if (btn != null)
            {
                btn.BackColor = _inactiveColor;
                btn.Font = _regularFont;
            }
        }

        private void HighlightButton(Button btn)
        {
            if (btn != null)
            {
                btn.BackColor = _activeColor;
                btn.Font = _boldFont;
            }
        }
    }
} 