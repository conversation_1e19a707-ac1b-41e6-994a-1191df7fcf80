using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace prueba
{
    public static class Logger
    {
        private static RichTextBox consoleTextBox;
        private static string logFilePath;
        private static readonly object lockObject = new object();

        // Throttling mechanism for repetitive messages
        private static readonly Dictionary<string, DateTime> lastLoggedTimes = new Dictionary<string, DateTime>();
        private static readonly Dictionary<string, int> messageCounters = new Dictionary<string, int>();
        private static readonly TimeSpan throttleInterval = TimeSpan.FromSeconds(30); // Throttle same message for 30 seconds

        public static void Initialize(RichTextBox console)
        {
            consoleTextBox = console;
            consoleTextBox.Font = new Font("Segoe UI", 12F, FontStyle.Bold);

            // Configurar el archivo de log
            string logDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "PruebaMQTT",
                "Logs"
            );

            if (!Directory.Exists(logDirectory))
                Directory.CreateDirectory(logDirectory);

            logFilePath = Path.Combine(logDirectory, $"log_{DateTime.Now:yyyy-MM-dd}.txt");
        }

        public static void Log(string message, LogLevel level = LogLevel.Info)
        {
            string timeStamp = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
            string formattedMessage = $"[{timeStamp}] [{level}] - {message}";

            // Always write to file (no throttling for file logging)
            WriteToFile(formattedMessage);

            // Check if we should throttle this message for UI display
            bool shouldDisplayInUI = ShouldDisplayMessage(message, level);

            // Actualizar la UI only if not throttled
            if (shouldDisplayInUI && consoleTextBox != null && !consoleTextBox.IsDisposed)
            {
                if (consoleTextBox.InvokeRequired)
                {
                    consoleTextBox.Invoke(new Action(() => UpdateConsole(formattedMessage, level)));
                }
                else
                {
                    UpdateConsole(formattedMessage, level);
                }
            }
        }

        /// <summary>
        /// Determines if a message should be displayed in the UI based on throttling rules
        /// </summary>
        private static bool ShouldDisplayMessage(string message, LogLevel level)
        {
            lock (lockObject)
            {
                // Don't throttle Info, Success, or Debug messages - only Warning and Error
                if (level != LogLevel.Warning && level != LogLevel.Error)
                    return true;

                DateTime now = DateTime.Now;
                string messageKey = $"{level}:{message}";

                // Check if we've seen this message before
                if (lastLoggedTimes.TryGetValue(messageKey, out DateTime lastTime))
                {
                    // If the message was logged recently, increment counter and don't display
                    if (now - lastTime < throttleInterval)
                    {
                        messageCounters[messageKey] = messageCounters.GetValueOrDefault(messageKey, 1) + 1;
                        return false;
                    }
                    else
                    {
                        // Time interval has passed, show the message and reset counter
                        if (messageCounters.TryGetValue(messageKey, out int count) && count > 1)
                        {
                            // Show a summary of how many times the message was suppressed
                            string summaryMessage = $"[{now:dd/MM/yyyy HH:mm:ss}] [Info] - Mensaje anterior repetido {count - 1} veces más (suprimido)";
                            if (consoleTextBox != null && !consoleTextBox.IsDisposed)
                            {
                                if (consoleTextBox.InvokeRequired)
                                {
                                    consoleTextBox.Invoke(new Action(() => UpdateConsole(summaryMessage, LogLevel.Info)));
                                }
                                else
                                {
                                    UpdateConsole(summaryMessage, LogLevel.Info);
                                }
                            }
                        }

                        lastLoggedTimes[messageKey] = now;
                        messageCounters[messageKey] = 1;
                        return true;
                    }
                }
                else
                {
                    // First time seeing this message
                    lastLoggedTimes[messageKey] = now;
                    messageCounters[messageKey] = 1;
                    return true;
                }
            }
        }

        private static void UpdateConsole(string message, LogLevel level)
        {
            if (consoleTextBox == null || consoleTextBox.IsDisposed)
                return;

            Color messageColor = GetColorForLogLevel(level);

            // Guardar la posici�n actual del cursor y el color
            int currentPosition = consoleTextBox.TextLength;

            consoleTextBox.AppendText(message + Environment.NewLine);

            // Aplicar el color al texto reci�n a�adido
            consoleTextBox.Select(currentPosition, message.Length);
            consoleTextBox.SelectionColor = messageColor;

            // Restaurar la selecci�n al final
            consoleTextBox.SelectionStart = consoleTextBox.TextLength;
            consoleTextBox.ScrollToCaret();
        }

        private static void WriteToFile(string message)
        {
            if (string.IsNullOrEmpty(logFilePath)) return;

            lock (lockObject)
            {
                try
                {
                    File.AppendAllText(logFilePath, message + Environment.NewLine, Encoding.UTF8);
                }
                catch (Exception)
                {
                    // Silenciosamente fallar si no podemos escribir al archivo
                }
            }
        }

        private static Color GetColorForLogLevel(LogLevel level)
        {
            switch (level)
            {
                case LogLevel.Error:
                    return Color.Red;
                case LogLevel.Warning:
                    return Color.Orange;
                case LogLevel.Success:
                    return Color.Green;
                case LogLevel.Info:
                    return Color.LightGreen;
                case LogLevel.Debug:
                    return Color.Gray;
                default:
                    return Color.White;
            }
        }

        public static void Clear()
        {
            if (consoleTextBox != null && !consoleTextBox.IsDisposed)
            {
                if (consoleTextBox.InvokeRequired)
                {
                    consoleTextBox.Invoke(new Action(() => consoleTextBox.Clear()));
                }
                else
                {
                    consoleTextBox.Clear();
                }
            }
        }
    }

    public enum LogLevel
    {
        Debug,
        Info,
        Success,
        Warning,
        Error
    }
}