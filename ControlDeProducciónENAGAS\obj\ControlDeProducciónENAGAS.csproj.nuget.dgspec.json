{"format": 1, "restore": {"C:\\ProgramaciónIA\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS.csproj": {}}, "projects": {"C:\\ProgramaciónIA\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\ProgramaciónIA\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS.csproj", "projectName": "ControlDeProducciónENAGAS", "projectPath": "C:\\ProgramaciónIA\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\ProgramaciónIA\\ControlDeProducciónENAGAS\\ControlDeProducciónENAGAS\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"EasyModbusTCP": {"target": "Package", "version": "[5.6.0, )"}, "MQTTnet": {"target": "Package", "version": "[4.3.7, )"}, "Microsoft.Exchange.WebServices": {"target": "Package", "version": "[2.2.0, )"}, "System.IO.Ports": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.303/PortableRuntimeIdentifierGraph.json"}}}}}