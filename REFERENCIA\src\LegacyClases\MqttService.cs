﻿using MQTTnet;
using MQTTnet.Client;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Security.Authentication;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace prueba
{
    public class MqttService
    {
        private IMqttClient mqttClient;
        public Action<Color> updateStatusView;
        private Dictionary<string, string> lastMessageByTopic = new Dictionary<string, string>();
        private TopicValueStorage topicValueStorage;
        private string currentProfileIdentifierForTopics; // Para saber qué archivo de tópicos usar
        public event Action MessagesUpdated;

        /// <summary>
        /// Indica si el cliente MQTT está conectado
        /// </summary>
        public bool IsConnected
        {
            get { return mqttClient?.IsConnected ?? false; }
        }

        public MqttService(Action<Color> statusUpdater)
        {
            this.updateStatusView = statusUpdater;

            try
            {
                this.topicValueStorage = new TopicValueStorage();
            }
            catch (Exception ex)
            {
                Logger.Log($"Error crítico creando TopicValueStorage: {ex.Message}\nFuncionalidad de guardado/carga de tópicos deshabilitada", LogLevel.Error);
                this.topicValueStorage = null;
            }
            // YA NO SE CARGAN LOS TÓPICOS AQUÍ AL INICIO
            // LoadInitialTopicValues(); 

            var factory = new MqttFactory();
            mqttClient = factory.CreateMqttClient();
            ConfigureEventHandlers();
        }

        // Método para cargar tópicos para un perfil específico ANTES de conectar
        public void LoadTopicsForProfile(string profileIdentifier)
        {
            this.currentProfileIdentifierForTopics = profileIdentifier; // Guardar para el guardado al salir
            if (this.topicValueStorage != null && !string.IsNullOrEmpty(profileIdentifier))
            {
                this.lastMessageByTopic = topicValueStorage.Load(profileIdentifier);
                Logger.Log("Mensajes cargados exitosamente", LogLevel.Debug);
            }
            else
            {
                this.lastMessageByTopic.Clear(); // Si no hay perfil o storage, empezar vacío
            }
            // La UI se actualizará desde Form1 después de esta llamada
        }


        private void ConfigureEventHandlers()
        {
            mqttClient.ConnectedAsync += async e =>
            {
                updateStatusView(Color.Green);
                // Update the new status indicator
                if (System.Windows.Forms.Application.OpenForms.OfType<Form1>().FirstOrDefault() is Form1 form)
                {
                    form.UpdateMqttStatus(true, "MQTT: Conectado");
                }
                await mqttClient.SubscribeAsync(new MqttTopicFilterBuilder().WithTopic("DD/ENAGAS/ALMENDRALEJO/#").Build());
                MessagesUpdated?.Invoke();
            };

            mqttClient.DisconnectedAsync += async e =>
            {
                updateStatusView(Color.Gray);
                // Update the new status indicator
                if (System.Windows.Forms.Application.OpenForms.OfType<Form1>().FirstOrDefault() is Form1 form)
                {
                    form.UpdateMqttStatus(false, "MQTT: Desconectado");
                }
                await Task.CompletedTask;
            };

            mqttClient.ApplicationMessageReceivedAsync += e =>
            {
                var topic = e.ApplicationMessage.Topic;
                var payload = Encoding.UTF8.GetString(
                    e.ApplicationMessage.PayloadSegment.Array,
                    e.ApplicationMessage.PayloadSegment.Offset,
                    e.ApplicationMessage.PayloadSegment.Count
                        );

                lock (lastMessageByTopic)
                {
                    lastMessageByTopic[topic] = payload;
                }
                MessagesUpdated?.Invoke();
                //Logger.Log($"Mensaje recibido - Tópico: [{topic}], Mensaje: [{payload}]", LogLevel.Debug);
                return Task.CompletedTask;
            };
        }

        public async Task ConnectAsync(
            string protocol,
            string hostAddress,
            int port,
            string clientId,
            string username,
            string password,
            bool useTlsFromCheckbox,
            bool isCASigned,
            bool useAlpn)
        {
            updateStatusView(Color.Blue); // Indicar inicio del proceso de conexión
            try
            {
                // La carga de tópicos para el perfil ya se hizo ANTES de llamar a este método
                // desde Form1.ConnectToBroker

                var optionsBuilder = new MqttClientOptionsBuilder()
                    .WithClientId(clientId)
                    .WithCredentials(username, password);

                bool isWebSocket = protocol == "ws://" || protocol == "wss://";
                bool enableTls = protocol == "mqtts://" || protocol == "wss://" || useTlsFromCheckbox;

                if (isWebSocket)
                {
                    string wsProtocol = (enableTls) ? "wss" : "ws";
                    string wsUri = $"{wsProtocol}://{hostAddress}:{port}/mqtt";
                    optionsBuilder.WithWebSocketServer(wsUri);
                }
                else
                {
                    optionsBuilder.WithTcpServer(hostAddress, port);
                }

                if (enableTls)
                {
                    var tlsOptions = new MqttClientOptionsBuilderTlsParameters
                    {
                        UseTls = true,
                        SslProtocol = SslProtocols.Tls12,
                        AllowUntrustedCertificates = !isCASigned,
                        IgnoreCertificateChainErrors = !isCASigned,
                        IgnoreCertificateRevocationErrors = !isCASigned
                    };
                    optionsBuilder.WithTls(tlsOptions);
                }


                var options = optionsBuilder.Build();
                await mqttClient.ConnectAsync(options, System.Threading.CancellationToken.None);
                Logger.Log("Conectado exitosamente al broker MQTT", LogLevel.Success);
                // Si ConnectedAsync se dispara, pondrá el status en verde
            }
            catch (Exception ex)
            {
                updateStatusView(Color.Red); // Falló la conexión
                // Update the new status indicator
                if (System.Windows.Forms.Application.OpenForms.OfType<Form1>().FirstOrDefault() is Form1 form)
                {
                    form.UpdateMqttStatus(false, "MQTT: Error de conexión");
                }
                Logger.Log($"Error de conexión MQTT: {ex.Message}", LogLevel.Error);
            }
        }

        public async Task PublishAsync(string topic, string message)
        {
            if (!mqttClient.IsConnected)
            {
                Logger.Log("Error al publicar: No conectado al broker MQTT", LogLevel.Error);
                return;
            }

            try
            {
                var mqttMessage = new MqttApplicationMessageBuilder()
                    .WithTopic(topic)
                    .WithPayload(Encoding.UTF8.GetBytes(message))
                    .Build();

                await mqttClient.PublishAsync(mqttMessage, System.Threading.CancellationToken.None);
                Logger.Log($"Mensaje publicado en tópico {topic}: {message}");
            }
            catch (Exception ex)
            {
                Logger.Log($"Error de publicación MQTT: {ex.Message}", LogLevel.Error);
            }
        }

        public async Task DisconnectAsync()
        {
            if (mqttClient.IsConnected)
            {
                await mqttClient.DisconnectAsync();
                Logger.Log("Desconectado del broker", LogLevel.Warning);
                // El estado se actualiza a Gris en el evento DisconnectedAsync
                // Update the new status indicator
                if (System.Windows.Forms.Application.OpenForms.OfType<Form1>().FirstOrDefault() is Form1 form)
                {
                    form.UpdateMqttStatus(false, "MQTT: Desconectado");
                }
            }
            // No limpiar currentProfileIdentifierForTopics aquí, se usa para el guardado al salir
        }

        public Dictionary<string, string> GetLastMessages()
        {
            lock (lastMessageByTopic)
            {
                return new Dictionary<string, string>(lastMessageByTopic);
            }
        }

        public void ClearStoredMessages()
        {
            // Limpia solo la memoria, no el archivo.
            // El archivo se sobrescribe al guardar con la nueva sesión.
            lastMessageByTopic.Clear();
            //currentProfileIdentifierForTopics no se limpia aquí para saber donde guardar.
        }

        public void SaveTopicsOnExit()
        {
            if (topicValueStorage != null && !string.IsNullOrEmpty(currentProfileIdentifierForTopics))
            {
                topicValueStorage.Save(this.lastMessageByTopic, currentProfileIdentifierForTopics);
            }
            else if (topicValueStorage != null)
            {
                // Opcional: Guardar en un archivo "default" si no hay perfil activo
                // topicValueStorage.Save(this.lastMessageByTopic, "default_session");
            }
        }

        /// <summary>
        /// Suscribe al cliente a un tópico específico
        /// </summary>
        /// <param name="topic">El tópico al que suscribirse</param>
        /// <returns>Task que representa la operación asíncrona</returns>
        public async Task SubscribeToTopic(string topic)
        {
            if (mqttClient == null || !mqttClient.IsConnected)
            {
                return;
            }

            try
            {
                var mqttSubscribeOptions = new MqttClientSubscribeOptionsBuilder()
                    .WithTopicFilter(f => f.WithTopic(topic))
                    .Build();

                await mqttClient.SubscribeAsync(mqttSubscribeOptions, CancellationToken.None);
                Logger.Log($"Suscrito al tópico: {topic}", LogLevel.Success);
            }
            catch (Exception ex)
            {
                Logger.Log($"Error al suscribirse al tópico {topic}: {ex.Message}", LogLevel.Error);
                Console.WriteLine($"Error al suscribirse al tópico {topic}: {ex.Message}");
            }
        }

        /// <summary>
        /// Cancela la suscripción del cliente a un tópico específico
        /// </summary>
        /// <param name="topic">El tópico del que desuscribirse</param>
        /// <returns>Task que representa la operación asíncrona</returns>
        public async Task UnsubscribeFromTopic(string topic)
        {
            if (mqttClient == null || !mqttClient.IsConnected)
            {
                return;
            }

            try
            {
                var mqttUnsubscribeOptions = new MqttClientUnsubscribeOptionsBuilder()
                    .WithTopicFilter(topic)
                    .Build();

                await mqttClient.UnsubscribeAsync(mqttUnsubscribeOptions, CancellationToken.None);
                Logger.Log($"Desuscrito del topico: {topic}", LogLevel.Info);
            }
            catch (Exception ex)
            {
                Logger.Log($"Error al desuscribirse del tópico {topic}: {ex.Message}", LogLevel.Error);
                Console.WriteLine($"Error al desuscribirse del tópico {topic}: {ex.Message}");
            }
        }

    }
}
