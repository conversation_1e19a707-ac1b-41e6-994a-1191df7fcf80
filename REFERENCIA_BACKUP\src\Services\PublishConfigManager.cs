﻿using System; // Para Exception
using System.Collections.Generic;
using System.IO;
using System.Text.Json; // Para JsonSerializer

namespace prueba // Asegúrate de que esté en el mismo namespace
{
    public class PublishConfigManager
    {
        public const string PublishConfigFileName = "publish_configurations.json"; // Cambiado a .json

        /// <summary>
        /// Guarda la lista de configuraciones de publicación en un archivo.
        /// </summary>
        /// <param name="configs">La lista de TopicPublishConfig a guardar.</param>
        public void SaveConfigurations(List<TopicPublishConfig> configs)
        {
            if (configs == null)
            {
                // Considera si quieres guardar una lista vacía o simplemente no hacer nada.
                Logger.Log("Intento de guardar una lista de configuraciones nula.", LogLevel.Warning);
                // Para borrar el archivo existente si se pasa null:
                // configs = new List<TopicPublishConfig>();
                return;
            }

            try
            {
                // Obtener la ruta completa al directorio de la aplicación
                string appDirectory = AppDomain.CurrentDomain.BaseDirectory;
                string filePath = Path.Combine(appDirectory, PublishConfigFileName);



                string json = JsonSerializer.Serialize(configs, new JsonSerializerOptions { WriteIndented = true });




                File.WriteAllText(filePath, json);



                Logger.Log($"Configuraciones de publicación guardadas en: {filePath}", LogLevel.Info);
            }
            catch (Exception ex)
            {
                Logger.Log($"Error al guardar las configuraciones de publicación: {ex.Message}", LogLevel.Error);
                Logger.Log($"Stack trace: {ex.StackTrace}", LogLevel.Error);
                throw; // Re-throw to let the caller handle it
            }
        }

        /// <summary>
        /// Carga la lista de configuraciones de publicación desde un archivo.
        /// </summary>
        /// <returns>Una lista de TopicPublishConfig. Devuelve una lista vacía si el archivo no existe o hay un error.</returns>
        public List<TopicPublishConfig> LoadConfigurations()
        {
            string appDirectory = AppDomain.CurrentDomain.BaseDirectory;
            string filePath = Path.Combine(appDirectory, PublishConfigFileName);

            if (!File.Exists(filePath))
            {
                Logger.Log($"Archivo de configuraciones '{filePath}' no encontrado. Se devolverá una lista vacía.", LogLevel.Info);
                return new List<TopicPublishConfig>(); // Devuelve lista vacía si el archivo no existe
            }

            List<TopicPublishConfig> configs = null;
            try
            {
                string json = File.ReadAllText(filePath);


                configs = JsonSerializer.Deserialize<List<TopicPublishConfig>>(json);



                Logger.Log($"Configuraciones de publicación cargadas desde: {filePath}. Total: {configs?.Count ?? 0} configuraciones.", LogLevel.Info);
                return configs ?? new List<TopicPublishConfig>(); // Asegurar que no se devuelve null
            }
            catch (Exception ex)
            {
                Logger.Log($"Error al cargar las configuraciones de publicación desde '{filePath}': {ex.Message}", LogLevel.Error);
                Logger.Log($"Stack trace: {ex.StackTrace}", LogLevel.Error);
                return new List<TopicPublishConfig>(); // Devuelve lista vacía en caso de error
            }
        }
    }
}
