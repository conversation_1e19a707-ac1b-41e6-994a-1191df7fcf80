using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using prueba.Clases;

namespace prueba.Services
{
    /// <summary>
    /// Manages periodic Modbus writes based on configured write topics with refresh rates
    /// </summary>
    public class TopicWritingManager : IDisposable
    {
        private readonly Dictionary<string, TopicWriteConfig> _topicConfigs = new Dictionary<string, TopicWriteConfig>();
        private readonly Dictionary<string, Timer> _topicTimers = new Dictionary<string, Timer>();
        private readonly Func<string, ushort, string, string, System.Threading.Tasks.Task> _writeToModbusCallback;
        private readonly Func<Dictionary<string, string>> _getLastMessagesCallback;
        private bool _disposed = false;

        /// <summary>
        /// Configuration for a write topic
        /// </summary>
        public class TopicWriteConfig
        {
            public string Topic { get; set; }
            public string DeviceId { get; set; }
            public ushort ModbusAddress { get; set; }
            public int RefreshRateMs { get; set; }
            public string RefreshRateString { get; set; }
            public bool IsFloatFormat { get; set; }
            public string OriginalAddressString { get; set; }
        }

        public TopicWritingManager(
            Func<string, ushort, string, string, System.Threading.Tasks.Task> writeToModbusCallback,
            Func<Dictionary<string, string>> getLastMessagesCallback)
        {
            _writeToModbusCallback = writeToModbusCallback ?? throw new ArgumentNullException(nameof(writeToModbusCallback));
            _getLastMessagesCallback = getLastMessagesCallback ?? throw new ArgumentNullException(nameof(getLastMessagesCallback));
            Logger.Log("TopicWritingManager initialized", LogLevel.Info);
        }

        /// <summary>
        /// Updates topic configurations from the write topics grid
        /// </summary>
        /// <param name="writeTopicsGrid">The DataGridView containing write topic configurations</param>
        public void UpdateTopicConfigurations(DataGridView writeTopicsGrid)
        {
            if (writeTopicsGrid == null)
            {
                Logger.Log("TopicWritingManager: WriteTopicsGrid is null", LogLevel.Warning);
                return;
            }

            Logger.Log("TopicWritingManager: Updating topic configurations...", LogLevel.Info);

            // Stop and clear existing timers
            StopAllTimers();
            _topicConfigs.Clear();

            // Process each row in the write topics grid
            foreach (DataGridViewRow row in writeTopicsGrid.Rows)
            {
                if (row.IsNewRow) continue;

                string topic = row.Cells["Topico"].Value?.ToString();
                string deviceId = row.Cells["IdDispositivo"].Value?.ToString();
                string modbusAddress = row.Cells["DireccionPLC"].Value?.ToString();
                string refreshRate = row.Cells["TasaRefresco"].Value?.ToString() ?? "Realtime";

                // Skip if any required field is empty
                if (string.IsNullOrEmpty(topic) || string.IsNullOrEmpty(deviceId) || string.IsNullOrEmpty(modbusAddress))
                    continue;

                // Skip if device ID indicates no configuration or error
                if (deviceId.Contains("No hay configuraciones") || deviceId.Contains("Error"))
                    continue;

                // Extract actual device ID from display format
                string actualDeviceId = deviceId;
                if (deviceId.Contains("No conectado"))
                {
                    // Extract from "No conectado - Left-1 (ConfigName)" format
                    var match = System.Text.RegularExpressions.Regex.Match(deviceId, @"No conectado - (.+?) \(");
                    if (match.Success)
                    {
                        actualDeviceId = match.Groups[1].Value;
                    }
                }
                else if (deviceId.Contains("Conectado"))
                {
                    // Extract from "Conectado - PLC-Principal-1 (ConfigName)" format
                    var match = System.Text.RegularExpressions.Regex.Match(deviceId, @"Conectado - (.+?) \(");
                    if (match.Success)
                    {
                        actualDeviceId = match.Groups[1].Value;
                    }
                }

                // Parse Modbus address (supports MF{address} format)
                if (!ModbusService.ParseAddress(modbusAddress, out ushort address, out bool isFloatFormat))
                {
                    Logger.Log($"TopicWritingManager: Invalid Modbus address format: {modbusAddress}", LogLevel.Warning);
                    continue;
                }

                // Handle "Realtime" refresh rate (no timer needed)
                if (refreshRate.Equals("Realtime", StringComparison.OrdinalIgnoreCase))
                {
                    var config = new TopicWriteConfig
                    {
                        Topic = topic,
                        DeviceId = actualDeviceId,
                        ModbusAddress = address,
                        RefreshRateMs = 0, // 0 indicates realtime
                        RefreshRateString = refreshRate,
                        IsFloatFormat = isFloatFormat,
                        OriginalAddressString = modbusAddress
                    };

                    _topicConfigs[topic] = config;
                    Logger.Log($"TopicWritingManager: Configured realtime topic '{topic}' for device '{actualDeviceId}' at address {address}", LogLevel.Info);
                    continue;
                }

                // Parse refresh rate to milliseconds for periodic writes
                int intervalMs = ParseRefreshRateToMilliseconds(refreshRate);
                if (intervalMs <= 0)
                {
                    Logger.Log($"TopicWritingManager: Invalid refresh rate: {refreshRate}", LogLevel.Warning);
                    continue;
                }

                // Create topic configuration
                var periodicConfig = new TopicWriteConfig
                {
                    Topic = topic,
                    DeviceId = actualDeviceId,
                    ModbusAddress = address,
                    RefreshRateMs = intervalMs,
                    RefreshRateString = refreshRate,
                    IsFloatFormat = isFloatFormat,
                    OriginalAddressString = modbusAddress
                };

                _topicConfigs[topic] = periodicConfig;

                // Create and start timer for this topic
                CreateTimerForTopic(periodicConfig);

                Logger.Log($"TopicWritingManager: Configured periodic topic '{topic}' with refresh rate {refreshRate} ({intervalMs}ms)", LogLevel.Info);
            }

            Logger.Log($"TopicWritingManager: Updated configurations for {_topicConfigs.Count} topics", LogLevel.Info);
        }

        /// <summary>
        /// Creates and starts a timer for a specific topic configuration
        /// </summary>
        /// <param name="config">The topic configuration</param>
        private void CreateTimerForTopic(TopicWriteConfig config)
        {
            var timer = new Timer();
            timer.Interval = config.RefreshRateMs;
            timer.Tick += async (s, e) =>
            {
                try
                {
                    await PerformPeriodicWrite(config);
                }
                catch (Exception ex)
                {
                    Logger.Log($"TopicWritingManager: Error writing topic '{config.Topic}': {ex.Message}", LogLevel.Error);
                }
            };

            _topicTimers[config.Topic] = timer;
            timer.Start();

            Logger.Log($"TopicWritingManager: Started timer for topic '{config.Topic}' with interval {config.RefreshRateMs}ms", LogLevel.Debug);
        }

        /// <summary>
        /// Performs a periodic write for a specific topic configuration
        /// </summary>
        /// <param name="config">The topic configuration</param>
        private async System.Threading.Tasks.Task PerformPeriodicWrite(TopicWriteConfig config)
        {
            try
            {
                var messages = _getLastMessagesCallback();
                if (messages == null || !messages.TryGetValue(config.Topic, out string topicValue) || string.IsNullOrEmpty(topicValue))
                {
                    Logger.Log($"TopicWritingManager: No value available for periodic write to topic '{config.Topic}'", LogLevel.Debug);
                    return;
                }

                // Detect data type and write to Modbus
                string dataType = DetectDataType(topicValue);
                await _writeToModbusCallback(config.DeviceId, config.ModbusAddress, topicValue, dataType);

                Logger.Log($"TopicWritingManager: Periodic write completed for topic '{config.Topic}' with value '{topicValue}'", LogLevel.Debug);
            }
            catch (Exception ex)
            {
                Logger.Log($"TopicWritingManager: Error in periodic write for topic '{config.Topic}': {ex.Message}", LogLevel.Error);
            }
        }

        /// <summary>
        /// Handles realtime writes for topics configured with "Realtime" refresh rate
        /// </summary>
        /// <param name="changedTopic">The topic that changed</param>
        /// <param name="newValue">The new value</param>
        public async System.Threading.Tasks.Task HandleRealtimeWrite(string changedTopic, string newValue)
        {
            if (string.IsNullOrEmpty(changedTopic) || string.IsNullOrEmpty(newValue))
                return;

            // Check if this topic is configured for realtime writes
            if (_topicConfigs.TryGetValue(changedTopic, out TopicWriteConfig config) && config.RefreshRateMs == 0)
            {
                try
                {
                    string dataType = DetectDataType(newValue);
                    await _writeToModbusCallback(config.DeviceId, config.ModbusAddress, newValue, dataType);
                    Logger.Log($"TopicWritingManager: Realtime write completed for topic '{changedTopic}' with value '{newValue}'", LogLevel.Info);
                }
                catch (Exception ex)
                {
                    Logger.Log($"TopicWritingManager: Error in realtime write for topic '{changedTopic}': {ex.Message}", LogLevel.Error);
                }
            }
        }

        /// <summary>
        /// Parses refresh rate string to milliseconds
        /// </summary>
        /// <param name="refreshRate">Refresh rate string (e.g., "1s", "5min")</param>
        /// <returns>Interval in milliseconds</returns>
        private int ParseRefreshRateToMilliseconds(string refreshRate)
        {
            if (string.IsNullOrEmpty(refreshRate))
                return 10000; // Default 10 seconds

            refreshRate = refreshRate.Trim().ToLowerInvariant();

            if (refreshRate.EndsWith("s"))
            {
                if (int.TryParse(refreshRate.Substring(0, refreshRate.Length - 1), out int seconds))
                    return seconds * 1000;
            }
            else if (refreshRate.EndsWith("min"))
            {
                if (int.TryParse(refreshRate.Substring(0, refreshRate.Length - 3), out int minutes))
                    return minutes * 60 * 1000;
            }

            Logger.Log($"TopicWritingManager: Unable to parse refresh rate '{refreshRate}', using default 10s", LogLevel.Warning);
            return 10000; // Default 10 seconds
        }

        /// <summary>
        /// Detects the data type of a topic value
        /// </summary>
        /// <param name="topicValue">The value from the MQTT topic</param>
        /// <returns>Detected data type as string</returns>
        private string DetectDataType(string topicValue)
        {
            if (string.IsNullOrEmpty(topicValue))
                return "unknown";

            // Try boolean
            if (bool.TryParse(topicValue, out _) || topicValue == "1" || topicValue == "0")
                return "bool";

            // Try integer
            if (int.TryParse(topicValue, out _))
                return "int";

            // Try float
            if (float.TryParse(topicValue, out _))
                return "float";

            // Default to string
            return "string";
        }

        /// <summary>
        /// Stops all active timers
        /// </summary>
        private void StopAllTimers()
        {
            foreach (var timer in _topicTimers.Values)
            {
                timer?.Stop();
                timer?.Dispose();
            }
            _topicTimers.Clear();

        }

        /// <summary>
        /// Gets the current topic configurations
        /// </summary>
        /// <returns>Dictionary of topic configurations</returns>
        public Dictionary<string, TopicWriteConfig> GetTopicConfigurations()
        {
            return new Dictionary<string, TopicWriteConfig>(_topicConfigs);
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                StopAllTimers();
                _disposed = true;
                Logger.Log("TopicWritingManager disposed", LogLevel.Info);
            }
        }
    }
}
