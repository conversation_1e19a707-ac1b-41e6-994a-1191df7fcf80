using prueba.Models;
using prueba.Services;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;

namespace prueba.ViewModels
{
    /// <summary>
    /// ViewModel for managing Modbus communications in the UI
    /// </summary>
    public class ModbusViewModel : INotifyPropertyChanged
    {
        #region Private Fields

        private ModbusService _modbusServiceLeft;
        private ModbusService _modbusServiceRight;
        private ModbusConfiguration _selectedConfigurationLeft;
        private ModbusConfiguration _selectedConfigurationRight;
        private ModbusConfigurationManager _configManager;
        private bool _isConnectedLeft;
        private bool _isConnectedRight;
        private string _connectionStatusLeft = "Disconnected";
        private string _connectionStatusRight = "Disconnected";
        private string _lastErrorLeft = string.Empty;
        private string _lastErrorRight = string.Empty;
        private bool _isBusyLeft;
        private bool _isBusyRight;
        private ushort _registerAddressLeft;
        private ushort _registerAddressRight;
        private ushort _registerValueLeft;
        private ushort _registerValueRight;
        private ushort _registerCountLeft = 1;
        private ushort _registerCountRight = 1;
        private bool _coilValueLeft;
        private bool _coilValueRight;
        private bool _isFloatFormatLeft = false;
        private bool _isFloatFormatRight = false;
        private string _originalAddressStringLeft = "400";
        private string _originalAddressStringRight = "400";
        private readonly SynchronizationContext _uiSyncContext;

        #endregion

        #region Properties

        /// <summary>
        /// Gets or sets the selected Modbus configuration for the Left instance
        /// </summary>
        public ModbusConfiguration SelectedConfigurationLeft
        {
            get => _selectedConfigurationLeft;
            set
            {
                if (SetProperty(ref _selectedConfigurationLeft, value))
                {
                    if (_isConnectedLeft && _modbusServiceLeft != null)
                    {
                        DisconnectCommandLeft.Execute(null);
                    }
                }
            }
        }

        /// <summary>
        /// Gets or sets the selected Modbus configuration for the Right instance
        /// </summary>
        public ModbusConfiguration SelectedConfigurationRight
        {
            get => _selectedConfigurationRight;
            set
            {
                if (SetProperty(ref _selectedConfigurationRight, value))
                {
                    if (_isConnectedRight && _modbusServiceRight != null)
                    {
                        DisconnectCommandRight.Execute(null);
                    }
                }
            }
        }

        /// <summary>
        /// Gets the collection of available configurations (shared)
        /// </summary>
        public ObservableCollection<ModbusConfiguration> AvailableConfigurations { get; } = new ObservableCollection<ModbusConfiguration>();

        /// <summary>
        /// Gets or sets a value indicating whether the Left Modbus service is connected
        /// </summary>
        public bool IsConnectedLeft
        {
            get => _isConnectedLeft;
            private set => SetProperty(ref _isConnectedLeft, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the Right Modbus service is connected
        /// </summary>
        public bool IsConnectedRight
        {
            get => _isConnectedRight;
            private set => SetProperty(ref _isConnectedRight, value);
        }

        /// <summary>
        /// Gets or sets the connection status text for the Left instance
        /// </summary>
        public string ConnectionStatusLeft
        {
            get => _connectionStatusLeft;
            private set => SetProperty(ref _connectionStatusLeft, value);
        }

        /// <summary>
        /// Gets or sets the connection status text for the Right instance
        /// </summary>
        public string ConnectionStatusRight
        {
            get => _connectionStatusRight;
            private set => SetProperty(ref _connectionStatusRight, value);
        }

        /// <summary>
        /// Gets or sets the last error message for the Left instance
        /// </summary>
        public string LastErrorLeft
        {
            get => _lastErrorLeft;
            private set => SetProperty(ref _lastErrorLeft, value);
        }

        /// <summary>
        /// Gets or sets the last error message for the Right instance
        /// </summary>
        public string LastErrorRight
        {
            get => _lastErrorRight;
            private set => SetProperty(ref _lastErrorRight, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether an operation is in progress for the Left instance
        /// </summary>
        public bool IsBusyLeft
        {
            get => _isBusyLeft;
            private set => SetProperty(ref _isBusyLeft, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether an operation is in progress for the Right instance
        /// </summary>
        public bool IsBusyRight
        {
            get => _isBusyRight;
            private set => SetProperty(ref _isBusyRight, value);
        }

        /// <summary>
        /// Gets or sets the register address for read/write operations for the Left instance
        /// </summary>
        public ushort RegisterAddressLeft
        {
            get => _registerAddressLeft;
            set => SetProperty(ref _registerAddressLeft, value);
        }

        /// <summary>
        /// Gets or sets the register address for read/write operations for the Right instance
        /// </summary>
        public ushort RegisterAddressRight
        {
            get => _registerAddressRight;
            set => SetProperty(ref _registerAddressRight, value);
        }

        /// <summary>
        /// Gets or sets the register value for write operations for the Left instance
        /// </summary>
        public ushort RegisterValueLeft
        {
            get => _registerValueLeft;
            set => SetProperty(ref _registerValueLeft, value);
        }

        /// <summary>
        /// Gets or sets the register value for write operations for the Right instance
        /// </summary>
        public ushort RegisterValueRight
        {
            get => _registerValueRight;
            set => SetProperty(ref _registerValueRight, value);
        }

        /// <summary>
        /// Gets or sets the number of registers to read for the Left instance
        /// </summary>
        public ushort RegisterCountLeft
        {
            get => _registerCountLeft;
            set => SetProperty(ref _registerCountLeft, Math.Max((ushort)1, value));
        }

        /// <summary>
        /// Gets or sets the number of registers to read for the Right instance
        /// </summary>
        public ushort RegisterCountRight
        {
            get => _registerCountRight;
            set => SetProperty(ref _registerCountRight, Math.Max((ushort)1, value));
        }

        /// <summary>
        /// Gets or sets the coil value for write operations for the Left instance
        /// </summary>
        public bool CoilValueLeft
        {
            get => _coilValueLeft;
            set => SetProperty(ref _coilValueLeft, value);
        }

        /// <summary>
        /// Gets or sets the coil value for write operations for the Right instance
        /// </summary>
        public bool CoilValueRight
        {
            get => _coilValueRight;
            set => SetProperty(ref _coilValueRight, value);
        }

        /// <summary>
        /// Gets or sets the read holding registers results for the Left instance
        /// </summary>
        public ObservableCollection<RegisterValue> ReadResultsLeft { get; } = new ObservableCollection<RegisterValue>();

        /// <summary>
        /// Gets or sets the read holding registers results for the Right instance
        /// </summary>
        public ObservableCollection<RegisterValue> ReadResultsRight { get; } = new ObservableCollection<RegisterValue>();

        /// <summary>
        /// Gets or sets whether the Left address is in MF{address} float format
        /// </summary>
        public bool IsFloatFormatLeft
        {
            get => _isFloatFormatLeft;
            set => SetProperty(ref _isFloatFormatLeft, value);
        }

        /// <summary>
        /// Gets or sets whether the Right address is in MF{address} float format
        /// </summary>
        public bool IsFloatFormatRight
        {
            get => _isFloatFormatRight;
            set => SetProperty(ref _isFloatFormatRight, value);
        }

        /// <summary>
        /// Gets or sets the original address string for the Left instance (e.g., "400" or "MF400")
        /// </summary>
        public string OriginalAddressStringLeft
        {
            get => _originalAddressStringLeft;
            set => SetProperty(ref _originalAddressStringLeft, value);
        }

        /// <summary>
        /// Gets or sets the original address string for the Right instance (e.g., "400" or "MF400")
        /// </summary>
        public string OriginalAddressStringRight
        {
            get => _originalAddressStringRight;
            set => SetProperty(ref _originalAddressStringRight, value);
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to connect to the Left Modbus device
        /// </summary>
        public ICommand ConnectCommandLeft { get; }

        /// <summary>
        /// Command to connect to the Right Modbus device
        /// </summary>
        public ICommand ConnectCommandRight { get; }

        /// <summary>
        /// Command to disconnect from the Left Modbus device
        /// </summary>
        public ICommand DisconnectCommandLeft { get; }

        /// <summary>
        /// Command to disconnect from the Right Modbus device
        /// </summary>
        public ICommand DisconnectCommandRight { get; }

        /// <summary>
        /// Command to save the current configuration
        /// </summary>
        public ICommand SaveConfigCommand { get; }

        /// <summary>
        /// Command to create a new configuration
        /// </summary>
        public ICommand NewConfigCommand { get; }

        /// <summary>
        /// Command to delete the selected configuration
        /// </summary>
        public ICommand DeleteConfigCommand { get; }

        /// <summary>
        /// Command to read holding registers for the Left instance
        /// </summary>
        public ICommand ReadHoldingRegistersCommandLeft { get; }

        /// <summary>
        /// Command to read holding registers for the Right instance
        /// </summary>
        public ICommand ReadHoldingRegistersCommandRight { get; }

        /// <summary>
        /// Command to read input registers for the Left instance
        /// </summary>
        public ICommand ReadInputRegistersCommandLeft { get; }

        /// <summary>
        /// Command to read input registers for the Right instance
        /// </summary>
        public ICommand ReadInputRegistersCommandRight { get; }

        /// <summary>
        /// Command to read coils for the Left instance
        /// </summary>
        public ICommand ReadCoilsCommandLeft { get; }

        /// <summary>
        /// Command to read coils for the Right instance
        /// </summary>
        public ICommand ReadCoilsCommandRight { get; }

        /// <summary>
        /// Command to read discrete inputs for the Left instance
        /// </summary>
        public ICommand ReadDiscreteInputsCommandLeft { get; }

        /// <summary>
        /// Command to read discrete inputs for the Right instance
        /// </summary>
        public ICommand ReadDiscreteInputsCommandRight { get; }

        /// <summary>
        /// Command to write a single register for the Left instance
        /// </summary>
        public ICommand WriteSingleRegisterCommandLeft { get; }

        /// <summary>
        /// Command to write a single register for the Right instance
        /// </summary>
        public ICommand WriteSingleRegisterCommandRight { get; }

        /// <summary>
        /// Command to write a single coil for the Left instance
        /// </summary>
        public ICommand WriteSingleCoilCommandLeft { get; }

        /// <summary>
        /// Command to write a single coil for the Right instance
        /// </summary>
        public ICommand WriteSingleCoilCommandRight { get; }

        /// <summary>
        /// Command to test reading register 400 for the Left instance (like Python client)
        /// </summary>
        public ICommand TestRegister400CommandLeft { get; }

        /// <summary>
        /// Command to test reading register 400 for the Right instance (like Python client)
        /// </summary>
        public ICommand TestRegister400CommandRight { get; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the ModbusViewModel class
        /// </summary>
        /// <param name="configDirectory">Directory for configuration files</param>
        public ModbusViewModel(string configDirectory = null)
        {
            _uiSyncContext = SynchronizationContext.Current;
            Logger.Log("ModbusViewModel: Initializing.", LogLevel.Debug);
            // Initialize the configuration manager
            _configManager = new ModbusConfigurationManager(configDirectory ?? ModbusConfigurationManager.DEFAULT_CONFIG_DIRECTORY);

            // Initialize commands
            ConnectCommandLeft = new RelayCommand(ConnectLeft, CanConnectLeft);
            ConnectCommandRight = new RelayCommand(ConnectRight, CanConnectRight);
            DisconnectCommandLeft = new RelayCommand(DisconnectLeft, CanDisconnectLeft);
            DisconnectCommandRight = new RelayCommand(DisconnectRight, CanDisconnectRight);
            SaveConfigCommand = new RelayCommand(SaveConfig, CanSaveConfig);
            NewConfigCommand = new RelayCommand(NewConfig);
            DeleteConfigCommand = new RelayCommand(DeleteConfig, CanDeleteConfig);

            ReadHoldingRegistersCommandLeft = new RelayCommand(ReadHoldingRegistersLeft, CanExecuteReadCommandLeft);
            ReadInputRegistersCommandLeft = new RelayCommand(ReadInputRegistersLeft, CanExecuteReadCommandLeft);
            ReadCoilsCommandLeft = new RelayCommand(ReadCoilsLeft, CanExecuteReadCommandLeft);
            ReadDiscreteInputsCommandLeft = new RelayCommand(ReadDiscreteInputsLeft, CanExecuteReadCommandLeft);

            ReadHoldingRegistersCommandRight = new RelayCommand(ReadHoldingRegistersRight, CanExecuteReadCommandRight);
            ReadInputRegistersCommandRight = new RelayCommand(ReadInputRegistersRight, CanExecuteReadCommandRight);
            ReadCoilsCommandRight = new RelayCommand(ReadCoilsRight, CanExecuteReadCommandRight);
            ReadDiscreteInputsCommandRight = new RelayCommand(ReadDiscreteInputsRight, CanExecuteReadCommandRight);

            WriteSingleRegisterCommandLeft = new RelayCommand(WriteSingleRegisterLeft, CanExecuteWriteCommandLeft);
            WriteSingleRegisterCommandRight = new RelayCommand(WriteSingleRegisterRight, CanExecuteWriteCommandRight);
            WriteSingleCoilCommandLeft = new RelayCommand(WriteSingleCoilLeft, CanExecuteWriteCommandLeft);
            WriteSingleCoilCommandRight = new RelayCommand(WriteSingleCoilRight, CanExecuteWriteCommandRight);

            // Load configurations
            LoadConfigurations();
            Logger.Log("ModbusViewModel: Initialization complete.", LogLevel.Info);
        }

        #endregion

        #region Methods

        /// <summary>
        /// Loads all available configurations
        /// </summary>
        /// <param name="preserveCurrentSelections">If true, preserves current left/right selections when reloading configurations</param>
        public void LoadConfigurations(bool preserveCurrentSelections = false)
        {
            Logger.Log($"ModbusViewModel: Loading configurations. Preserve selections: {preserveCurrentSelections}", LogLevel.Debug);

            // Store current selections if we need to preserve them
            ModbusConfiguration currentLeft = preserveCurrentSelections ? SelectedConfigurationLeft : null;
            ModbusConfiguration currentRight = preserveCurrentSelections ? SelectedConfigurationRight : null;
            string currentLeftName = currentLeft?.Name;
            string currentRightName = currentRight?.Name;

            _configManager.LoadAllConfigurations();

            AvailableConfigurations.Clear();
            foreach (var config in _configManager.Configurations)
            {
                AvailableConfigurations.Add(config);
            }

            if (preserveCurrentSelections && AvailableConfigurations.Any())
            {
                // Try to restore previous selections by name
                if (!string.IsNullOrEmpty(currentLeftName))
                {
                    var restoredLeft = AvailableConfigurations.FirstOrDefault(c => c.Name.Equals(currentLeftName, StringComparison.OrdinalIgnoreCase));
                    SelectedConfigurationLeft = restoredLeft ?? AvailableConfigurations.FirstOrDefault();
                    Logger.Log($"ModbusViewModel: Restored left selection to '{SelectedConfigurationLeft?.Name ?? "None"}' (was '{currentLeftName}')", LogLevel.Debug);
                }
                else
                {
                    SelectedConfigurationLeft = AvailableConfigurations.FirstOrDefault();
                }

                if (!string.IsNullOrEmpty(currentRightName))
                {
                    var restoredRight = AvailableConfigurations.FirstOrDefault(c => c.Name.Equals(currentRightName, StringComparison.OrdinalIgnoreCase));
                    SelectedConfigurationRight = restoredRight ?? AvailableConfigurations.FirstOrDefault();
                    Logger.Log($"ModbusViewModel: Restored right selection to '{SelectedConfigurationRight?.Name ?? "None"}' (was '{currentRightName}')", LogLevel.Debug);
                }
                else
                {
                    SelectedConfigurationRight = AvailableConfigurations.FirstOrDefault();
                }
            }
            else
            {
                // Select the first configuration if available (original behavior)
                SelectedConfigurationLeft = AvailableConfigurations.FirstOrDefault();
                SelectedConfigurationRight = AvailableConfigurations.FirstOrDefault();
            }

            Logger.Log($"ModbusViewModel: Configurations loaded. Count: {AvailableConfigurations.Count}. Selected Left: {SelectedConfigurationLeft?.Name ?? "None"}, Selected Right: {SelectedConfigurationRight?.Name ?? "None"}", LogLevel.Info);
        }

        /// <summary>
        /// Gets the left Modbus service instance
        /// </summary>
        /// <returns>ModbusService instance for left panel, or null if not connected</returns>
        public ModbusService GetModbusServiceLeft()
        {
            return _modbusServiceLeft;
        }

        /// <summary>
        /// Gets the right Modbus service instance
        /// </summary>
        /// <returns>ModbusService instance for right panel, or null if not connected</returns>
        public ModbusService GetModbusServiceRight()
        {
            return _modbusServiceRight;
        }

        private bool CanConnectLeft()
        {
            return SelectedConfigurationLeft != null && !IsConnectedLeft && !IsBusyLeft;
        }

        private async void ConnectLeft()
        {
            if (SelectedConfigurationLeft == null)
            {
                LastErrorLeft = "No configuration selected.";
                Logger.Log("ModbusViewModel: Connect attempt failed. No configuration selected.", LogLevel.Warning);
                return;
            }

            Logger.Log($"ModbusViewModel: Attempting to connect with configuration '{SelectedConfigurationLeft.Name}'.", LogLevel.Info);

            // Update status indicator to show connecting state
            if (System.Windows.Forms.Application.OpenForms.OfType<Form1>().FirstOrDefault() is Form1 form)
            {
                form.UpdateModbusConnectingStatus("Modbus: Conectando...");
            }

            await ExecuteWithBusyStateLeft(async () =>
            {
                LastErrorLeft = string.Empty;
                ConnectionStatusLeft = $"Connecting to {SelectedConfigurationLeft.Name}...";

                _modbusServiceLeft = SelectedConfigurationLeft.CreateService();
                _modbusServiceLeft.ConnectionStatusChanged += OnConnectionStatusChangedLeft;
                _modbusServiceLeft.CommunicationError += OnCommunicationErrorLeft;
                _modbusServiceLeft.DataReceived += OnDataReceivedLeft;

                bool success = await _modbusServiceLeft.ConnectAsync();

                if (success)
                {
                    IsConnectedLeft = true;
                    ConnectionStatusLeft = $"Connected to {SelectedConfigurationLeft.Name}";
                    Logger.Log($"ModbusViewModel: Successfully connected to '{SelectedConfigurationLeft.Name}'.", LogLevel.Info);
                }
                else
                {
                    IsConnectedLeft = false;
                    ConnectionStatusLeft = "Connection Failed";
                    LastErrorLeft = _modbusServiceLeft.IsConnected ? "Connected but status update failed." : "Failed to establish connection."; // More specific error if possible
                    Logger.Log($"ModbusViewModel: Connection to '{SelectedConfigurationLeft.Name}' failed. LastError: {LastErrorLeft}", LogLevel.Error);
                    // Clean up if connection failed outright
                    if (_modbusServiceLeft != null)
                    {
                        _modbusServiceLeft.ConnectionStatusChanged -= OnConnectionStatusChangedLeft;
                        _modbusServiceLeft.CommunicationError -= OnCommunicationErrorLeft;
                        _modbusServiceLeft.DataReceived -= OnDataReceivedLeft;
                        _modbusServiceLeft.Dispose();
                        _modbusServiceLeft = null;
                    }
                }
            });
            // Explicitly call RaiseCanExecuteChanged for relevant commands
            ((RelayCommand)ConnectCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)DisconnectCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)ReadHoldingRegistersCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)ReadInputRegistersCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)ReadCoilsCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)ReadDiscreteInputsCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)WriteSingleRegisterCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)WriteSingleCoilCommandLeft).RaiseCanExecuteChanged();
        }

        private bool CanConnectRight()
        {
            return SelectedConfigurationRight != null && !IsConnectedRight && !IsBusyRight;
        }

        private async void ConnectRight()
        {
            if (SelectedConfigurationRight == null)
            {
                LastErrorRight = "No configuration selected.";
                Logger.Log("ModbusViewModel: Connect attempt failed. No configuration selected.", LogLevel.Warning);
                return;
            }

            Logger.Log($"ModbusViewModel: Attempting to connect with configuration '{SelectedConfigurationRight.Name}'.", LogLevel.Info);

            // Update status indicator to show connecting state
            if (System.Windows.Forms.Application.OpenForms.OfType<Form1>().FirstOrDefault() is Form1 form)
            {
                form.UpdateModbusConnectingStatus("Modbus: Conectando...");
            }

            await ExecuteWithBusyStateRight(async () =>
            {
                LastErrorRight = string.Empty;
                ConnectionStatusRight = $"Connecting to {SelectedConfigurationRight.Name}...";

                _modbusServiceRight = SelectedConfigurationRight.CreateService();
                _modbusServiceRight.ConnectionStatusChanged += OnConnectionStatusChangedRight;
                _modbusServiceRight.CommunicationError += OnCommunicationErrorRight;
                _modbusServiceRight.DataReceived += OnDataReceivedRight;

                bool success = await _modbusServiceRight.ConnectAsync();

                if (success)
                {
                    IsConnectedRight = true;
                    ConnectionStatusRight = $"Connected to {SelectedConfigurationRight.Name}";
                    Logger.Log($"ModbusViewModel: Successfully connected to '{SelectedConfigurationRight.Name}'.", LogLevel.Info);
                }
                else
                {
                    IsConnectedRight = false;
                    ConnectionStatusRight = "Connection Failed";
                    LastErrorRight = _modbusServiceRight.IsConnected ? "Connected but status update failed." : "Failed to establish connection."; // More specific error if possible
                    Logger.Log($"ModbusViewModel: Connection to '{SelectedConfigurationRight.Name}' failed. LastError: {LastErrorRight}", LogLevel.Error);
                    // Clean up if connection failed outright
                    if (_modbusServiceRight != null)
                    {
                        _modbusServiceRight.ConnectionStatusChanged -= OnConnectionStatusChangedRight;
                        _modbusServiceRight.CommunicationError -= OnCommunicationErrorRight;
                        _modbusServiceRight.DataReceived -= OnDataReceivedRight;
                        _modbusServiceRight.Dispose();
                        _modbusServiceRight = null;
                    }
                }
            });
            // Explicitly call RaiseCanExecuteChanged for relevant commands
            ((RelayCommand)ConnectCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)DisconnectCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)ReadHoldingRegistersCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)ReadInputRegistersCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)ReadCoilsCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)ReadDiscreteInputsCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)WriteSingleRegisterCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)WriteSingleCoilCommandRight).RaiseCanExecuteChanged();
        }

        private bool CanDisconnectLeft()
        {
            bool canDisconnect = IsConnectedLeft && !IsBusyLeft;
            // Logger.Log($"ModbusViewModel: CanDisconnect() evaluated. IsConnected: {IsConnected}, IsBusy: {IsBusy}, Result: {canDisconnect}", LogLevel.Debug);
            return canDisconnect;
        }

        private async void DisconnectLeft()
        {
            Logger.Log($"ModbusViewModel: Disconnect() called. Current IsConnected: {IsConnectedLeft}, IsBusy: {IsBusyLeft}", LogLevel.Info);
            await ExecuteWithBusyStateLeft(async () =>
            {
                if (_modbusServiceLeft != null)
                {
                    Logger.Log("ModbusViewModel: ModbusService found. Calling _modbusService.DisconnectAsync().", LogLevel.Info);
                    await _modbusServiceLeft.DisconnectAsync();
                    Logger.Log("ModbusViewModel: _modbusService.DisconnectAsync() returned.", LogLevel.Info);

                    // IsConnected will be set by OnConnectionStatusChanged event
                    // However, ensure cleanup if event doesn't fire or service is disposed prematurely
                    if (IsConnectedLeft) // If status didn't change via event
                    {
                        IsConnectedLeft = false;
                        ConnectionStatusLeft = "Disconnected (Manually Set)";
                        Logger.Log("ModbusViewModel: IsConnected was still true after service disconnect. Manually set to false.", LogLevel.Warning);
                    }
                    else
                    {
                        Logger.Log("ModbusViewModel: IsConnected was already false after service disconnect (likely updated by event).", LogLevel.Info);
                    }

                    _modbusServiceLeft.ConnectionStatusChanged -= OnConnectionStatusChangedLeft;
                    _modbusServiceLeft.CommunicationError -= OnCommunicationErrorLeft;
                    _modbusServiceLeft.DataReceived -= OnDataReceivedLeft;
                    _modbusServiceLeft.Dispose();
                    _modbusServiceLeft = null;
                    Logger.Log("ModbusViewModel: ModbusService event handlers unsubscribed and service disposed.", LogLevel.Debug);
                }
                else
                {
                    // Fallback if service is null but we thought we were connected
                    IsConnectedLeft = false;
                    ConnectionStatusLeft = "Disconnected (Service Null)";
                    Logger.Log("ModbusViewModel: Disconnect called but ModbusService was null. Status set to Disconnected.", LogLevel.Warning);
                }
            });
            Logger.Log($"ModbusViewModel: Disconnect() finished. IsConnected: {IsConnectedLeft}, ConnectionStatus: '{ConnectionStatusLeft}'", LogLevel.Info);
            // Explicitly call RaiseCanExecuteChanged for relevant commands
            ((RelayCommand)ConnectCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)DisconnectCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)ReadHoldingRegistersCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)ReadInputRegistersCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)ReadCoilsCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)ReadDiscreteInputsCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)WriteSingleRegisterCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)WriteSingleCoilCommandLeft).RaiseCanExecuteChanged();
        }

        private bool CanDisconnectRight()
        {
            bool canDisconnect = IsConnectedRight && !IsBusyRight;
            // Logger.Log($"ModbusViewModel: CanDisconnect() evaluated. IsConnected: {IsConnected}, IsBusy: {IsBusy}, Result: {canDisconnect}", LogLevel.Debug);
            return canDisconnect;
        }

        private async void DisconnectRight()
        {
            Logger.Log($"ModbusViewModel: Disconnect() called. Current IsConnected: {IsConnectedRight}, IsBusy: {IsBusyRight}", LogLevel.Info);
            await ExecuteWithBusyStateRight(async () =>
            {
                if (_modbusServiceRight != null)
                {
                    Logger.Log("ModbusViewModel: ModbusService found. Calling _modbusService.DisconnectAsync().", LogLevel.Debug);
                    await _modbusServiceRight.DisconnectAsync();
                    Logger.Log("ModbusViewModel: _modbusService.DisconnectAsync() returned.", LogLevel.Debug);

                    // IsConnected will be set by OnConnectionStatusChanged event
                    // However, ensure cleanup if event doesn't fire or service is disposed prematurely
                    if (IsConnectedRight) // If status didn't change via event
                    {
                        IsConnectedRight = false;
                        ConnectionStatusRight = "Disconnected (Manually Set)";
                        Logger.Log("ModbusViewModel: IsConnected was still true after service disconnect. Manually set to false.", LogLevel.Warning);
                    }
                    else
                    {
                        Logger.Log("ModbusViewModel: IsConnected was already false after service disconnect (likely updated by event).", LogLevel.Debug);
                    }

                    _modbusServiceRight.ConnectionStatusChanged -= OnConnectionStatusChangedRight;
                    _modbusServiceRight.CommunicationError -= OnCommunicationErrorRight;
                    _modbusServiceRight.DataReceived -= OnDataReceivedRight;
                    _modbusServiceRight.Dispose();
                    _modbusServiceRight = null;
                    Logger.Log("ModbusViewModel: ModbusService event handlers unsubscribed and service disposed.", LogLevel.Debug);
                }
                else
                {
                    // Fallback if service is null but we thought we were connected
                    IsConnectedRight = false;
                    ConnectionStatusRight = "Disconnected (Service Null)";
                    Logger.Log("ModbusViewModel: Disconnect called but ModbusService was null. Status set to Disconnected.", LogLevel.Warning);
                }
            });
            Logger.Log($"ModbusViewModel: Disconnect() finished. IsConnected: {IsConnectedRight}, ConnectionStatus: '{ConnectionStatusRight}'", LogLevel.Info);
            // Explicitly call RaiseCanExecuteChanged for relevant commands
            ((RelayCommand)ConnectCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)DisconnectCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)ReadHoldingRegistersCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)ReadInputRegistersCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)ReadCoilsCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)ReadDiscreteInputsCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)WriteSingleRegisterCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)WriteSingleCoilCommandRight).RaiseCanExecuteChanged();
        }

        private bool CanSaveConfig()
        {
            return SelectedConfigurationLeft != null && !string.IsNullOrWhiteSpace(SelectedConfigurationLeft.Name) && !IsBusyLeft &&
                   SelectedConfigurationRight != null && !string.IsNullOrWhiteSpace(SelectedConfigurationRight.Name) && !IsBusyRight;
        }

        private void SaveConfig()
        {
            if (SelectedConfigurationLeft == null || string.IsNullOrWhiteSpace(SelectedConfigurationLeft.Name) ||
                SelectedConfigurationRight == null || string.IsNullOrWhiteSpace(SelectedConfigurationRight.Name))
            {
                LastErrorLeft = "Configuration name cannot be empty.";
                LastErrorRight = "Configuration name cannot be empty.";
                Logger.Log("ModbusViewModel: SaveConfig failed. Configuration name empty or config is null.", LogLevel.Warning);
                return;
            }
            Logger.Log($"ModbusViewModel: Saving configuration '{SelectedConfigurationLeft.Name}' and '{SelectedConfigurationRight.Name}'.", LogLevel.Info);
            _configManager.SaveConfiguration(SelectedConfigurationLeft);
            _configManager.SaveConfiguration(SelectedConfigurationRight);
            // Refresh list in case it was a new name or to ensure consistency
            LoadConfigurations(preserveCurrentSelections: true);
            LastErrorLeft = "Configuration saved.";
            LastErrorRight = "Configuration saved.";
            Logger.Log($"ModbusViewModel: Configurations '{SelectedConfigurationLeft.Name}' and '{SelectedConfigurationRight.Name}' saved successfully.", LogLevel.Info);
        }

        /// <summary>
        /// Creates a new, empty configuration and selects it for both instances
        /// </summary>
        private void NewConfig()
        {
            Logger.Log("ModbusViewModel: Creating new configuration.", LogLevel.Debug);
            // Create one new config, can be assigned to either or both
            var newConfig = new ModbusConfiguration { Name = "New Configuration" };
            AvailableConfigurations.Add(newConfig);
            SelectedConfigurationLeft = newConfig; // Or allow selecting for one specifically
            SelectedConfigurationRight = newConfig; // Or allow selecting for one specifically
            LastErrorLeft = "New configuration created. Edit and save.";
            LastErrorRight = "New configuration created. Edit and save.";
            Logger.Log("ModbusViewModel: New configuration created and selected.", LogLevel.Debug);
        }

        /// <summary>
        /// Checks if a configuration can be deleted.
        /// </summary>
        /// <returns>True if a configuration is selected and it's not the last one.</returns>
        private bool CanDeleteConfig()
        {
            // Allow deleting if either is selected and it's not the absolute last config
            bool canDeleteLeft = SelectedConfigurationLeft != null && !IsBusyLeft;
            bool canDeleteRight = SelectedConfigurationRight != null && !IsBusyRight;
            return (canDeleteLeft || canDeleteRight) && AvailableConfigurations.Count > 1;
        }

        /// <summary>
        /// Deletes the selected configuration(s).
        /// If both Left and Right point to the same config, it's deleted.
        /// If they point to different configs, this logic might need refinement
        /// (e.g., a way to select which one to delete or delete both).
        /// Current logic: Deletes SelectedConfigurationLeft if it exists.
        /// Consider if SelectedConfigurationRight should also be deleted if different.
        /// </summary>
        private void DeleteConfig()
        {
            // This logic might need to be more specific if Left and Right can have different configs selected
            // and the user wants to delete only one. For now, assume we delete the config selected in Left,
            // and if Right uses the same, it will also be effectively gone.
            ModbusConfiguration configToDelete = SelectedConfigurationLeft ?? SelectedConfigurationRight;

            if (configToDelete == null)
            {
                Logger.Log("ModbusViewModel: DeleteConfig failed. No configuration selected for either instance.", LogLevel.Warning);
                return;
            }
            if (AvailableConfigurations.Count <= 1)
            {
                LastErrorLeft = "Cannot delete the last configuration.";
                LastErrorRight = "Cannot delete the last configuration.";
                Logger.Log("ModbusViewModel: DeleteConfig failed. Cannot delete the last configuration.", LogLevel.Warning);
                return;
            }

            string configNameToDelete = configToDelete.Name;
            Logger.Log($"ModbusViewModel: Deleting configuration '{configNameToDelete}'.", LogLevel.Info);
            _configManager.DeleteConfiguration(configToDelete);
            LoadConfigurations(preserveCurrentSelections: false); // Refresh the list, don't preserve since we're deleting

            // Clear selection if the deleted config was selected
            if (SelectedConfigurationLeft == configToDelete) SelectedConfigurationLeft = AvailableConfigurations.FirstOrDefault();
            if (SelectedConfigurationRight == configToDelete) SelectedConfigurationRight = AvailableConfigurations.FirstOrDefault();

            LastErrorLeft = $"Configuration '{configNameToDelete}' deleted.";
            LastErrorRight = $"Configuration '{configNameToDelete}' deleted."; // Or clear if only one was deleted
            Logger.Log($"ModbusViewModel: Configuration '{configNameToDelete}' deleted successfully.", LogLevel.Info);
        }

        private bool CanExecuteReadCommandLeft()
        {
            return IsConnectedLeft && !IsBusyLeft && RegisterCountLeft > 0 && RegisterCountLeft <= 125;
        }

        private bool CanExecuteReadCommandRight()
        {
            return IsConnectedRight && !IsBusyRight && RegisterCountRight > 0 && RegisterCountRight <= 125;
        }

        private bool CanExecuteWriteCommandLeft()
        {
            return IsConnectedLeft && !IsBusyLeft;
        }

        private bool CanExecuteWriteCommandRight()
        {
            return IsConnectedRight && !IsBusyRight;
        }

        private async void ReadHoldingRegistersLeft()
        {
            Logger.Log($"ModbusViewModel: Executing ReadHoldingRegistersLeft. Address: {RegisterAddressLeft}, Count: {RegisterCountLeft}", LogLevel.Debug);
            await ReadHoldingRegistersAsyncLeft();
        }

        private async void ReadInputRegistersLeft()
        {
            Logger.Log($"ModbusViewModel: Executing ReadInputRegistersLeft. Address: {RegisterAddressLeft}, Count: {RegisterCountLeft}", LogLevel.Debug);
            await ExecuteWithBusyStateLeft(async () =>
            {
                LastErrorLeft = string.Empty;
                var values = await _modbusServiceLeft.ReadInputRegistersAsync(RegisterAddressLeft, RegisterCountLeft);
                if (values != null)
                {
                    DisplayRegisterResultsLeft(values, RegisterAddressLeft, ModbusService.ModbusFunctionCode.ReadInputRegisters);
                    Logger.Log($"ModbusViewModel: Successfully read {values.Length} Input Registers from address {RegisterAddressLeft} for Left instance.", LogLevel.Info);
                }
                else
                {
                    Logger.Log($"ModbusViewModel: Failed to read Input Registers from address {RegisterAddressLeft} for Left instance.", LogLevel.Warning);
                }
            });
        }

        private async void ReadCoilsLeft()
        {
            Logger.Log($"ModbusViewModel: Executing ReadCoilsLeft. Address: {RegisterAddressLeft}, Count: {RegisterCountLeft}", LogLevel.Debug);
            await ReadCoilsAsyncLeft();
        }

        private async void ReadDiscreteInputsLeft()
        {

            await ExecuteWithBusyStateLeft(async () =>
            {
                LastErrorLeft = string.Empty;
                var values = await _modbusServiceLeft.ReadDiscreteInputsAsync(RegisterAddressLeft, RegisterCountLeft);
                if (values != null)
                {
                    DisplayBooleanResultsLeft(values, RegisterAddressLeft, ModbusService.ModbusFunctionCode.ReadDiscreteInputs);
                    Logger.Log($"ModbusViewModel: Successfully read {values.Length} Discrete Inputs from address {RegisterAddressLeft} for Left instance.", LogLevel.Info);
                }
                else
                {
                    Logger.Log($"ModbusViewModel: Failed to read Discrete Inputs from address {RegisterAddressLeft} for Left instance.", LogLevel.Warning);
                }
            });
        }

        private async void WriteSingleRegisterLeft()
        {
            Logger.Log($"ModbusViewModel: Executing WriteSingleRegisterLeft. Address: {RegisterAddressLeft}, Value: {RegisterValueLeft}", LogLevel.Debug);
            await ExecuteWithBusyStateLeft(async () =>
            {
                LastErrorLeft = string.Empty;
                bool success = await _modbusServiceLeft.WriteSingleRegisterAsync(RegisterAddressLeft, RegisterValueLeft);
                if (success)
                {
                    LastErrorLeft = $"Register {RegisterAddressLeft} written with value {RegisterValueLeft}.";
                    Logger.Log($"ModbusViewModel: Successfully wrote Single Register. Address: {RegisterAddressLeft}, Value: {RegisterValueLeft} for Left instance.", LogLevel.Info);
                }
                else
                {
                    LastErrorLeft = $"Failed to write register {RegisterAddressLeft}.";
                    Logger.Log($"ModbusViewModel: Failed to write Single Register. Address: {RegisterAddressLeft}, Value: {RegisterValueLeft} for Left instance.", LogLevel.Warning);
                }
            });
        }

        private async void WriteSingleCoilLeft()
        {
            Logger.Log($"ModbusViewModel: Executing WriteSingleCoilLeft. Address: {RegisterAddressLeft}, Value: {CoilValueLeft}", LogLevel.Debug);
            await ExecuteWithBusyStateLeft(async () =>
            {
                LastErrorLeft = string.Empty;
                bool success = await _modbusServiceLeft.WriteSingleCoilAsync(RegisterAddressLeft, CoilValueLeft);
                if (success)
                {
                    LastErrorLeft = $"Coil {RegisterAddressLeft} written with value {CoilValueLeft}.";
                    Logger.Log($"ModbusViewModel: Successfully wrote Single Coil. Address: {RegisterAddressLeft}, Value: {CoilValueLeft} for Left instance.", LogLevel.Info);
                }
                else
                {
                    LastErrorLeft = $"Failed to write coil {RegisterAddressLeft}.";
                    Logger.Log($"ModbusViewModel: Failed to write Single Coil. Address: {RegisterAddressLeft}, Value: {CoilValueLeft} for Left instance.", LogLevel.Warning);
                }
            });
        }

        private async void ReadHoldingRegistersRight()
        {
            Logger.Log($"ModbusViewModel: Executing ReadHoldingRegistersRight. Address: {RegisterAddressRight}, Count: {RegisterCountRight}", LogLevel.Debug);
            await ReadHoldingRegistersAsyncRight();
        }

        private async void ReadInputRegistersRight()
        {
            Logger.Log($"ModbusViewModel: Executing ReadInputRegistersRight. Address: {RegisterAddressRight}, Count: {RegisterCountRight}", LogLevel.Debug);
            await ExecuteWithBusyStateRight(async () =>
            {
                LastErrorRight = string.Empty;
                var values = await _modbusServiceRight.ReadInputRegistersAsync(RegisterAddressRight, RegisterCountRight);
                if (values != null)
                {
                    DisplayRegisterResultsRight(values, RegisterAddressRight, ModbusService.ModbusFunctionCode.ReadInputRegisters);
                    Logger.Log($"ModbusViewModel: Successfully read {values.Length} Input Registers from address {RegisterAddressRight} for Right instance.", LogLevel.Info);
                }
                else
                {
                    Logger.Log($"ModbusViewModel: Failed to read Input Registers from address {RegisterAddressRight} for Right instance.", LogLevel.Warning);
                }
            });
        }

        private async void ReadCoilsRight()
        {
            Logger.Log($"ModbusViewModel: Executing ReadCoilsRight. Address: {RegisterAddressRight}, Count: {RegisterCountRight}", LogLevel.Debug);
            await ReadCoilsAsyncRight();
        }

        private async void ReadDiscreteInputsRight()
        {
            Logger.Log($"ModbusViewModel: Executing ReadDiscreteInputsRight. Address: {RegisterAddressRight}, Count: {RegisterCountRight}", LogLevel.Debug);
            await ExecuteWithBusyStateRight(async () =>
            {
                LastErrorRight = string.Empty;
                var values = await _modbusServiceRight.ReadDiscreteInputsAsync(RegisterAddressRight, RegisterCountRight);
                if (values != null)
                {
                    DisplayBooleanResultsRight(values, RegisterAddressRight, ModbusService.ModbusFunctionCode.ReadDiscreteInputs);
                    Logger.Log($"ModbusViewModel: Successfully read {values.Length} Discrete Inputs from address {RegisterAddressRight} for Right instance.", LogLevel.Info);
                }
                else
                {
                    Logger.Log($"ModbusViewModel: Failed to read Discrete Inputs from address {RegisterAddressRight} for Right instance.", LogLevel.Warning);
                }
            });
        }

        private async void WriteSingleRegisterRight()
        {
            Logger.Log($"ModbusViewModel: Executing WriteSingleRegisterRight. Address: {RegisterAddressRight}, Value: {RegisterValueRight}", LogLevel.Debug);
            await ExecuteWithBusyStateRight(async () =>
            {
                LastErrorRight = string.Empty;
                bool success = await _modbusServiceRight.WriteSingleRegisterAsync(RegisterAddressRight, RegisterValueRight);
                if (success)
                {
                    LastErrorRight = $"Register {RegisterAddressRight} written with value {RegisterValueRight}.";
                    Logger.Log($"ModbusViewModel: Successfully wrote Single Register. Address: {RegisterAddressRight}, Value: {RegisterValueRight} for Right instance.", LogLevel.Info);
                }
                else
                {
                    LastErrorRight = $"Failed to write register {RegisterAddressRight}.";
                    Logger.Log($"ModbusViewModel: Failed to write Single Register. Address: {RegisterAddressRight}, Value: {RegisterValueRight} for Right instance.", LogLevel.Warning);
                }
            });
        }

        private async void WriteSingleCoilRight()
        {
            Logger.Log($"ModbusViewModel: Executing WriteSingleCoilRight. Address: {RegisterAddressRight}, Value: {CoilValueRight}", LogLevel.Debug);
            await ExecuteWithBusyStateRight(async () =>
            {
                LastErrorRight = string.Empty;
                bool success = await _modbusServiceRight.WriteSingleCoilAsync(RegisterAddressRight, CoilValueRight);
                if (success)
                {
                    LastErrorRight = $"Coil {RegisterAddressRight} written with value {CoilValueRight}.";
                    Logger.Log($"ModbusViewModel: Successfully wrote Single Coil. Address: {RegisterAddressRight}, Value: {CoilValueRight} for Right instance.", LogLevel.Info);
                }
                else
                {
                    LastErrorRight = $"Failed to write coil {RegisterAddressRight}.";
                    Logger.Log($"ModbusViewModel: Failed to write Single Coil. Address: {RegisterAddressRight}, Value: {CoilValueRight} for Right instance.", LogLevel.Warning);
                }
            });
        }

        #endregion

        #region Async Read Helpers

        private async Task ReadHoldingRegistersAsyncLeft()
        {
            Logger.Log($"ModbusViewModel: Internal ReadHoldingRegistersAsync. Address: {RegisterAddressLeft}, Count: {RegisterCountLeft}", LogLevel.Debug);
            await ExecuteWithBusyStateLeft(async () =>
            {
                LastErrorLeft = string.Empty;
                var values = await _modbusServiceLeft.ReadHoldingRegistersAsync(RegisterAddressLeft, RegisterCountLeft);
                if (values != null)
                {
                    DisplayRegisterResultsLeft(values, RegisterAddressLeft, ModbusService.ModbusFunctionCode.ReadHoldingRegisters);
                    Logger.Log($"ModbusViewModel: Successfully read {values.Length} Holding Registers from address {RegisterAddressLeft}.", LogLevel.Info);
                }
                else
                {
                    Logger.Log($"ModbusViewModel: Failed to read Holding Registers from address {RegisterAddressLeft}.", LogLevel.Warning);
                }
            });
        }

        private async Task ReadCoilsAsyncLeft()
        {

            await ExecuteWithBusyStateLeft(async () =>
            {
                LastErrorLeft = string.Empty;
                var values = await _modbusServiceLeft.ReadCoilsAsync(RegisterAddressLeft, RegisterCountLeft);
                if (values != null)
                {
                    DisplayBooleanResultsLeft(values, RegisterAddressLeft, ModbusService.ModbusFunctionCode.ReadCoils);
                    Logger.Log($"ModbusViewModel: Successfully read {values.Length} Coils from address {RegisterAddressLeft}.", LogLevel.Info);
                }
                else
                {
                    Logger.Log($"ModbusViewModel: Failed to read Coils from address {RegisterAddressLeft}.", LogLevel.Warning);
                }
            });
        }

        private async Task ReadHoldingRegistersAsyncRight()
        {
            Logger.Log($"ModbusViewModel: Internal ReadHoldingRegistersAsync. Address: {RegisterAddressRight}, Count: {RegisterCountRight}", LogLevel.Debug);
            await ExecuteWithBusyStateRight(async () =>
            {
                LastErrorRight = string.Empty;
                var values = await _modbusServiceRight.ReadHoldingRegistersAsync(RegisterAddressRight, RegisterCountRight);
                if (values != null)
                {
                    DisplayRegisterResultsRight(values, RegisterAddressRight, ModbusService.ModbusFunctionCode.ReadHoldingRegisters);
                    Logger.Log($"ModbusViewModel: Successfully read {values.Length} Holding Registers from address {RegisterAddressRight}.", LogLevel.Info);
                }
                else
                {
                    Logger.Log($"ModbusViewModel: Failed to read Holding Registers from address {RegisterAddressRight}.", LogLevel.Warning);
                }
            });
        }

        private async Task ReadCoilsAsyncRight()
        {
            Logger.Log($"ModbusViewModel: Internal ReadCoilsAsync. Address: {RegisterAddressRight}, Count: {RegisterCountRight}", LogLevel.Debug);
            await ExecuteWithBusyStateRight(async () =>
            {
                LastErrorRight = string.Empty;
                var values = await _modbusServiceRight.ReadCoilsAsync(RegisterAddressRight, RegisterCountRight);
                if (values != null)
                {
                    DisplayBooleanResultsRight(values, RegisterAddressRight, ModbusService.ModbusFunctionCode.ReadCoils);
                    Logger.Log($"ModbusViewModel: Successfully read {values.Length} Coils from address {RegisterAddressRight}.", LogLevel.Info);
                }
                else
                {
                    Logger.Log($"ModbusViewModel: Failed to read Coils from address {RegisterAddressRight}.", LogLevel.Warning);
                }
            });
        }

        #endregion

        #region Display Methods

        private void DisplayRegisterResultsLeft(ushort[] values, ushort startAddress, ModbusService.ModbusFunctionCode functionCode)
        {
            _uiSyncContext.Post(_ =>
            {
                // Logger.Log($"Modbus Read Results ({functionCode}, StartAddr: {startAddress}):", LogLevel.Info); // Too verbose
                string dataType = "Unknown";
                if (values.Length > 0)
                {
                    var rv = new RegisterValue { FunctionCode = functionCode, IsBoolean = false }; // Temporary to get type
                    dataType = rv.DataTypeName;
                }

                for (int i = 0; i < values.Length; i++)
                {
                    var rv = new RegisterValue
                    {
                        Address = (ushort)(startAddress + i),
                        Value = values[i],
                        FunctionCode = functionCode,
                        IsBoolean = false
                    };
                }
                LastErrorLeft = $"Leídos {values.Length} {dataType}(s) desde dirección {startAddress}";
                Logger.Log($"Modbus: Lectura exitosa de {values.Length} {dataType}(s) desde dirección {startAddress}", LogLevel.Success);
            }, null);
        }

        private void DisplayBooleanResultsLeft(bool[] values, ushort startAddress, ModbusService.ModbusFunctionCode functionCode)
        {
            _uiSyncContext.Post(_ =>
            {
                // Logger.Log($"Modbus Read Results ({functionCode}, StartAddr: {startAddress}):", LogLevel.Info); // Too verbose
                string dataType = "Unknown";
                if (values.Length > 0)
                {
                    var rv = new RegisterValue { FunctionCode = functionCode, IsBoolean = true }; // Temporary to get type
                    dataType = rv.DataTypeName;
                }

                for (int i = 0; i < values.Length; i++)
                {
                    var rv = new RegisterValue
                    {
                        Address = (ushort)(startAddress + i),
                        BooleanValue = values[i],
                        FunctionCode = functionCode,
                        IsBoolean = true
                    };
                    if (i == 0 || i == values.Length - 1 || values.Length <= 10) // Log first, last, or all if few
                    {
                        Logger.Log($"  Read - Addr: {rv.ModbusAddress}, Val: {rv.DisplayValue}", LogLevel.Debug);
                    }
                }
                LastErrorLeft = $"Read {values.Length} {dataType}(s) from address {startAddress}.";
                Logger.Log($"Modbus Read: Successfully read {values.Length} {dataType}(s) starting at address {startAddress}.", LogLevel.Info);
            }, null);
        }

        private void DisplayRegisterResultsRight(ushort[] values, ushort startAddress, ModbusService.ModbusFunctionCode functionCode)
        {
            _uiSyncContext.Post(_ =>
            {
                // Logger.Log($"Modbus Read Results ({functionCode}, StartAddr: {startAddress}):", LogLevel.Info); // Too verbose
                string dataType = "Unknown";
                if (values.Length > 0)
                {
                    var rv = new RegisterValue { FunctionCode = functionCode, IsBoolean = false }; // Temporary to get type
                    dataType = rv.DataTypeName;
                }

                for (int i = 0; i < values.Length; i++)
                {
                    var rv = new RegisterValue
                    {
                        Address = (ushort)(startAddress + i),
                        Value = values[i],
                        FunctionCode = functionCode,
                        IsBoolean = false
                    };
                }
                LastErrorRight = $"Leídos {values.Length} {dataType}(s) desde dirección {startAddress}";
                Logger.Log($"Modbus: Lectura exitosa de {values.Length} {dataType}(s) desde dirección {startAddress}", LogLevel.Success);
            }, null);
        }

        private void DisplayBooleanResultsRight(bool[] values, ushort startAddress, ModbusService.ModbusFunctionCode functionCode)
        {
            _uiSyncContext.Post(_ =>
            {
                // Logger.Log($"Modbus Read Results ({functionCode}, StartAddr: {startAddress}):", LogLevel.Info); // Too verbose
                string dataType = "Unknown";
                if (values.Length > 0)
                {
                    var rv = new RegisterValue { FunctionCode = functionCode, IsBoolean = true }; // Temporary to get type
                    dataType = rv.DataTypeName;
                }

                for (int i = 0; i < values.Length; i++)
                {
                    var rv = new RegisterValue
                    {
                        Address = (ushort)(startAddress + i),
                        BooleanValue = values[i],
                        FunctionCode = functionCode,
                        IsBoolean = true
                    };
                    if (i == 0 || i == values.Length - 1 || values.Length <= 10) // Log first, last, or all if few
                    {
                        Logger.Log($"  Read - Addr: {rv.ModbusAddress}, Val: {rv.DisplayValue}", LogLevel.Debug);
                    }
                }
                LastErrorRight = $"Read {values.Length} {dataType}(s) from address {startAddress}.";
                Logger.Log($"Modbus Read: Successfully read {values.Length} {dataType}(s) starting at address {startAddress}.", LogLevel.Info);
            }, null);
        }

        #endregion

        #region Event Handlers

        private void OnConnectionStatusChangedLeft(object sender, bool connected)
        {
            IsConnectedLeft = connected;
            ConnectionStatusLeft = connected ? $"Connected to {SelectedConfigurationLeft?.Name ?? "Unknown"}" : "Disconnected";
            LastErrorLeft = string.Empty;
            Logger.Log($"ModbusViewModel: OnConnectionStatusChanged event from ModbusService. Service reported Connected: {connected}. ViewModel IsConnected set to: {IsConnectedLeft}, ConnectionStatus: '{ConnectionStatusLeft}'", LogLevel.Info);

            // Update the new status indicator
            if (System.Windows.Forms.Application.OpenForms.OfType<Form1>().FirstOrDefault() is Form1 form)
            {
                bool anyModbusConnected = IsConnectedLeft || IsConnectedRight;
                string statusText = anyModbusConnected ? "Modbus: Conectado" : "Modbus: Desconectado";
                form.UpdateModbusStatus(anyModbusConnected, statusText);
            }
        }

        private void OnCommunicationErrorLeft(object sender, Exception ex)
        {
            LastErrorLeft = $"Communication Error: {ex.Message}";
            ConnectionStatusLeft = "Error"; // Or keep previous status and just show error
            IsConnectedLeft = false; // Assume error means disconnected or unusable connection
            Logger.Log($"ModbusViewModel: ModbusService CommunicationError event. Error: {ex.Message}", LogLevel.Error);

            // Update the new status indicator
            if (System.Windows.Forms.Application.OpenForms.OfType<Form1>().FirstOrDefault() is Form1 form)
            {
                bool anyModbusConnected = IsConnectedLeft || IsConnectedRight;
                string statusText = anyModbusConnected ? "Modbus: Conectado" : "Modbus: Error";
                form.UpdateModbusStatus(anyModbusConnected, statusText);
            }
        }

        private void OnDataReceivedLeft(object sender, ModbusDataReceivedEventArgs e)
        {
            // Data received events are too verbose for production logging - removed
            // UI update for received data is handled by DisplayRegisterResults/DisplayBooleanResults
            // Called directly after successful read/write operations that trigger them.
        }

        private void OnConnectionStatusChangedRight(object sender, bool connected)
        {
            IsConnectedRight = connected;
            ConnectionStatusRight = connected ? $"Connected to {SelectedConfigurationRight?.Name ?? "Unknown"}" : "Disconnected";
            LastErrorRight = string.Empty;
            Logger.Log($"Modbus (Derecha): {(connected ? "Conectado" : "Desconectado")} - {SelectedConfigurationRight?.Name ?? "Desconocido"}", LogLevel.Info);

            // Update the new status indicator
            if (System.Windows.Forms.Application.OpenForms.OfType<Form1>().FirstOrDefault() is Form1 form)
            {
                bool anyModbusConnected = IsConnectedLeft || IsConnectedRight;
                string statusText = anyModbusConnected ? "Modbus: Conectado" : "Modbus: Desconectado";
                form.UpdateModbusStatus(anyModbusConnected, statusText);
            }
        }

        private void OnCommunicationErrorRight(object sender, Exception ex)
        {
            LastErrorRight = $"Communication Error: {ex.Message}";
            ConnectionStatusRight = "Error"; // Or keep previous status and just show error
            IsConnectedRight = false; // Assume error means disconnected or unusable connection
            Logger.Log($"Error de comunicación Modbus (Derecha): {ex.Message}", LogLevel.Error);

            // Update the new status indicator
            if (System.Windows.Forms.Application.OpenForms.OfType<Form1>().FirstOrDefault() is Form1 form)
            {
                bool anyModbusConnected = IsConnectedLeft || IsConnectedRight;
                string statusText = anyModbusConnected ? "Modbus: Conectado" : "Modbus: Error";
                form.UpdateModbusStatus(anyModbusConnected, statusText);
            }
        }

        private void OnDataReceivedRight(object sender, ModbusDataReceivedEventArgs e)
        {
            // Data received events are too verbose for production logging - removed
            // UI update for received data is handled by DisplayRegisterResults/DisplayBooleanResults
            // Called directly after successful read/write operations that trigger them.
        }

        #endregion

        #region Async Execute Methods

        private async Task ExecuteWithBusyStateLeft(Func<Task> action)
        {
            IsBusyLeft = true;
            // Explicitly call RaiseCanExecuteChanged for relevant commands
            ((RelayCommand)ConnectCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)DisconnectCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)ReadHoldingRegistersCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)ReadInputRegistersCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)ReadCoilsCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)ReadDiscreteInputsCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)WriteSingleRegisterCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)WriteSingleCoilCommandLeft).RaiseCanExecuteChanged();
            ((RelayCommand)SaveConfigCommand).RaiseCanExecuteChanged();
            ((RelayCommand)DeleteConfigCommand).RaiseCanExecuteChanged();
            // Removed verbose busy state logging for production

            try
            {
                await action();
            }
            catch (Exception ex)
            {
                LastErrorLeft = $"Operation failed: {ex.Message}";
                Logger.Log($"Error en operación Modbus (Izquierda): {ex.Message}", LogLevel.Error);
                // Optionally, re-throw or handle more gracefully depending on app requirements
            }
            finally
            {
                IsBusyLeft = false;
                // Explicitly call RaiseCanExecuteChanged for relevant commands
                ((RelayCommand)ConnectCommandLeft).RaiseCanExecuteChanged();
                ((RelayCommand)DisconnectCommandLeft).RaiseCanExecuteChanged();
                ((RelayCommand)ReadHoldingRegistersCommandLeft).RaiseCanExecuteChanged();
                ((RelayCommand)ReadInputRegistersCommandLeft).RaiseCanExecuteChanged();
                ((RelayCommand)ReadCoilsCommandLeft).RaiseCanExecuteChanged();
                ((RelayCommand)ReadDiscreteInputsCommandLeft).RaiseCanExecuteChanged();
                ((RelayCommand)WriteSingleRegisterCommandLeft).RaiseCanExecuteChanged();
                ((RelayCommand)WriteSingleCoilCommandLeft).RaiseCanExecuteChanged();
                ((RelayCommand)SaveConfigCommand).RaiseCanExecuteChanged();
                ((RelayCommand)DeleteConfigCommand).RaiseCanExecuteChanged();
                // Removed verbose busy state logging for production
            }
        }

        private async Task ExecuteWithBusyStateRight(Func<Task> action)
        {
            IsBusyRight = true;
            // Explicitly call RaiseCanExecuteChanged for relevant commands
            ((RelayCommand)ConnectCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)DisconnectCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)ReadHoldingRegistersCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)ReadInputRegistersCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)ReadCoilsCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)ReadDiscreteInputsCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)WriteSingleRegisterCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)WriteSingleCoilCommandRight).RaiseCanExecuteChanged();
            ((RelayCommand)SaveConfigCommand).RaiseCanExecuteChanged();
            ((RelayCommand)DeleteConfigCommand).RaiseCanExecuteChanged();
            // Removed verbose busy state logging for production

            try
            {
                await action();
            }
            catch (Exception ex)
            {
                LastErrorRight = $"Operation failed: {ex.Message}";
                Logger.Log($"Error en operación Modbus (Derecha): {ex.Message}", LogLevel.Error);
                // Optionally, re-throw or handle more gracefully depending on app requirements
            }
            finally
            {
                IsBusyRight = false;
                // Explicitly call RaiseCanExecuteChanged for relevant commands
                ((RelayCommand)ConnectCommandRight).RaiseCanExecuteChanged();
                ((RelayCommand)DisconnectCommandRight).RaiseCanExecuteChanged();
                ((RelayCommand)ReadHoldingRegistersCommandRight).RaiseCanExecuteChanged();
                ((RelayCommand)ReadInputRegistersCommandRight).RaiseCanExecuteChanged();
                ((RelayCommand)ReadCoilsCommandRight).RaiseCanExecuteChanged();
                ((RelayCommand)ReadDiscreteInputsCommandRight).RaiseCanExecuteChanged();
                ((RelayCommand)WriteSingleRegisterCommandRight).RaiseCanExecuteChanged();
                ((RelayCommand)WriteSingleCoilCommandRight).RaiseCanExecuteChanged();
                ((RelayCommand)SaveConfigCommand).RaiseCanExecuteChanged();
                ((RelayCommand)DeleteConfigCommand).RaiseCanExecuteChanged();
                // Removed verbose busy state logging for production
            }
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        /// <summary>
        /// Event triggered when a property value changes
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        /// <param name="propertyName">Name of the property that changed</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Sets a property value and raises the PropertyChanged event if the value changed
        /// </summary>
        /// <typeparam name="T">Type of the property</typeparam>
        /// <param name="storage">Reference to the backing field</param>
        /// <param name="value">New value</param>
        /// <param name="propertyName">Name of the property (auto-filled by compiler)</param>
        /// <returns>True if the value changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string propertyName = null)
        {
            if (System.Collections.Generic.EqualityComparer<T>.Default.Equals(storage, value))
            {
                return false;
            }

            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }

    /// <summary>
    /// Represents a register value with different representations (decimal, hex, binary)
    /// </summary>
    public class RegisterValue
    {
        private ushort _value;
        private bool _booleanValue;

        /// <summary>
        /// Gets or sets the register address
        /// </summary>
        public ushort Address { get; set; }

        /// <summary>
        /// Gets or sets the raw register value. Updates related string representations.
        /// </summary>
        public ushort Value
        {
            get => _value;
            set
            {
                _value = value;
                if (!IsBoolean)
                {
                    Decimal = _value;
                    Hex = _value.ToString("X4");
                    Binary = Convert.ToString(_value, 2).PadLeft(16, '0');
                }
            }
        }

        /// <summary>
        /// Gets or sets the decimal representation
        /// </summary>
        public int Decimal { get; private set; }

        /// <summary>
        /// Gets or sets the hexadecimal representation
        /// </summary>
        public string Hex { get; private set; }

        /// <summary>
        /// Gets or sets the binary representation
        /// </summary>
        public string Binary { get; private set; }

        /// <summary>
        /// Gets or sets the function code used to read this value
        /// </summary>
        public ModbusService.ModbusFunctionCode FunctionCode { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this is a boolean value (coil or discrete input)
        /// </summary>
        public bool IsBoolean { get; set; }

        /// <summary>
        /// Gets or sets the boolean value if this is a coil or discrete input. Updates related string representations.
        /// </summary>
        public bool BooleanValue
        {
            get => _booleanValue;
            set
            {
                _booleanValue = value;
                if (IsBoolean)
                {
                    // For booleans, Value, Decimal, Hex, Binary can represent the boolean state
                    _value = (ushort)(_booleanValue ? 1 : 0);
                    Decimal = _value;
                    Hex = _value.ToString("X1"); // Or "X4" if preferred
                    Binary = _value.ToString(); // Or Convert.ToString(_value, 2).PadLeft(16, '0');
                }
            }
        }

        /// <summary>
        /// Gets the user-friendly display value (adapts for boolean or register)
        /// </summary>
        public string DisplayValue
        {
            get
            {
                return IsBoolean ? (BooleanValue ? "ON" : "OFF") : Value.ToString();
            }
        }

        /// <summary>
        /// Gets the name of the data type (e.g., "Holding Register", "Coil")
        /// </summary>
        public string DataTypeName
        {
            get
            {
                if (IsBoolean)
                {
                    return FunctionCode == ModbusService.ModbusFunctionCode.ReadCoils || FunctionCode == ModbusService.ModbusFunctionCode.WriteSingleCoil ? "Coil" : "Discrete Input";
                }
                else
                {
                    return FunctionCode == ModbusService.ModbusFunctionCode.ReadHoldingRegisters || FunctionCode == ModbusService.ModbusFunctionCode.WriteSingleRegister ? "Holding Register" : "Input Register";
                }
            }
        }

        /// <summary>
        /// Gets the Modbus address format (based on function code)
        /// </summary>
        public string ModbusAddress
        {
            get
            {
                string prefix = FunctionCode switch
                {
                    ModbusService.ModbusFunctionCode.ReadCoils => "0",
                    ModbusService.ModbusFunctionCode.ReadDiscreteInputs => "1",
                    ModbusService.ModbusFunctionCode.ReadInputRegisters => "3",
                    ModbusService.ModbusFunctionCode.ReadHoldingRegisters => "4",
                    _ => "4"
                };

                return $"{prefix}x{Address + 1:D5}"; // Modbus addresses are typically 1-based in display
            }
        }
    }

    /// <summary>
    /// Implementation of the ICommand interface
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool> _canExecute;

        /// <summary>
        /// Initializes a new instance of the RelayCommand class
        /// </summary>
        /// <param name="execute">Action to execute</param>
        /// <param name="canExecute">Function to determine if the command can execute</param>
        public RelayCommand(Action execute, Func<bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        /// <summary>
        /// Determines if the command can execute
        /// </summary>
        /// <param name="parameter">Command parameter (not used)</param>
        /// <returns>True if the command can execute, false otherwise</returns>
        public bool CanExecute(object parameter)
        {
            return _canExecute == null || _canExecute();
        }

        /// <summary>
        /// Executes the command
        /// </summary>
        /// <param name="parameter">Command parameter (not used)</param>
        public void Execute(object parameter)
        {
            _execute();
        }

        /// <summary>
        /// Event triggered when the CanExecute status changes
        /// </summary>
        public event EventHandler CanExecuteChanged;
        //{
        //    //add { CommandManager.RequerySuggested += value; }
        //    //remove { CommandManager.RequerySuggested -= value; }
        //}


        /// <summary>
        /// Raises the CanExecuteChanged event
        /// </summary>
        public void RaiseCanExecuteChanged()
        {
            // Manually raise the event
            CanExecuteChanged?.Invoke(this, EventArgs.Empty);
            //CommandManager.InvalidateRequerySuggested();

        }
    }
}