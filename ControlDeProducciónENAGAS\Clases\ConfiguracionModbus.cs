using System;
using System.IO;
using System.Text.Json;

namespace ControlDeProducciónENAGAS.Clases
{
    /// <summary>
    /// Clase para manejar la configuración Modbus desde archivo JSON
    /// </summary>
    public class ConfiguracionModbus
    {
        public string Ip { get; set; } = "*************";
        public int Puerto { get; set; } = 502;
        public byte DeviceId { get; set; } = 1;
        public int Timeout { get; set; } = 5000;
        public bool ReconexionAutomatica { get; set; } = true;
        public int IntervaloReconexion { get; set; } = 10; // segundos

        /// <summary>
        /// Carga la configuración desde el archivo JSON
        /// </summary>
        /// <param name="rutaArchivo">Ruta del archivo de configuración</param>
        /// <returns>Configuración Modbus cargada</returns>
        public static ConfiguracionModbus CargarDesdeArchivo(string rutaArchivo = "config_modbus.json")
        {
            try
            {
                if (!File.Exists(rutaArchivo))
                {
                    // Crear archivo de configuración por defecto
                    var configDefault = new ConfiguracionModbus
                    {
                        Ip = "*************", // IP por defecto para ENAGAS Huelva
                        Puerto = 502,
                        DeviceId = 1,
                        Timeout = 5000,
                        ReconexionAutomatica = true,
                        IntervaloReconexion = 10
                    };

                    GuardarEnArchivo(configDefault, rutaArchivo);
                    return configDefault;
                }

                string jsonContent = File.ReadAllText(rutaArchivo);
                var jsonDoc = JsonDocument.Parse(jsonContent);
                var modbusSection = jsonDoc.RootElement.GetProperty("modbus");

                return new ConfiguracionModbus
                {
                    Ip = modbusSection.TryGetProperty("ip", out var ip) ? ip.GetString() ?? "*************" : "*************",
                    Puerto = modbusSection.TryGetProperty("puerto", out var puerto) ? puerto.GetInt32() : 502,
                    DeviceId = modbusSection.TryGetProperty("deviceId", out var deviceId) ? (byte)deviceId.GetInt32() : (byte)1,
                    Timeout = modbusSection.TryGetProperty("timeout", out var timeout) ? timeout.GetInt32() : 5000,
                    ReconexionAutomatica = modbusSection.TryGetProperty("reconexionAutomatica", out var reconexion) ? reconexion.GetBoolean() : true,
                    IntervaloReconexion = modbusSection.TryGetProperty("intervaloReconexion", out var intervalo) ? intervalo.GetInt32() : 10
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"Error cargando configuración Modbus: {ex.Message}");
            }
        }

        /// <summary>
        /// Guarda la configuración en archivo JSON
        /// </summary>
        /// <param name="config">Configuración a guardar</param>
        /// <param name="rutaArchivo">Ruta del archivo</param>
        public static void GuardarEnArchivo(ConfiguracionModbus config, string rutaArchivo = "config_modbus.json")
        {
            try
            {
                var jsonObject = new
                {
                    modbus = new
                    {
                        ip = config.Ip,
                        puerto = config.Puerto,
                        deviceId = config.DeviceId,
                        timeout = config.Timeout,
                        reconexionAutomatica = config.ReconexionAutomatica,
                        intervaloReconexion = config.IntervaloReconexion
                    }
                };

                string jsonString = JsonSerializer.Serialize(jsonObject, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                File.WriteAllText(rutaArchivo, jsonString);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error guardando configuración Modbus: {ex.Message}");
            }
        }

        /// <summary>
        /// Valida que la configuración sea correcta
        /// </summary>
        /// <returns>True si es válida</returns>
        public bool EsValida()
        {
            return !string.IsNullOrWhiteSpace(Ip) &&
                   Puerto > 0 && Puerto <= 65535 &&
                   DeviceId >= 0 && DeviceId <= 255 &&
                   Timeout > 0 &&
                   IntervaloReconexion > 0;
        }

        /// <summary>
        /// Obtiene errores de validación
        /// </summary>
        /// <returns>Lista de errores</returns>
        public List<string> ObtenerErrores()
        {
            var errores = new List<string>();

            if (string.IsNullOrWhiteSpace(Ip))
                errores.Add("❌ La IP no puede estar vacía");

            if (Puerto <= 0 || Puerto > 65535)
                errores.Add("❌ El puerto debe estar entre 1 y 65535");

            if (DeviceId < 0 || DeviceId > 255)
                errores.Add("❌ El Device ID debe estar entre 0 y 255");

            if (Timeout <= 0)
                errores.Add("❌ El timeout debe ser mayor a 0");

            if (IntervaloReconexion <= 0)
                errores.Add("❌ El intervalo de reconexión debe ser mayor a 0");

            return errores;
        }

        public override string ToString()
        {
            return $"Modbus Config: {Ip}:{Puerto} (Device ID: {DeviceId}, Timeout: {Timeout}ms, Reconexión: {ReconexionAutomatica})";
        }
    }
}
