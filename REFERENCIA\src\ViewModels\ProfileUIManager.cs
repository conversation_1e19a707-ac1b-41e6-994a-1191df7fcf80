﻿using prueba.ViewModels;
using prueba.Models;
using prueba.Services;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;


namespace prueba
{
    public class ProfileUIManager
    {
        private readonly Form1 form; // Referencia al formulario principal
        private readonly ConnectionProfileManager profileManager;
        private readonly FlowLayoutPanel panelProfiles;
        private List<Panel> profileCardList = new List<Panel>();

        // Crear una instancia de PublishConfigManager
        private readonly PublishConfigManager publishConfigManager = new PublishConfigManager();

        // Flag to prevent duplicate email initialization
        private bool _emailInitializationInProgress = false;

        public ProfileUIManager(Form1 form, ConnectionProfileManager profileManager, FlowLayoutPanel panelProfiles)
        {
            this.form = form;
            this.profileManager = profileManager;
            this.panelProfiles = panelProfiles;
            Initialize();
        }

        private void Initialize()
        {
            CreateProfileCards();
        }

        public void CreateProfileCards()
        {
            profileCardList.Clear();
            panelProfiles.Controls.Clear();

            // Obtener los perfiles existentes
            var profiles = profileManager.LoadProfiles();

            // Crear una tarjeta para cada perfil (hasta 5)
            for (int i = 0; i < 5; i++)
            {
                Panel card = new Panel();
                card.Size = new Size(170, 75); // Increased size for better spacing
                card.Margin = new Padding(8, 5, 8, 5); // Increased horizontal margin for better spacing
                card.Tag = i;
                card.Cursor = Cursors.Hand;
                card.BackColor = profileManager.HasProfileAt(i) ? Color.FromArgb(60, 100, 60) : Color.FromArgb(60, 63, 65);

                // Añadir borde redondeado
                card.Paint += (s, e) =>
                {
                    using (GraphicsPath path = new GraphicsPath())
                    {
                        int radius = 8;
                        Rectangle rect = new Rectangle(0, 0, card.Width - 1, card.Height - 1);
                        path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90);
                        path.AddArc(rect.Right - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90);
                        path.AddArc(rect.Right - radius * 2, rect.Bottom - radius * 2, radius * 2, radius * 2, 0, 90);
                        path.AddArc(rect.X, rect.Bottom - radius * 2, radius * 2, radius * 2, 90, 90);
                        path.CloseFigure();
                        e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
                        using (Pen pen = new Pen(Color.FromArgb(100, 100, 100), 1))
                        {
                            e.Graphics.DrawPath(pen, path);
                        }
                    }
                };

                // Etiqueta para el número de perfil
                Label lblNumber = new Label();
                lblNumber.Text = (i + 1).ToString();
                lblNumber.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
                lblNumber.ForeColor = Color.White;
                lblNumber.AutoSize = false;
                lblNumber.Size = new Size(32, 32); // Slightly larger number badge
                lblNumber.Location = new Point(12, 18); // Better positioning with more padding
                lblNumber.TextAlign = ContentAlignment.MiddleCenter;
                lblNumber.BackColor = Color.FromArgb(0, 122, 204);

                // Etiqueta para el nombre del perfil
                Label lblName = new Label();
                string profileName = "Vacío";
                if (i < profiles.Count && profiles[i] != null && !string.IsNullOrEmpty(profiles[i].Name))
                {
                    profileName = profiles[i].Name;
                }

                lblName.Text = profileName;
                lblName.Font = new Font("Segoe UI Semibold", 9.75F, FontStyle.Bold);
                lblName.ForeColor = Color.White;
                lblName.AutoSize = false;
                lblName.Size = new Size(115, 22); // Increased width for better text display
                lblName.Location = new Point(50, 12); // Better positioning with more spacing

                // Etiqueta para el host (si existe)
                Label lblHost = new Label();
                string hostInfo = "";
                if (i < profiles.Count && profiles[i] != null && !string.IsNullOrEmpty(profiles[i].Host))
                {
                    hostInfo = profiles[i].Host + ":" + profiles[i].Port;
                }

                lblHost.Text = hostInfo;
                lblHost.Font = new Font("Segoe UI", 8F);
                lblHost.ForeColor = Color.Silver;
                lblHost.AutoSize = false;
                lblHost.Size = new Size(115, 18); // Increased width for better text display
                lblHost.Location = new Point(50, 38); // Better positioning with more spacing

                // Añadir los controles a la tarjeta
                card.Controls.Add(lblNumber);
                card.Controls.Add(lblName);
                card.Controls.Add(lblHost);

                // Manejar el evento de clic
                card.Click += ProfileCard_Click;
                card.MouseClick += ProfileCard_MouseClick;

                // Añadir la tarjeta al panel y a la lista
                panelProfiles.Controls.Add(card);
                profileCardList.Add(card);
            }
        }

        private async void ProfileCard_Click(object sender, EventArgs e)
        {
            Panel card = sender as Panel;
            if (card?.Tag == null) return;
            int idx = (int)card.Tag;
            try
            {
                if (Control.ModifierKeys == Keys.Control)
                {
                    SaveProfile(idx);
                }
                else
                {
                    await LoadProfile(idx);
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error al procesar el perfil: {ex.Message}", LogLevel.Error);
                MessageBox.Show($"Error al procesar el perfil: {ex.Message}",
                 "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ProfileCard_MouseClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                Panel card = sender as Panel;
                if (card?.Tag == null) return;
                int idx = (int)card.Tag;

                // Check if profile exists
                var profile = profileManager.GetProfileAt(idx);
                if (profile != null && (!string.IsNullOrEmpty(profile.Host) || !string.IsNullOrEmpty(profile.Pop3Server)))
                {
                    RenameProfile(idx, profile);
                }
                else
                {
                    MessageBox.Show("No se puede renombrar un perfil vacío.", "Información",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void RenameProfile(int idx, ConnectionProfile profile)
        {
            try
            {
                string currentName = profile.Name ?? $"Perfil {idx + 1}";

                // Create a simple input dialog
                string newName = ShowInputDialog("Renombrar Perfil", "Ingrese el nuevo nombre para el perfil:", currentName);

                if (!string.IsNullOrWhiteSpace(newName) && newName.Trim() != currentName)
                {
                    // Update the profile name
                    profile.Name = newName.Trim();
                    profileManager.SaveProfileAt(profile, idx);

                    // Update the form title if this is the currently loaded profile
                    if (form.currentLoadedProfileIndex == idx)
                    {
                        form.currentActiveProfile.Name = newName.Trim();
                        form.Text = $"Modern MQTT Client - {newName.Trim()}";
                        form.txtName.Text = newName.Trim();
                    }

                    // Refresh the profile cards to show the new name
                    CreateProfileCards();

                    Logger.Log($"Perfil renombrado de '{currentName}' a '{newName.Trim()}'", LogLevel.Info);
                    MessageBox.Show($"Perfil renombrado exitosamente a '{newName.Trim()}'",
                        "Perfil Renombrado", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error al renombrar el perfil: {ex.Message}", LogLevel.Error);
                MessageBox.Show($"Error al renombrar el perfil: {ex.Message}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveProfile(int idx)
        {
            // Verificar campos obligatorios
            if (string.IsNullOrWhiteSpace(form.txtHostAddress.Text))
            {
                MessageBox.Show("El campo Host es obligatorio.", "Validación",
                 MessageBoxButtons.OK, MessageBoxIcon.Warning);
                Logger.Log("El campo Host es obligatorio.", LogLevel.Error);
                return;
            }

            form.currentActiveProfile.Name = string.IsNullOrWhiteSpace(form.txtName.Text) ?
             $"Perfil {idx + 1}" : form.txtName.Text.Trim();
            form.currentActiveProfile.Protocol = form.cmbHost.SelectedItem?.ToString() ?? "mqtt://";
            form.currentActiveProfile.Host = form.txtHostAddress.Text.Trim();
            form.currentActiveProfile.Port = int.TryParse(form.txtPort.Text.Trim(), out int p) ? p : 1883;
            form.currentActiveProfile.ClientId = form.txtClientID.Text.Trim();
            form.currentActiveProfile.Username = form.txtUsername.Text.Trim();
            form.currentActiveProfile.Password = form.txtPassword.Text;
            form.currentActiveProfile.UseTls = form.chkSSL_TLS.Checked;
            form.currentActiveProfile.IsCASigned = form.rdoCASigned.Checked;

            // Save email configuration (POP3/EWS) - Apply current UI values to profile
            form.ApplyMailUIToProfile();

            // Save Modbus configuration settings
            form.currentActiveProfile.LeftModbusConfigName = form._modbusViewModel?.SelectedConfigurationLeft?.Name ?? string.Empty;
            form.currentActiveProfile.RightModbusConfigName = form._modbusViewModel?.SelectedConfigurationRight?.Name ?? string.Empty;

            // Set auto-connect based on current connection status, but never for default configurations
            // This prevents default configurations from being saved with auto-connect enabled
            bool leftIsConnected = form._modbusViewModel?.IsConnectedLeft ?? false;
            bool rightIsConnected = form._modbusViewModel?.IsConnectedRight ?? false;

            // Only set auto-connect to true if connected AND not a default configuration
            bool leftIsDefault = form._modbusViewModel?.SelectedConfigurationLeft != null && IsDefaultConfiguration(form._modbusViewModel.SelectedConfigurationLeft);
            bool rightIsDefault = form._modbusViewModel?.SelectedConfigurationRight != null && IsDefaultConfiguration(form._modbusViewModel.SelectedConfigurationRight);

            form.currentActiveProfile.AutoConnectLeftModbus = leftIsConnected && !leftIsDefault;
            form.currentActiveProfile.AutoConnectRightModbus = rightIsConnected && !rightIsDefault;

            if (leftIsDefault && leftIsConnected)
            {
                Logger.Log($"SaveProfile: Left Modbus config '{form._modbusViewModel.SelectedConfigurationLeft.Name}' is default - setting AutoConnect to false despite being connected", LogLevel.Info);
            }
            if (rightIsDefault && rightIsConnected)
            {
                Logger.Log($"SaveProfile: Right Modbus config '{form._modbusViewModel.SelectedConfigurationRight.Name}' is default - setting AutoConnect to false despite being connected", LogLevel.Info);
            }

            Logger.Log($"Saving Modbus config to profile - Left: '{form.currentActiveProfile.LeftModbusConfigName}', Right: '{form.currentActiveProfile.RightModbusConfigName}', AutoConnectLeft: {form.currentActiveProfile.AutoConnectLeftModbus}, AutoConnectRight: {form.currentActiveProfile.AutoConnectRightModbus}", LogLevel.Info);
            profileManager.SaveProfileAt(form.currentActiveProfile, idx);

            // Also save MQTT configurations when profile is saved
            form.SavePublishConfigurations();
            form.currentLoadedProfileIndex = idx;
            form.Text = $"Modern MQTT Client - {form.currentActiveProfile.Name}";
            Logger.Log($"Perfil {form.currentActiveProfile.Name} guardado correctamente.", LogLevel.Info);
            MessageBox.Show($"Perfil '{form.currentActiveProfile.Name}' guardado correctamente.",
             "Perfil Guardado", MessageBoxButtons.OK, MessageBoxIcon.Information);
            CreateProfileCards();
        }

        public async Task LoadProfile(int idx)
        {
            Logger.Log($"LoadProfile: Starting to load profile at index {idx}", LogLevel.Info);

            // Disconnect from MQTT before switching profiles for clean transitions
            if (form.mqttService != null)
            {
                try
                {
                    Logger.Log("LoadProfile: Disconnecting from MQTT before profile switch...", LogLevel.Info);
                    await form.mqttService.DisconnectAsync();
                    Logger.Log("LoadProfile: MQTT disconnection completed", LogLevel.Info);
                }
                catch (Exception ex)
                {
                    Logger.Log($"LoadProfile: Error disconnecting MQTT: {ex.Message}", LogLevel.Warning);
                }
            }

            var loadedProfile = profileManager.GetProfileAt(idx);

            if (loadedProfile != null)
            {
                Logger.Log($"LoadProfile: Profile loaded - Name: '{loadedProfile.Name}', Host: '{loadedProfile.Host}', LeftModbus: '{loadedProfile.LeftModbusConfigName}', RightModbus: '{loadedProfile.RightModbusConfigName}'", LogLevel.Info);
            }

            if (loadedProfile != null &&
             (!string.IsNullOrEmpty(loadedProfile.Host) || !string.IsNullOrEmpty(loadedProfile.Pop3Server)))
            {
                form.currentActiveProfile = loadedProfile;
                form.currentLoadedProfileIndex = idx;

                // Save this as the last loaded profile
                profileManager.SaveLastLoadedProfile(idx);

                Logger.Log($"LoadProfile: Using loaded profile '{loadedProfile.Name}' with Modbus configs - Left: '{loadedProfile.LeftModbusConfigName}', Right: '{loadedProfile.RightModbusConfigName}'", LogLevel.Info);
            }
            else
            {
                form.currentActiveProfile = new ConnectionProfile { Name = $"Perfil {idx + 1}" };
                form.currentLoadedProfileIndex = null;
                Logger.Log($"Casilla {idx + 1} vacía. Se usará configuración por defecto.", LogLevel.Warning);
                MessageBox.Show($"Casilla {idx + 1} vacía. Se usará configuración por defecto.",
                 "Perfil Nuevo", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

            UpdateFormFieldsFromProfile(form.currentActiveProfile);
            //form.ClearUIAndTopicMemoryState();

            // Load and apply publish configurations to maintain checkbox states
            publishConfigManager.LoadConfigurations();
            form.ApplyLoadedPublishConfigurations();

            // Load and synchronize Modbus configurations to ensure they are available for the profile
            form.EnsureModbusConfigurationsSynchronized();

            // Restore Modbus configuration selections from profile
            RestoreModbusConfigurationsFromProfile(form.currentActiveProfile);

            // Refresh topics table to show current state with preserved checkboxes
            // Note: This should preserve the checkbox states that were just loaded
            form.TxtFiltroTopicos_TextChanged(null, EventArgs.Empty);

            // CRITICAL FIX: Remove blocking email operations that cause UI thread hanging
            // These operations are now handled asynchronously by delayed initialization
            Logger.Log("LoadProfile: Starting delayed email and MQTT initialization to prevent UI thread blocking", LogLevel.Info);

            // Start delayed email processing initialization with safety check
            _ = Task.Run(async () =>
            {
                try
                {
                    // Prevent duplicate email initialization
                    if (_emailInitializationInProgress)
                    {
                        Logger.Log("LoadProfile: Email initialization already in progress, skipping duplicate request", LogLevel.Info);
                        return;
                    }

                    _emailInitializationInProgress = true;

                    // Wait for UI to be fully updated before starting email processing
                    await Task.Delay(1500); // Longer delay for email processing to ensure full initialization

                    Logger.Log("LoadProfile: Starting delayed email processing initialization...", LogLevel.Info);

                    // Initialize email manager and start processing on UI thread
                    await form.Invoke(new Func<Task>(async () =>
                    {
                        try
                        {
                            // Only initialize email processing if profile has email configuration
                            if (ShouldInitializeEmailProcessing(form.currentActiveProfile))
                            {
                                Logger.Log("LoadProfile: Starting automatic email connection test (EWS or POP3)...", LogLevel.Info);

                                // Initialize email manager with current profile configuration
                                form.InitializeEmailManager();

                                // Call the appropriate connection test function based on profile type
                                await form.Probar_Correo_Automatico();

                                // Start email processing with proper sequencing (same as Form1.UpdateFormFieldsFromProfile)
                                form.StartEmailProcessingSequentially(form.currentActiveProfile);

                                Logger.Log("LoadProfile: Automatic email connection test and processing initialization completed", LogLevel.Info);
                            }
                            else
                            {
                                Logger.Log("LoadProfile: Skipping email initialization - no email configuration in profile", LogLevel.Info);
                            }
                        }
                        catch (Exception emailEx)
                        {
                            Logger.Log($"Error during delayed email initialization: {emailEx.Message}", LogLevel.Error);
                        }
                        finally
                        {
                            _emailInitializationInProgress = false;
                        }
                    }));
                }
                catch (Exception ex)
                {
                    Logger.Log($"Error in delayed email processing task: {ex.Message}", LogLevel.Warning);
                    _emailInitializationInProgress = false;
                }
            });

            // Connect to MQTT broker asynchronously without blocking
            _ = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(500); // Small delay to ensure UI updates complete
                    form.Invoke(new Action(() => form._mqttConnectionUIManager.ConnectToBroker()));
                }
                catch (Exception ex)
                {
                    Logger.Log($"Error connecting to MQTT broker during profile load: {ex.Message}", LogLevel.Warning);
                }
            });

            // Auto-populate and select Device IDs in MQTT Configuration (now includes saved configs)
            form.PopulateDeviceIdDropdowns();
            form.AutoSelectDeviceIds();

            // Update UI without Application.DoEvents() to prevent reentrancy issues
            form.Invoke(new Action(() => CreateProfileCards()));

            Logger.Log($"LoadProfile: Profile {idx} loaded successfully without blocking operations", LogLevel.Info);
        }

        public void UpdateFormFieldsFromProfile(ConnectionProfile profile)
        {
            if (profile == null) profile = new ConnectionProfile(); // Evitar NullReference
            form.txtName.Text = profile.Name ?? (form.currentLoadedProfileIndex.HasValue ? $"Perfil {form.currentLoadedProfileIndex.Value + 1}" : "Nuevo Perfil");
            form.cmbHost.SelectedItem = profile.Protocol ?? "mqtt://";
            form.txtHostAddress.Text = profile.Host;
            form.txtPort.Text = profile.Port != 0 ? profile.Port.ToString() : "1883";
            form.txtClientID.Text = string.IsNullOrEmpty(profile.ClientId) ? ("mqtt_client_" + Guid.NewGuid().ToString().Substring(0, 8)) : profile.ClientId;
            form.txtUsername.Text = profile.Username;
            form.txtPassword.Text = profile.Password;
            form.chkSSL_TLS.Checked = profile.UseTls;
            form.rdoCASigned.Checked = profile.IsCASigned;
            form.rdoSelfSigned.Checked = !profile.IsCASigned;

            // Añadir actualización de campos de correo POP3
            form.txtServidor.Text = profile.Pop3Server;
            form.txtPuerto.Text = profile.Pop3Port.ToString();
            form.txtUsuario.Text = profile.Pop3Username;
            form.txtContraseña.Text = profile.Pop3Password;
            form.chkPop3Ssl.Checked = profile.Pop3UseSsl;

            // Añadir actualización de campos de correo EWS
            form.chkUseEws.Checked = profile.UseEws;
            form.txtEwsServerUrl.Text = profile.EwsServerUrl ?? "";
            form.txtEwsUsername.Text = profile.EwsUsername ?? "";
            form.txtEwsPassword.Text = profile.EwsPassword ?? "";
            form.txtEwsDomain.Text = profile.EwsDomain ?? "";
            form.chkEwsUseOAuth.Checked = profile.EwsUseOAuth;

            // Email notification addresses
            form.txtEmailNotif1.Text = profile.EmailNotification1 ?? "";
            form.txtEmailNotif2.Text = profile.EmailNotification2 ?? "";
            form.txtEmailNotif3.Text = profile.EmailNotification3 ?? "";
            form.txtEmailNotif4.Text = profile.EmailNotification4 ?? "";
            form.txtEmailNotif5.Text = profile.EmailNotification5 ?? "";

            // Update email checking settings
            if (form.nudCheckInterval != null)
            {
                form.nudCheckInterval.Value = Math.Max(1, Math.Min(60, profile.EmailCheckIntervalMinutes));
            }

            // Update email controls state based on UseEws setting
            form.UpdateEmailControlsState();

            if (!string.IsNullOrEmpty(profile.Name) && form.currentLoadedProfileIndex.HasValue)
            {
                form.Text = $"Modern MQTT Client - {profile.Name}";
            }
            else
            {
                form.Text = "Modern MQTT Client";
            }
        }

        // Puedes añadir un método para refrescar la UI si es necesario
        public void RefreshUI()
        {
            CreateProfileCards();
        }

        /// <summary>
        /// Restores Modbus configuration selections from the loaded profile and attempts automatic connections
        /// </summary>
        /// <param name="profile">The profile containing Modbus configuration settings</param>
        private void RestoreModbusConfigurationsFromProfile(ConnectionProfile profile)
        {
            Logger.Log("RestoreModbusConfigurationsFromProfile: Method called", LogLevel.Info);

            if (profile == null || form._modbusViewModel == null)
            {
                Logger.Log($"Cannot restore Modbus configurations: profile is null: {profile == null}, ModbusViewModel is null: {form._modbusViewModel == null}", LogLevel.Warning);
                return;
            }

            try
            {
                Logger.Log($"Restoring Modbus configurations from profile '{profile.Name}' - Left: '{profile.LeftModbusConfigName}', Right: '{profile.RightModbusConfigName}', AutoConnectLeft: {profile.AutoConnectLeftModbus}, AutoConnectRight: {profile.AutoConnectRightModbus}", LogLevel.Info);

                // Configurations should already be loaded by EnsureModbusConfigurationsSynchronized()
                if (form._modbusViewModel.AvailableConfigurations == null || !form._modbusViewModel.AvailableConfigurations.Any())
                {
                    Logger.Log("No Modbus configurations available after synchronization. Cannot restore profile settings.", LogLevel.Warning);
                    return;
                }

                Logger.Log($"Available Modbus configurations: {string.Join(", ", form._modbusViewModel.AvailableConfigurations.Select(c => c.Name))}", LogLevel.Info);

                // Restore left Modbus configuration
                RestoreAndConnectModbusConfiguration(profile, isLeft: true);

                // Restore right Modbus configuration
                RestoreAndConnectModbusConfiguration(profile, isLeft: false);

                // Update UI to reflect the restored configurations
                form.Invoke(new Action(() =>
                {
                    form.UpdateUIFromModbusViewModel(isLeft: true);
                    form.UpdateUIFromModbusViewModel(isLeft: false);
                }));

                Logger.Log("Modbus configuration restoration completed", LogLevel.Info);
            }
            catch (Exception ex)
            {
                Logger.Log($"Error restoring Modbus configurations from profile: {ex.Message}", LogLevel.Error);
            }
        }

        /// <summary>
        /// Restores and optionally connects a specific Modbus configuration (left or right)
        /// </summary>
        /// <param name="profile">The profile containing configuration settings</param>
        /// <param name="isLeft">True for left panel, false for right panel</param>
        private void RestoreAndConnectModbusConfiguration(ConnectionProfile profile, bool isLeft)
        {
            string configName = isLeft ? profile.LeftModbusConfigName : profile.RightModbusConfigName;
            bool autoConnect = isLeft ? profile.AutoConnectLeftModbus : profile.AutoConnectRightModbus;
            string panelName = isLeft ? "left" : "right";

            Logger.Log($"RestoreAndConnectModbusConfiguration: {panelName} panel - ConfigName: '{configName}', AutoConnect: {autoConnect}", LogLevel.Info);

            // Early exit if no configuration name is specified
            if (string.IsNullOrEmpty(configName))
            {
                Logger.Log($"RestoreAndConnectModbusConfiguration: No {panelName} Modbus configuration name specified, skipping", LogLevel.Info);
                return;
            }

            // Find the configuration
            var config = form._modbusViewModel.AvailableConfigurations
                .FirstOrDefault(c => c.Name.Equals(configName, StringComparison.OrdinalIgnoreCase));

            if (config == null)
            {
                Logger.Log($"{char.ToUpper(panelName[0])}{panelName.Substring(1)} Modbus configuration '{configName}' not found in available configurations", LogLevel.Warning);
                Logger.Log($"Available configurations: {string.Join(", ", form._modbusViewModel.AvailableConfigurations.Select(c => $"'{c.Name}'"))}", LogLevel.Warning);
                return;
            }

            // Set the configuration
            if (isLeft)
            {
                form._modbusViewModel.SelectedConfigurationLeft = config;
            }
            else
            {
                form._modbusViewModel.SelectedConfigurationRight = config;
            }

            Logger.Log($"Restored {panelName} Modbus configuration: {configName}", LogLevel.Info);

            // Attempt auto-connection if enabled and configuration is valid
            if (autoConnect && IsConfigurationValid(config))
            {
                AttemptAutoConnection(config, isLeft, panelName);
            }
            else if (autoConnect)
            {
                Logger.Log($"Skipping auto-connect for {panelName} Modbus: configuration validation failed", LogLevel.Warning);
            }
        }

        /// <summary>
        /// Determines if email processing should be initialized based on profile configuration
        /// </summary>
        /// <param name="profile">The profile to check</param>
        /// <returns>True if email processing should be initialized</returns>
        private bool ShouldInitializeEmailProcessing(ConnectionProfile profile)
        {
            if (profile == null) return false;

            // Check if EWS is configured
            if (profile.UseEws)
            {
                return !string.IsNullOrEmpty(profile.EwsUsername) &&
                       !string.IsNullOrEmpty(profile.EwsPassword);
            }

            // Check if POP3 is configured
            return !string.IsNullOrEmpty(profile.Pop3Server) &&
                   !string.IsNullOrEmpty(profile.Pop3Username) &&
                   !string.IsNullOrEmpty(profile.Pop3Password);
        }

        /// <summary>
        /// Validates if a Modbus configuration has the minimum required settings and is not a default configuration
        /// </summary>
        /// <param name="config">The configuration to validate</param>
        /// <returns>True if configuration is valid for connection and not using default values</returns>
        private bool IsConfigurationValid(ModbusConfiguration config)
        {
            if (config == null)
                return false;

            // Check if configuration has default name - skip auto-connection for default configurations
            if (IsDefaultConfiguration(config))
            {
                Logger.Log($"IsConfigurationValid: Skipping auto-connection for default Modbus configuration: {config.Name} (IP: {config.IpAddress}, Port: {config.Port}, DeviceId: {config.DeviceId})", LogLevel.Info);
                return false;
            }



            if (config.ConnectionType == ModbusService.ModbusConnectionType.TCP)
            {
                return !string.IsNullOrEmpty(config.IpAddress) &&
                       config.Port > 0 && config.Port <= 65535 &&
                       config.DeviceId >= 1 && config.DeviceId <= 247;
            }
            else // RTU
            {
                return !string.IsNullOrEmpty(config.ComPort) &&
                       config.BaudRate > 0 &&
                       config.DeviceId >= 1 && config.DeviceId <= 247;
            }
        }

        /// <summary>
        /// Determines if a Modbus configuration is using default/placeholder values
        /// </summary>
        /// <param name="config">The configuration to check</param>
        /// <returns>True if configuration appears to be a default/placeholder configuration</returns>
        private bool IsDefaultConfiguration(ModbusConfiguration config)
        {
            if (config == null)
            {
                Logger.Log("IsDefaultConfiguration: Config is null, returning true", LogLevel.Info);
                return true;
            }

            Logger.Log($"IsDefaultConfiguration: Checking config '{config.Name}' (Type: {config.ConnectionType}, IP: {config.IpAddress}, Port: {config.Port}, DeviceId: {config.DeviceId})", LogLevel.Info);

            // Check for default names
            var defaultNames = new[] {
                "Default Configuration",
                "Default TCP",
                "Default RTU",
                "Nueva Configuración",
                "New Configuration"
            };

            if (defaultNames.Any(name => config.Name.Equals(name, StringComparison.OrdinalIgnoreCase)))
            {
                Logger.Log($"IsDefaultConfiguration: Config '{config.Name}' matches default name, returning true", LogLevel.Info);
                return true;
            }

            // Check for default TCP configuration values
            if (config.ConnectionType == ModbusService.ModbusConnectionType.TCP)
            {
                bool isDefaultTcp = (config.IpAddress == "127.0.0.1" || config.IpAddress == "localhost") &&
                                   config.Port == 502 &&
                                   config.DeviceId == 1;
                Logger.Log($"IsDefaultConfiguration: TCP config '{config.Name}' default values check: {isDefaultTcp} (IP: {config.IpAddress}, Port: {config.Port}, DeviceId: {config.DeviceId})", LogLevel.Info);
                return isDefaultTcp;
            }
            else // RTU
            {
                bool isDefaultRtu = config.ComPort == "COM1" &&
                                   config.BaudRate == 9600 &&
                                   config.DeviceId == 1;
                Logger.Log($"IsDefaultConfiguration: RTU config '{config.Name}' default values check: {isDefaultRtu} (ComPort: {config.ComPort}, BaudRate: {config.BaudRate}, DeviceId: {config.DeviceId})", LogLevel.Info);
                return isDefaultRtu;
            }
        }

        /// <summary>
        /// Attempts to establish an automatic Modbus connection
        /// </summary>
        /// <param name="config">The configuration to connect with</param>
        /// <param name="isLeft">True for left panel, false for right panel</param>
        /// <param name="panelName">Panel name for logging</param>
        private void AttemptAutoConnection(ModbusConfiguration config, bool isLeft, string panelName)
        {
            // Add a small delay to ensure UI is updated before attempting connection
            Task.Delay(500).ContinueWith(_ =>
            {
                try
                {
                    var connectCommand = isLeft ? form._modbusViewModel.ConnectCommandLeft : form._modbusViewModel.ConnectCommandRight;

                    if (connectCommand?.CanExecute(null) == true)
                    {
                        Logger.Log($"Attempting auto-connection for {panelName} Modbus: {config.Name} ({config.ConnectionType})", LogLevel.Info);

                        connectCommand.Execute(null);

                        // Log the connection attempt
                        Logger.Log($"Auto-connection command executed for {panelName} Modbus: {config.Name}", LogLevel.Info);
                    }
                    else
                    {
                        Logger.Log($"Cannot execute auto-connection for {panelName} Modbus: command not available or not executable", LogLevel.Warning);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Log($"Failed to auto-connect {panelName} Modbus '{config.Name}': {ex.Message}", LogLevel.Error);
                }
            }, TaskScheduler.Default);
        }

        /// <summary>
        /// Shows a simple input dialog for text entry
        /// </summary>
        private string ShowInputDialog(string title, string prompt, string defaultValue = "")
        {
            Form inputForm = new Form()
            {
                Width = 400,
                Height = 180,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                Text = title,
                StartPosition = FormStartPosition.CenterParent,
                MaximizeBox = false,
                MinimizeBox = false,
                BackColor = Color.FromArgb(45, 45, 48),
                ForeColor = Color.White
            };

            Label promptLabel = new Label()
            {
                Left = 20,
                Top = 20,
                Width = 350,
                Text = prompt,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 9F)
            };

            TextBox textBox = new TextBox()
            {
                Left = 20,
                Top = 50,
                Width = 350,
                Text = defaultValue,
                BackColor = Color.FromArgb(60, 63, 65),
                ForeColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Font = new Font("Segoe UI", 9F)
            };

            Button okButton = new Button()
            {
                Text = "Aceptar",
                Left = 220,
                Width = 75,
                Top = 90,
                DialogResult = DialogResult.OK,
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };

            Button cancelButton = new Button()
            {
                Text = "Cancelar",
                Left = 300,
                Width = 75,
                Top = 90,
                DialogResult = DialogResult.Cancel,
                BackColor = Color.FromArgb(60, 63, 65),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };

            inputForm.Controls.Add(promptLabel);
            inputForm.Controls.Add(textBox);
            inputForm.Controls.Add(okButton);
            inputForm.Controls.Add(cancelButton);
            inputForm.AcceptButton = okButton;
            inputForm.CancelButton = cancelButton;

            textBox.SelectAll();
            textBox.Focus();

            return inputForm.ShowDialog() == DialogResult.OK ? textBox.Text : null;
        }
    }
}
