using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using prueba.Services;

namespace prueba
{
    /// <summary>
    /// Manages individual timers for selective Modbus reading based on configured topics and their refresh rates
    /// </summary>
    public class TopicReadingManager
    {
        private readonly Dictionary<string, Timer> _topicTimers;
        private readonly Dictionary<string, TopicReadConfig> _topicConfigs;
        private readonly Func<string, ushort, string, Task> _readAndPublishCallback;
        private readonly Func<string, string, string, Task> _readAndPublishWithFormatCallback;

        public TopicReadingManager(Func<string, ushort, string, Task> readAndPublishCallback, Func<string, string, string, Task> readAndPublishWithFormatCallback = null)
        {
            _topicTimers = new Dictionary<string, Timer>();
            _topicConfigs = new Dictionary<string, TopicReadConfig>();
            _readAndPublishCallback = readAndPublishCallback ?? throw new ArgumentNullException(nameof(readAndPublishCallback));
            _readAndPublishWithFormatCallback = readAndPublishWithFormatCallback;
        }

        /// <summary>
        /// Updates the reading configuration for topics based on the current UI state
        /// </summary>
        /// <param name="readTopicsGrid">The DataGridView containing read topics configuration</param>
        public void UpdateTopicConfigurations(DataGridView readTopicsGrid)
        {
            if (readTopicsGrid == null)
                return;

            // Stop all existing timers
            StopAllTimers();

            // Clear existing configurations
            _topicConfigs.Clear();

            // Process each row in the read topics grid
            foreach (DataGridViewRow row in readTopicsGrid.Rows)
            {
                if (row.IsNewRow) continue;

                string topic = row.Cells["Topico"].Value?.ToString();
                string deviceId = row.Cells["IdDispositivo"].Value?.ToString();
                string modbusAddress = row.Cells["DireccionPLC"].Value?.ToString();
                string refreshRate = row.Cells["TasaRefresco"].Value?.ToString() ?? "10s";

                // Skip if any required field is empty
                if (string.IsNullOrEmpty(topic) || string.IsNullOrEmpty(deviceId) || string.IsNullOrEmpty(modbusAddress))
                    continue;

                // Skip if device ID indicates no configuration or error
                if (deviceId.Contains("No hay configuraciones") || deviceId.Contains("Error"))
                    continue;

                // Extract actual device ID if it contains connection status info - Updated for production-ready device names
                string actualDeviceId = deviceId;
                if (deviceId.Contains("No conectado"))
                {
                    // Extract the actual device ID from "No conectado - PLC-Principal-1 (ConfigName)"
                    var match = System.Text.RegularExpressions.Regex.Match(deviceId, @"No conectado - (.+?) \(");
                    if (match.Success)
                    {
                        actualDeviceId = match.Groups[1].Value;
                    }
                    else
                    {
                        Logger.Log($"TopicReadingManager: Could not extract device ID from: {deviceId}", LogLevel.Warning);
                        continue;
                    }
                }
                else if (deviceId.Contains("Conectado"))
                {
                    // Extract from "Conectado - PLC-Principal-1 (ConfigName)" format
                    var match = System.Text.RegularExpressions.Regex.Match(deviceId, @"Conectado - (.+?) \(");
                    if (match.Success)
                    {
                        actualDeviceId = match.Groups[1].Value;
                    }
                }

                // Parse Modbus address (supports MF{address} format)
                if (!ModbusService.ParseAddress(modbusAddress, out ushort address, out bool isFloatFormat))
                {
                    Logger.Log($"TopicReadingManager: Invalid Modbus address format: {modbusAddress}", LogLevel.Warning);
                    continue;
                }

                // Parse refresh rate to milliseconds
                int intervalMs = ParseRefreshRateToMilliseconds(refreshRate);
                if (intervalMs <= 0)
                {
                    Logger.Log($"TopicReadingManager: Invalid refresh rate: {refreshRate}", LogLevel.Warning);
                    continue;
                }

                // Create topic configuration
                var config = new TopicReadConfig
                {
                    Topic = topic,
                    DeviceId = actualDeviceId, // Use the extracted device ID
                    ModbusAddress = address,
                    RefreshRateMs = intervalMs,
                    IsFloatFormat = isFloatFormat,
                    OriginalAddressString = modbusAddress
                };

                _topicConfigs[topic] = config;

                // Create and start timer for this topic
                CreateTimerForTopic(config);

                Logger.Log($"TopicReadingManager: Configured topic '{topic}' with refresh rate {refreshRate} ({intervalMs}ms)", LogLevel.Info);
            }

            Logger.Log($"TopicReadingManager: Updated configurations for {_topicConfigs.Count} topics", LogLevel.Info);
        }

        /// <summary>
        /// Creates and starts a timer for a specific topic configuration
        /// </summary>
        /// <param name="config">The topic configuration</param>
        private void CreateTimerForTopic(TopicReadConfig config)
        {
            var timer = new Timer();
            timer.Interval = config.RefreshRateMs;
            timer.Tick += async (s, e) =>
            {
                try
                {
                    if (config.IsFloatFormat && _readAndPublishWithFormatCallback != null)
                    {
                        // Use the new method that supports MF format
                        await _readAndPublishWithFormatCallback(config.DeviceId, config.OriginalAddressString, config.Topic);
                    }
                    else
                    {
                        await _readAndPublishCallback(config.DeviceId, config.ModbusAddress, config.Topic);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Log($"TopicReadingManager: Error reading topic '{config.Topic}': {ex.Message}", LogLevel.Error);
                }
            };

            _topicTimers[config.Topic] = timer;
            timer.Start();


        }

        /// <summary>
        /// Starts reading for all configured topics
        /// </summary>
        public void StartReading()
        {
            foreach (var timer in _topicTimers.Values)
            {
                timer.Start();
            }

            Logger.Log($"TopicReadingManager: Started reading for {_topicTimers.Count} topics", LogLevel.Info);
        }

        /// <summary>
        /// Stops reading for all configured topics
        /// </summary>
        public void StopReading()
        {
            StopAllTimers();
            Logger.Log("TopicReadingManager: Stopped reading for all topics", LogLevel.Info);
        }

        /// <summary>
        /// Stops and disposes all timers
        /// </summary>
        private void StopAllTimers()
        {
            foreach (var timer in _topicTimers.Values)
            {
                timer.Stop();
                timer.Dispose();
            }
            _topicTimers.Clear();
        }

        /// <summary>
        /// Parses refresh rate string to milliseconds
        /// </summary>
        /// <param name="refreshRate">Refresh rate string (e.g., "1s", "5s", "1min")</param>
        /// <returns>Interval in milliseconds</returns>
        private int ParseRefreshRateToMilliseconds(string refreshRate)
        {
            if (string.IsNullOrEmpty(refreshRate))
                return 10000; // Default 10 seconds

            refreshRate = refreshRate.Trim().ToLowerInvariant();

            if (refreshRate.EndsWith("s"))
            {
                string numberPart = refreshRate.Substring(0, refreshRate.Length - 1);
                if (int.TryParse(numberPart, out int seconds))
                    return seconds * 1000;
            }
            else if (refreshRate.EndsWith("min"))
            {
                string numberPart = refreshRate.Substring(0, refreshRate.Length - 3);
                if (int.TryParse(numberPart, out int minutes))
                    return minutes * 60 * 1000;
            }

            // Default fallback
            return 10000; // 10 seconds
        }

        /// <summary>
        /// Gets the current reading status
        /// </summary>
        /// <returns>True if any timers are running</returns>
        public bool IsReading()
        {
            return _topicTimers.Values.Any(timer => timer.Enabled);
        }

        /// <summary>
        /// Gets the count of configured topics
        /// </summary>
        /// <returns>Number of configured topics</returns>
        public int GetConfiguredTopicsCount()
        {
            return _topicConfigs.Count;
        }

        /// <summary>
        /// Disposes all resources
        /// </summary>
        public void Dispose()
        {
            StopAllTimers();
        }
    }

    /// <summary>
    /// Configuration for a topic reading operation
    /// </summary>
    internal class TopicReadConfig
    {
        public string Topic { get; set; }
        public string DeviceId { get; set; }
        public ushort ModbusAddress { get; set; }
        public int RefreshRateMs { get; set; }
        public bool IsFloatFormat { get; set; }
        public string OriginalAddressString { get; set; }
    }
}
