﻿using System;

namespace prueba // Asegúrate de que esté en el mismo namespace o que sea accesible
{
    [Serializable]
    public class TopicPublishConfig
    {
        public string TopicName { get; set; }
        public bool IsForRead { get; set; }
        public bool IsForWrite { get; set; }
        public string Read_IdDispositivo { get; set; }
        public string Read_DireccionPLC { get; set; }
        public string Write_IdDispositivo { get; set; }
        public string Write_DireccionPLC { get; set; }
        public string Read_TasaRefresco { get; set; } // Refresh rate for read topics
        public string Write_TasaRefresco { get; set; } // Refresh rate for write topics

        // Constructor por defecto necesario para la deserialización
        public TopicPublishConfig() { }

        public TopicPublishConfig(string topicName)
        {
            TopicName = topicName;
            // Inicializar valores por defecto
            Read_IdDispositivo = "";
            Read_DireccionPLC = "";
            Write_IdDispositivo = "";
            Write_DireccionPLC = "";
            Read_TasaRefresco = "10s"; // Default refresh rate
            Write_TasaRefresco = "Realtime"; // Default refresh rate for write topics
        }
    }
}
