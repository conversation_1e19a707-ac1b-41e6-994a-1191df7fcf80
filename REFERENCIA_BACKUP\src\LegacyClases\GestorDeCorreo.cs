﻿using MailKit.Net.Pop3; // Asumo que lo usas para Pop3Client

using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing; // Added for Color
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using prueba.Services;

namespace prueba.Clases
{
    public class GestorDeCorreo
    {
        private Form1 form;
        private string servidor;
        private int puerto;
        private string usuario;
        private string contraseña;
        private bool usarSsl;

        private bool usarEws;

        private string rutaCarpeta = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Emails");
        private string archivoCorreosProcesados = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "CorreosProcesados.txt");

        // EWS-specific fields
        private string ewsServerUrl;
        private string ewsUsername;
        private string ewsPassword;
        private string ewsDomain;
        private bool ewsUseOAuth;
        private EwsEmailService ewsService;



        public GestorDeCorreo(Form1 mainForm, string servidor, int puerto, string usuario, string contraseña)
        {
            this.form = mainForm ?? throw new ArgumentNullException(nameof(mainForm)); // Assign and check for null
            this.servidor = servidor;
            this.puerto = puerto;
            this.usuario = usuario;
            this.contraseña = contraseña;

            // Initialize EWS as false by default
            this.usarEws = false;

            if (!Directory.Exists(rutaCarpeta))
            {
                Directory.CreateDirectory(rutaCarpeta);
            }

            if (!File.Exists(archivoCorreosProcesados))
            {
                File.Create(archivoCorreosProcesados).Close();
            }
        }

        /// <summary>
        /// Constructor for EWS email processing
        /// </summary>
        public GestorDeCorreo(Form1 mainForm, string ewsServerUrl, string ewsUsername, string ewsPassword, string ewsDomain = "", bool ewsUseOAuth = false)
        {
            this.form = mainForm ?? throw new ArgumentNullException(nameof(mainForm));
            this.usarEws = true;

            // EWS configuration
            this.ewsServerUrl = ewsServerUrl;
            this.ewsUsername = ewsUsername;
            this.ewsPassword = ewsPassword;
            this.ewsDomain = ewsDomain;
            this.ewsUseOAuth = ewsUseOAuth;

            // Initialize EWS service
            this.ewsService = new EwsEmailService(ewsServerUrl, ewsUsername, ewsPassword, ewsDomain, ewsUseOAuth, rutaCarpeta, mainForm);

            if (!Directory.Exists(rutaCarpeta))
            {
                Directory.CreateDirectory(rutaCarpeta);
            }

            if (!File.Exists(archivoCorreosProcesados))
            {
                File.Create(archivoCorreosProcesados).Close();
            }
        }

        public bool ProbarConexion()
        {
            try
            {
                if (usarEws)
                {
                    // Test EWS connection using async method with proper timeout
                    if (ewsService != null)
                    {
                        // Use Task.Run with timeout to avoid deadlocks
                        using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)))
                        {
                            var task = Task.Run(async () => await ewsService.TestConnectionAsync().ConfigureAwait(false), cts.Token);

                            try
                            {
                                var result = task.GetAwaiter().GetResult();
                                if (result)
                                {
                                    Logger.Log("Conexión establecida correctamente con Exchange Web Services (EWS).", LogLevel.Info);
                                }
                                return result;
                            }
                            catch (OperationCanceledException)
                            {
                                Logger.Log("EWS connection test timed out after 30 seconds", LogLevel.Error);
                                return false;
                            }
                        }
                    }
                    else
                    {
                        Logger.Log("EWS service is not initialized.", LogLevel.Error);
                        return false;
                    }
                }
                else
                {
                    // Usar POP3 para probar la conexión
                    using (var clientePop3 = new Pop3Client())
                    {
                        var securityOptions = usarSsl ?
                            MailKit.Security.SecureSocketOptions.SslOnConnect :
                            MailKit.Security.SecureSocketOptions.Auto;

                        clientePop3.Connect(servidor, puerto, securityOptions);
                        clientePop3.Authenticate(usuario, contraseña);

                        Logger.Log("Conexión establecida correctamente con el servidor POP3.", LogLevel.Info);

                        clientePop3.Disconnect(true);
                        return true;
                    }
                }
            }
            catch (System.Exception ex)
            {
                Logger.Log($"Error al conectar: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        /// <summary>
        /// Async version of ProbarConexion for better UI responsiveness
        /// </summary>
        public async Task<bool> ProbarConexionAsync()
        {
            try
            {
                if (usarEws)
                {
                    // Test EWS connection
                    if (ewsService != null)
                    {
                        var result = await ewsService.TestConnectionAsync().ConfigureAwait(false);
                        if (result)
                        {
                            Logger.Log("Conexión establecida correctamente con Exchange Web Services (EWS).", LogLevel.Info);
                        }
                        return result;
                    }
                    else
                    {
                        Logger.Log("EWS service is not initialized.", LogLevel.Error);
                        return false;
                    }
                }
                else
                {
                    // Usar POP3 para probar la conexión de forma asíncrona
                    using (var clientePop3 = new Pop3Client())
                    {
                        var securityOptions = usarSsl ?
                            MailKit.Security.SecureSocketOptions.SslOnConnect :
                            MailKit.Security.SecureSocketOptions.Auto;

                        await clientePop3.ConnectAsync(servidor, puerto, securityOptions).ConfigureAwait(false);
                        await clientePop3.AuthenticateAsync(usuario, contraseña).ConfigureAwait(false);

                        Logger.Log("Conexión establecida correctamente con el servidor POP3.", LogLevel.Info);

                        await clientePop3.DisconnectAsync(true).ConfigureAwait(false);
                        return true;
                    }
                }
            }
            catch (System.Exception ex)
            {
                Logger.Log($"Error al conectar: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        public void ConectarYProcesarCorreos()
        {
            try
            {
                // Verificar conexión antes de procesar correos
                Logger.Log("Verificando conexión antes de procesar correos...", LogLevel.Info);
                bool conexionExitosa = ProbarConexion();

                if (!conexionExitosa)
                {
                    Logger.Log("Error: No se pudo establecer conexión. Cancelando procesamiento de correos.", LogLevel.Error);
                    form.UpdatePanelEstadoColor(Color.Red);
                    form.UpdateEmailStatus(false, "Correo: Error de conexión", true);
                    return;
                }

                Logger.Log("Conexión verificada exitosamente. Iniciando procesamiento de correos...", LogLevel.Info);

                if (usarEws)
                {
                    ProcesarCorreosEWS();
                }
                else
                {
                    ProcesarCorreosPOP3();
                }

                form.UpdatePanelEstadoColor(Color.Green);
                form.UpdateEmailStatus(true, "Correo: Conectado");
                Logger.Log("Proceso de correos finalizado exitosamente.", LogLevel.Info);
            }
            catch (Exception ex)
            {
                Logger.Log($"Error durante el procesamiento de correos: {ex.Message}", LogLevel.Error);
                form.UpdatePanelEstadoColor(Color.Red);
                throw;
            }
        }

        /// <summary>
        /// EWS-specific email processing that updates the EWS status panel
        /// </summary>
        public void ConectarYProcesarCorreosEWS()
        {
            try
            {
                // Verificar conexión antes de procesar correos
                Logger.Log("Verificando conexión EWS antes de procesar correos...", LogLevel.Info);
                bool conexionExitosa = ProbarConexion();

                if (!conexionExitosa)
                {
                    Logger.Log("Error: No se pudo establecer conexión EWS. Cancelando procesamiento de correos.", LogLevel.Error);
                    form.UpdatePanelEwsEstadoColor(Color.Red);
                    form.UpdateEmailStatus(false, "Correo: Error EWS", true);
                    return;
                }

                Logger.Log("Conexión EWS verificada exitosamente. Iniciando procesamiento de correos...", LogLevel.Info);

                ProcesarCorreosEWS();

                form.UpdatePanelEwsEstadoColor(Color.Green);
                form.UpdateEmailStatus(true, "Correo: EWS Conectado");
                Logger.Log("Proceso de correos EWS finalizado exitosamente.", LogLevel.Info);
            }
            catch (Exception ex)
            {
                Logger.Log($"Error durante el procesamiento de correos EWS: {ex.Message}", LogLevel.Error);
                form.UpdatePanelEwsEstadoColor(Color.Red);
                throw;
            }
        }

        private void ProcesarCorreosEWS()
        {
            try
            {
                if (ewsService == null)
                {
                    Logger.Log("EWS service is not initialized.", LogLevel.Error);
                    throw new InvalidOperationException("EWS service is not initialized.");
                }

                Logger.Log("Procesando correos con Exchange Web Services (EWS) usando pipeline unificado", LogLevel.Info);

                // Get processed emails list (same as POP3)
                var correosProcesados = new List<string>();
                if (File.Exists(archivoCorreosProcesados))
                {
                    correosProcesados = File.ReadAllLines(archivoCorreosProcesados).ToList();
                }

                Logger.Log($"EWS Processing: Loaded {correosProcesados.Count} previously processed email IDs from {archivoCorreosProcesados}", LogLevel.Info);

                // Get raw emails from EWS (using synchronous method to avoid deadlocks)
                List<RawEmailData> rawEmails = null;

                // Use direct synchronous call to avoid deadlock issues
                rawEmails = ewsService.GetRawEmails();

                if (rawEmails == null || rawEmails.Count == 0)
                {
                    Logger.Log("EWS Processing: No emails retrieved from EWS service", LogLevel.Warning);
                    return;
                }

                Logger.Log($"EWS: {rawEmails.Count} correos obtenidos para procesamiento", LogLevel.Info);

                // Removed verbose email retrieval logging for production

                int emailsProcesados = 0;
                foreach (var rawEmail in rawEmails)
                {
                    try
                    {
                        // Removed verbose email checking logs for production

                        // Skip already processed emails (same as POP3)
                        if (correosProcesados.Contains(rawEmail.MessageId))
                        {
                            Logger.Log($"EWS: Correo ya procesado - {rawEmail.Subject}", LogLevel.Info);
                            continue;
                        }

                        // Skip feedback emails to prevent infinite loops
                        if (rawEmail.Subject.Contains("Feedback", StringComparison.OrdinalIgnoreCase))
                        {
                            Logger.Log($"EWS: Correo omitido (contiene 'Feedback') - {rawEmail.Subject}", LogLevel.Info);
                            // Mark as processed to avoid checking again
                            File.AppendAllText(archivoCorreosProcesados, rawEmail.MessageId + Environment.NewLine);
                            continue;
                        }

                        // Apply same subject filtering as POP3
                        string subjectUpper = rawEmail.Subject.ToUpper();
                        bool hasLimitacion = subjectUpper.Contains("LIMITACIÓN");
                        bool hasLimitaciones = subjectUpper.Contains("LIMITACIONES");

                        if (!hasLimitacion && !hasLimitaciones)
                        {
                            continue;
                        }

                        Logger.Log($"EWS Processing: Email passed subject filter, proceeding with processing: '{rawEmail.Subject}'", LogLevel.Info);

                        Logger.Log($"Procesando correo EWS: {rawEmail.Subject}", LogLevel.Info);

                        // Use HTML content (same as POP3: mensaje.HtmlBody ?? mensaje.TextBody ?? "")
                        string textoCorreo = rawEmail.HtmlContent;

                        Logger.Log($"EWS Email HTML content length: {textoCorreo?.Length ?? 0}, IsHTML: {textoCorreo?.Contains("<html>") == true || textoCorreo?.Contains("<HTML>") == true}", LogLevel.Debug);

                        if (!string.IsNullOrWhiteSpace(textoCorreo))
                        {
                            // Use same processing pipeline as POP3
                            GuardarCorreoEnHTML(rawEmail.MessageId, rawEmail.Subject, textoCorreo);
                            ProcesarTablaRestricciones(textoCorreo, rawEmail.Subject, rawEmail.Sender);

                            // Mark as processed (same as POP3)
                            File.AppendAllText(archivoCorreosProcesados, rawEmail.MessageId + Environment.NewLine);

                            // Mark email as read on server (using synchronous method to avoid deadlocks)
                            try
                            {
                                ewsService.MarkEmailAsRead(rawEmail.MessageId);
                            }
                            catch (Exception markEx)
                            {
                                Logger.Log($"Error marking email as read (non-critical): {markEx.Message}", LogLevel.Warning);
                                // Continue processing - marking as read is not critical
                            }

                            emailsProcesados++;
                            Logger.Log($"Correo EWS procesado exitosamente: {rawEmail.Subject}", LogLevel.Info);
                        }
                        else
                        {
                            Logger.Log($"Correo EWS sin contenido, omitiendo: {rawEmail.Subject}", LogLevel.Warning);
                        }
                    }
                    catch (Exception emailEx)
                    {
                        Logger.Log($"Error procesando correo individual EWS '{rawEmail.Subject}': {emailEx.Message}", LogLevel.Error);
                        // Continue processing other emails
                    }
                }

                Logger.Log($"Procesamiento EWS completado. Correos procesados: {emailsProcesados}", LogLevel.Info);
            }
            catch (Exception ex)
            {
                Logger.Log($"Error al procesar correos con EWS: {ex.Message}", LogLevel.Error);

                // Reset the EWS service to prevent future hanging
                try
                {
                    ewsService?.ResetService();
                    Logger.Log("EWS service reset after error to prevent hanging", LogLevel.Info);
                }
                catch (Exception resetEx)
                {
                    Logger.Log($"Error resetting EWS service: {resetEx.Message}", LogLevel.Warning);
                }

                throw;
            }
        }



        private void ProcesarCorreosPOP3()
        {
            Logger.Log("Procesando correos con POP3", LogLevel.Info);
            using (var clientePop3 = new Pop3Client())
            {
                try
                {
                    clientePop3.Connect(servidor, puerto, MailKit.Security.SecureSocketOptions.Auto);
                    clientePop3.Authenticate(usuario, contraseña);

                    var correosProcesados = new List<string>();
                    if (File.Exists(archivoCorreosProcesados))
                    {
                        correosProcesados = File.ReadAllLines(archivoCorreosProcesados).ToList();
                    }

                    Logger.Log($"POP3 Processing: Found {clientePop3.Count} total emails in mailbox", LogLevel.Info);

                    for (int i = 0; i < clientePop3.Count; i++)
                    {
                        var mensaje = clientePop3.GetMessage(i);
                        string mensajeId = mensaje.MessageId ?? $"SinID_{DateTime.Now.Ticks}_{i}";
                        string asunto = mensaje.Subject ?? "";

                        Logger.Log($"POP3 Processing: Checking email '{asunto}' (MessageId: {mensajeId.Substring(0, Math.Min(20, mensajeId.Length))}...)", LogLevel.Info);

                        if (correosProcesados.Contains(mensajeId))
                        {
                            Logger.Log($"POP3 Processing: Email already processed, skipping: '{asunto}'", LogLevel.Info);
                            continue;
                        }

                        // Skip feedback emails to prevent infinite loops
                        if (asunto.Contains("Feedback", StringComparison.OrdinalIgnoreCase))
                        {
                            Logger.Log($"POP3 Email skipped - contains 'Feedback' in subject: {asunto}", LogLevel.Info);
                            // Mark as processed to avoid checking again
                            File.AppendAllText(archivoCorreosProcesados, mensajeId + Environment.NewLine);
                            continue;
                        }

                        // Apply subject filtering
                        string subjectUpper = asunto.ToUpper();
                        bool hasLimitacion = subjectUpper.Contains("LIMITACIÓN");
                        bool hasLimitaciones = subjectUpper.Contains("LIMITACIONES");

                        if (!hasLimitacion && !hasLimitaciones)
                        {
                            continue;
                        }

                        string textoCorreo = mensaje.HtmlBody ?? mensaje.TextBody ?? "";



                        if (!string.IsNullOrWhiteSpace(textoCorreo))
                        {
                            string remitente = mensaje.From?.FirstOrDefault()?.ToString() ?? "";
                            GuardarCorreoEnHTML(mensajeId, asunto, textoCorreo);
                            ProcesarTablaRestricciones(textoCorreo, asunto, remitente);

                            File.AppendAllText(archivoCorreosProcesados, mensajeId + Environment.NewLine);
                        }
                    }

                    clientePop3.Disconnect(true);
                }
                catch (System.Exception ex)
                {
                    Logger.Log($"Error al procesar correos con POP3: {ex.Message}", LogLevel.Error);
                    throw;
                }
            }
        }

        private void GuardarCorreoEnHTML(string mensajeId, string asunto, string contenido)
        {
            // Sanear mensajeId para que sea un nombre de archivo válido
            string saneMensajeId = string.Join("_", mensajeId.Split(Path.GetInvalidFileNameChars()));
            string nombreArchivo = Path.Combine(rutaCarpeta, $"{saneMensajeId}.html");

            try
            {
                using (StreamWriter escritor = new StreamWriter(nombreArchivo))
                {
                    escritor.WriteLine("<html><head><meta charset='UTF-8'><title>Correo Guardado</title></head><body>");
                    escritor.WriteLine($"<h1>Asunto: {System.Net.WebUtility.HtmlEncode(asunto)}</h1>"); // Codificar para seguridad
                    escritor.WriteLine($"<p>Fecha de guardado: {DateTime.Now}</p>");
                    escritor.WriteLine("<hr>");
                    escritor.WriteLine(contenido); // Asumimos que el contenido ya es HTML o texto plano seguro
                    escritor.WriteLine("</body></html>");
                }
                // MessageBox.Show($"Correo '{saneMensajeId}' guardado exitosamente.", "Correo Guardado", MessageBoxButtons.OK, MessageBoxIcon.Information); // Opcional
                Logger.Log($"Correo '{saneMensajeId}' guardado exitosamente.", LogLevel.Debug);
            }
            catch (System.Exception ex)
            {
                Logger.Log($"Error al guardar el correo '{saneMensajeId}': {ex.Message}", LogLevel.Error);
            }
        }

        private void ProcesarTablaRestricciones(string htmlCorreo, string originalSubject = "", string originalSender = "")
        {
            if (string.IsNullOrWhiteSpace(htmlCorreo))
            {
                return;
            }

            List<string> analyzedData = new List<string>();

            try
            {
                // Buscar tablas de forma secuencial y controlada
                var regexTabla = new Regex(@"<table[^>]*>(.*?)</table>", RegexOptions.Singleline | RegexOptions.IgnoreCase);
                var tablasMatches = regexTabla.Matches(htmlCorreo);

                if (tablasMatches.Count == 0)
                {
                    Logger.Log("No se encontró ninguna tabla en el correo", LogLevel.Warning);
                    return;
                }

                // Procesar solo las primeras 3 tablas para evitar bucles infinitos
                int maxTablasAProcesar = Math.Min(tablasMatches.Count, 3);

                for (int i = 0; i < maxTablasAProcesar; i++)
                {
                    string contenidoTabla = tablasMatches[i].Groups[1].Value;

                    // Verificación rápida y segura: debe contener al menos H1, H2, H3
                    if (!contenidoTabla.Contains("H1") || !contenidoTabla.Contains("H2") || !contenidoTabla.Contains("H3"))
                    {
                        continue; // Saltar esta tabla y probar la siguiente
                    }

                    // Intentar procesar esta tabla
                    if (ProcesarTablaIndividual(contenidoTabla, out analyzedData))
                    {
                        // Tabla procesada exitosamente, enviar correo con datos analizados
                        Logger.Log($"Tabla procesada exitosamente. Datos analizados: {string.Join(", ", analyzedData)}", LogLevel.Info);

                        // Send email with analyzed data if using EWS
                        if (usarEws && ewsService != null)
                        {
                            _ = Task.Run(async () =>
                            {
                                try
                                {
                                    bool emailSent = await ewsService.SendAnalyzedDataEmailAsync(analyzedData, originalSubject, originalSender);
                                    if (emailSent)
                                    {
                                        Logger.Log("Correo con datos analizados enviado exitosamente", LogLevel.Info);
                                    }
                                    else
                                    {
                                        Logger.Log("Error al enviar correo con datos analizados", LogLevel.Warning);
                                    }
                                }
                                catch (Exception emailEx)
                                {
                                    Logger.Log($"Error enviando correo con datos analizados: {emailEx.Message}", LogLevel.Error);
                                }
                            });
                        }

                        return;
                    }
                }

                Logger.Log("No se encontró ninguna tabla válida con encabezados de hora", LogLevel.Warning);
            }
            catch (Exception ex)
            {
                Logger.Log($"Error procesando tablas de restricciones: {ex.Message}", LogLevel.Error);
            }
        }

        private bool ProcesarTablaIndividual(string contenidoTabla, out List<string> analyzedData)
        {
            analyzedData = new List<string>();

            try
            {
                // Regex simple para encabezados - busca H1, H2, etc. en celdas
                var regexEncabezados = new Regex(@"<t[hd][^>]*>.*?(H\d+).*?</t[hd]>", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                var encabezadosMatches = regexEncabezados.Matches(contenidoTabla);

                if (encabezadosMatches.Count == 0)
                {
                    return false;
                }

                List<string> nombresColumnas = new List<string>();
                // Límite de seguridad: máximo 30 encabezados para evitar bucles infinitos
                int maxEncabezados = Math.Min(encabezadosMatches.Count, 30);

                for (int i = 0; i < maxEncabezados; i++)
                {
                    string nombreColumna = encabezadosMatches[i].Groups[1].Value.Trim();
                    nombreColumna = System.Net.WebUtility.HtmlDecode(nombreColumna);

                    if (!string.IsNullOrWhiteSpace(nombreColumna))
                    {
                        nombresColumnas.Add(nombreColumna);
                    }
                }

                if (nombresColumnas.Count == 0)
                {
                    return false;
                }

                // Regex simple para capturar contenido en celdas <td>
                var regexValores = new Regex(@"<td[^>]*>(.*?)</td>", RegexOptions.IgnoreCase | RegexOptions.Singleline);

                // Procesar las filas <tr> de forma controlada
                var regexFilas = new Regex(@"<tr[^>]*>(.*?)</tr>", RegexOptions.Singleline | RegexOptions.IgnoreCase);
                var filasMatches = regexFilas.Matches(contenidoTabla);

                List<string> columnasConValor = new List<string>();

                // Límite de seguridad: máximo 10 filas para evitar bucles infinitos
                int maxFilas = Math.Min(filasMatches.Count, 10);
                bool encabezadosYaProcesados = false;

                for (int f = 0; f < maxFilas; f++)
                {
                    string contenidoFila = filasMatches[f].Groups[1].Value;

                    // Si esta fila contiene encabezados, la saltamos
                    if ((contenidoFila.Contains("<th") || contenidoFila.Contains("H1") || contenidoFila.Contains("H2")) && !encabezadosYaProcesados)
                    {
                        encabezadosYaProcesados = true;
                        continue;
                    }

                    var valoresMatchesEnFila = regexValores.Matches(contenidoFila);
                    if (valoresMatchesEnFila.Count > 0)
                    {
                        // Procesar los valores de esta fila con límite de seguridad
                        int maxColumnas = Math.Min(valoresMatchesEnFila.Count, nombresColumnas.Count);
                        for (int i = 0; i < maxColumnas; i++)
                        {
                            string valorCelda = valoresMatchesEnFila[i].Groups[1].Value;

                            // Limpiar HTML tags y decodificar
                            valorCelda = System.Text.RegularExpressions.Regex.Replace(valorCelda, @"<[^>]*>", "");
                            valorCelda = System.Net.WebUtility.HtmlDecode(valorCelda).Trim();

                            if (!string.IsNullOrWhiteSpace(valorCelda))
                            {
                                columnasConValor.Add(nombresColumnas[i]);
                            }
                        }

                        // Procesar solo la primera fila de datos válida
                        break;
                    }
                }

                if (columnasConValor.Count > 0)
                {
                    string columnasFormateadas = "Columnas con valores:\n" + string.Join(" ", columnasConValor);
                    Logger.Log(columnasFormateadas, LogLevel.Info);
                    UpdateStates(columnasConValor);

                    // Set the analyzed data for email sending
                    analyzedData = new List<string>(columnasConValor);

                    return true; // Tabla procesada exitosamente
                }
                else
                {
                    return false; // No se encontraron datos válidos
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error procesando tabla individual: {ex.Message}", LogLevel.Error);
                return false;
            }
        }
        private void ProcessHourlyStates(List<string> columnasConValor)
        {
            var states = new bool[24];
            foreach (var columna in columnasConValor)
            {
                if (columna.StartsWith("H", StringComparison.OrdinalIgnoreCase))
                {
                    if (int.TryParse(columna.Substring(1), out int hour) && hour >= 1 && hour <= 24)
                    {
                        states[hour - 1] = true;
                        Logger.Log("Hora:" + hour, LogLevel.Debug);
                        //MessageBox.Show("Hora:" + hour , "Tabla intermedia", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }

            // Guardar estados en CSV
            SaveStatesToCsv(states);
        }

        private string csvPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "HourlyStates.csv");

        private void SaveStatesToCsv(bool[] states)
        {
            var tomorrow = DateTime.Now.Date.AddDays(1);
            var lines = new List<string>
        {
            $"{tomorrow.ToString("yyyy-MM-dd")},{string.Join(",", states.Select(s => s ? "1" : "0"))}"
        };

            File.WriteAllLines(csvPath, lines);
        }

        private (HourlyState Today, HourlyState Tomorrow) LoadStatesFromCsv()
        {
            var today = new HourlyState { Date = DateTime.Now.Date };
            var tomorrow = new HourlyState { Date = DateTime.Now.Date.AddDays(1) };

            if (File.Exists(csvPath))
            {
                var lines = File.ReadAllLines(csvPath);
                foreach (var line in lines)
                {
                    var parts = line.Split(',');
                    if (parts.Length == 25) // fecha + 24 horas
                    {
                        if (DateTime.TryParse(parts[0], out DateTime date))
                        {
                            var states = parts.Skip(1).Select(s => s == "1").ToArray();
                            if (date.Date == today.Date)
                                today.HourStates = states;
                            else if (date.Date == tomorrow.Date)
                                tomorrow.HourStates = states;
                        }
                    }
                }
            }

            return (today, tomorrow);
        }

        public (HourlyState Today, HourlyState Tomorrow) GetCurrentStates()
        {
            return LoadStatesFromCsv();
        }

        public void UpdateStates(List<string> columnas)
        {
            ProcessHourlyStates(columnas);
            var states = LoadStatesFromCsv();
            var tomorrowStates = states.Tomorrow;
        }

    }
}
