﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Windows.Forms; // Para MessageBox

namespace prueba
{
    public class TopicValueStorage
    {
        private readonly string appFolderPath;
        private bool isInitializedSuccessfully = false;

        public TopicValueStorage()
        {
            try
            {
                string appData = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                appFolderPath = Path.Combine(appData, "PruebaMQTT");

                if (!Directory.Exists(appFolderPath))
                {
                    Directory.CreateDirectory(appFolderPath);
                }
                isInitializedSuccessfully = true;
            }
            catch (Exception ex)
            {
                isInitializedSuccessfully = false;
                MessageBox.Show($"Error crítico inicializando la carpeta de almacenamiento de tópicos: {ex.Message}\n" +
                                "La funcionalidad de guardar/cargar el estado de los tópicos no estará disponible.",
                                "Error de Inicialización", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GetFilePathForProfile(string profileIdentifier)
        {
            if (string.IsNullOrEmpty(profileIdentifier))
            {
                // Podríamos usar un nombre por defecto o no permitir guardado sin identificador.
                // Por ahora, no guardaremos si no hay identificador.
                return null;
            }
            // Sanear el identificador para que sea un nombre de archivo válido
            string saneIdentifier = string.Join("_", profileIdentifier.Split(Path.GetInvalidFileNameChars()));
            return Path.Combine(appFolderPath, $"topics_{saneIdentifier}.csv");
        }

        public void Save(Dictionary<string, string> topicValues, string profileIdentifier)
        {
            if (!isInitializedSuccessfully) return;

            string filePath = GetFilePathForProfile(profileIdentifier);
            if (string.IsNullOrEmpty(filePath))
            {
                // Opcional: Notificar que no se pueden guardar tópicos sin un perfil activo/identificador.
                // MessageBox.Show("No se especificó un perfil para guardar los tópicos.", "Guardado de Tópicos", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                var lines = new List<string>();
                lines.Add("Topic,Value");

                foreach (var kvp in topicValues)
                {
                    string topicEscaped = EscapeCsvField(kvp.Key);
                    string valueEscaped = EscapeCsvField(kvp.Value);
                    lines.Add($"{topicEscaped},{valueEscaped}");
                }
                File.WriteAllLines(filePath, lines, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error guardando valores de tópicos para el perfil '{profileIdentifier}': {ex.Message}", "Error al Guardar", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public Dictionary<string, string> Load(string profileIdentifier)
        {
            var result = new Dictionary<string, string>();
            if (!isInitializedSuccessfully) return result;

            string filePath = GetFilePathForProfile(profileIdentifier);
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
            {
                return result;
            }

            try
            {
                var lines = File.ReadAllLines(filePath, Encoding.UTF8);
                if (lines.Length <= 1) return result;

                for (int i = 1; i < lines.Length; i++)
                {
                    string line = lines[i];
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var fields = ParseCsvLine(line);
                    if (fields.Length >= 2)
                    {
                        string topic = UnescapeCsvField(fields[0]);
                        string value = UnescapeCsvField(fields[1]);
                        result[topic] = value;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error cargando valores de tópicos para el perfil '{profileIdentifier}': {ex.Message}", "Error al Cargar", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            return result;
        }

        // Métodos EscapeCsvField, UnescapeCsvField, ParseCsvLine (sin cambios de la versión anterior)
        private string EscapeCsvField(string field)
        {
            if (field == null) return "";
            if (field.Contains(",") || field.Contains("\"") || field.Contains("\n") || field.Contains("\r"))
            {
                return "\"" + field.Replace("\"", "\"\"") + "\"";
            }
            return field;
        }

        private string UnescapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field)) return field;
            if (field.StartsWith("\"") && field.EndsWith("\"") && field.Length >= 2)
            {
                string unquoted = field.Substring(1, field.Length - 2);
                return unquoted.Replace("\"\"", "\"");
            }
            return field;
        }

        private string[] ParseCsvLine(string line)
        {
            var fields = new List<string>();
            if (string.IsNullOrEmpty(line)) return fields.ToArray();
            var currentField = new StringBuilder();
            bool inQuotes = false;
            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];
                if (inQuotes)
                {
                    if (c == '"')
                    {
                        if (i + 1 < line.Length && line[i + 1] == '"')
                        {
                            currentField.Append('"');
                            i++;
                        }
                        else
                        {
                            inQuotes = false;
                        }
                    }
                    else
                    {
                        currentField.Append(c);
                    }
                }
                else
                {
                    if (c == ',')
                    {
                        fields.Add(currentField.ToString());
                        currentField.Clear();
                    }
                    else if (c == '"' && currentField.Length == 0)
                    {
                        inQuotes = true;
                    }
                    else
                    {
                        currentField.Append(c);
                    }
                }
            }
            fields.Add(currentField.ToString());
            return fields.ToArray();
        }
    }
}
