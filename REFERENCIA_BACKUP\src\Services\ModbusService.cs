using System;
using System.Collections.Generic;
using System.IO; // Added for IOException
using System.IO.Ports;
using System.Linq;
using System.Net.Sockets;
using System.Threading.Tasks;
// NOTE: This class requires the System.IO.Ports NuGet package.
// Install it using: Install-Package System.IO.Ports

namespace prueba.Services
{
    /// <summary>
    /// Service that handles Modbus communication (RTU and TCP) with configurable settings.
    /// </summary>
    public class ModbusService : IDisposable
    {
        #region Enums and Constants

        /// <summary>
        /// Defines the type of Modbus connection to use
        /// </summary>
        public enum ModbusConnectionType
        {
            /// <summary>Modbus TCP connection (Ethernet)</summary>
            TCP,
            /// <summary>Modbus RTU connection (Serial)</summary>
            RTU
        }

        /// <summary>
        /// Defines the Modbus function codes supported
        /// </summary>
        public enum ModbusFunctionCode : byte
        {
            /// <summary>Read Coils (00xxxx)</summary>
            ReadCoils = 0x01,
            /// <summary>Read Discrete Inputs (10xxxx)</summary>
            ReadDiscreteInputs = 0x02,
            /// <summary>Read Holding Registers (40xxxx)</summary>
            ReadHoldingRegisters = 0x03,
            /// <summary>Read Input Registers (30xxxx)</summary>
            ReadInputRegisters = 0x04,
            /// <summary>Write Single Coil (00xxxx)</summary>
            WriteSingleCoil = 0x05,
            /// <summary>Write Single Register (40xxxx)</summary>
            WriteSingleRegister = 0x06,
            /// <summary>Write Multiple Coils (00xxxx)</summary>
            WriteMultipleCoils = 0x0F,
            /// <summary>Write Multiple Registers (40xxxx)</summary>
            WriteMultipleRegisters = 0x10
        }

        // Default settings
        private const int DEFAULT_TCP_PORT = 502;
        private const int DEFAULT_TIMEOUT_MS = 1000;
        private const int DEFAULT_RETRY_COUNT = 3;
        private const int DEFAULT_RETRY_DELAY_MS = 100;

        #endregion

        #region Configuration Properties

        /// <summary>
        /// Gets or sets the Modbus connection type (TCP or RTU)
        /// </summary>
        public ModbusConnectionType ConnectionType { get; set; } = ModbusConnectionType.TCP;

        /// <summary>
        /// Gets or sets the Modbus device ID (slave address)
        /// </summary>
        public byte DeviceId { get; set; } = 1;

        // TCP Configuration
        /// <summary>
        /// Gets or sets the IP address or hostname for TCP connections
        /// </summary>
        public string IpAddress { get; set; } = "127.0.0.1";

        /// <summary>
        /// Gets or sets the TCP port number (default is 502)
        /// </summary>
        public int Port { get; set; } = DEFAULT_TCP_PORT;

        // RTU Configuration
        /// <summary>
        /// Gets or sets the COM port name for RTU connections (e.g., "COM1")
        /// </summary>
        public string ComPort { get; set; } = "COM1";

        /// <summary>
        /// Gets or sets the baud rate for RTU connections
        /// </summary>
        public int BaudRate { get; set; } = 9600;

        /// <summary>
        /// Gets or sets the parity for RTU connections
        /// </summary>
        public Parity Parity { get; set; } = Parity.None;

        /// <summary>
        /// Gets or sets the data bits for RTU connections
        /// </summary>
        public int DataBits { get; set; } = 8;

        /// <summary>
        /// Gets or sets the stop bits for RTU connections
        /// </summary>
        public StopBits StopBits { get; set; } = StopBits.One;

        // General Configuration
        /// <summary>
        /// Gets or sets the communication timeout in milliseconds
        /// </summary>
        public int TimeoutMs { get; set; } = DEFAULT_TIMEOUT_MS;

        /// <summary>
        /// Gets or sets the number of retry attempts for failed communications
        /// </summary>
        public int RetryCount { get; set; } = DEFAULT_RETRY_COUNT;

        /// <summary>
        /// Gets or sets the delay between retry attempts in milliseconds
        /// </summary>
        public int RetryDelayMs { get; set; } = DEFAULT_RETRY_DELAY_MS;

        #endregion

        #region Private Fields

        private TcpClient tcpClient;
        private SerialPort serialPort;
        private bool isConnected = false;
        private object communicationLock = new object();

        #endregion

        #region Events

        /// <summary>
        /// Event triggered when connection status changes
        /// </summary>
        public event EventHandler<bool> ConnectionStatusChanged;

        /// <summary>
        /// Event triggered when communication error occurs
        /// </summary>
        public event EventHandler<Exception> CommunicationError;

        /// <summary>
        /// Event triggered when data is received
        /// </summary>
        public event EventHandler<ModbusDataReceivedEventArgs> DataReceived;

        #endregion

        #region Constructors and Initialization

        /// <summary>
        /// Initializes a new instance of the ModbusService with default settings
        /// </summary>
        public ModbusService()
        {
            // Uses default settings
        }

        /// <summary>
        /// Initializes a new instance of the ModbusService for TCP connection
        /// </summary>
        /// <param name="ipAddress">IP address of the Modbus TCP server</param>
        /// <param name="port">TCP port number</param>
        /// <param name="deviceId">Modbus device ID (slave address)</param>
        public ModbusService(string ipAddress, int port = DEFAULT_TCP_PORT, byte deviceId = 1)
        {
            ConnectionType = ModbusConnectionType.TCP;
            IpAddress = ipAddress;
            Port = port;
            DeviceId = deviceId;
        }

        /// <summary>
        /// Initializes a new instance of the ModbusService for RTU connection
        /// </summary>
        /// <param name="comPort">COM port name (e.g., "COM1")</param>
        /// <param name="baudRate">Baud rate</param>
        /// <param name="deviceId">Modbus device ID (slave address)</param>
        /// <param name="parity">Parity (default: None)</param>
        /// <param name="dataBits">Data bits (default: 8)</param>
        /// <param name="stopBits">Stop bits (default: One)</param>
        public ModbusService(string comPort, int baudRate, byte deviceId = 1,
            Parity parity = Parity.None,
            int dataBits = 8,
            StopBits stopBits = StopBits.One)
        {
            ConnectionType = ModbusConnectionType.RTU;
            ComPort = comPort;
            BaudRate = baudRate;
            DeviceId = deviceId;
            Parity = parity;
            DataBits = dataBits;
            StopBits = stopBits;
        }

        #endregion

        #region Connection Management

        /// <summary>
        /// Gets a value indicating whether the connection is established
        /// </summary>
        public bool IsConnected => isConnected;

        /// <summary>
        /// Opens the connection to the Modbus device
        /// </summary>
        /// <returns>True if connection was successful, false otherwise</returns>
        public async Task<bool> ConnectAsync()
        {
            Logger.Log($"ModbusService: Attempting to connect. Type: {ConnectionType}", LogLevel.Info);
            try
            {
                if (isConnected)
                {
                    Logger.Log("ModbusService: Already connected, disconnecting first.", LogLevel.Info);
                    await DisconnectAsync();
                }

                if (ConnectionType == ModbusConnectionType.TCP)
                {
                    tcpClient = new TcpClient();
                    await tcpClient.ConnectAsync(IpAddress, Port);
                    tcpClient.ReceiveTimeout = TimeoutMs;
                    tcpClient.SendTimeout = TimeoutMs;
                    Logger.Log("ModbusService: TCP connection established.", LogLevel.Info);
                }
                else // RTU
                {
                    serialPort = new SerialPort(ComPort, BaudRate, Parity, DataBits, StopBits)
                    {
                        ReadTimeout = TimeoutMs,
                        WriteTimeout = TimeoutMs
                    };

                    serialPort.Open();
                    Logger.Log("ModbusService: Conexión RTU establecida.", LogLevel.Info);
                }

                isConnected = true;
                OnConnectionStatusChanged(true);
                return true;
            }
            catch (Exception ex)
            {
                Logger.Log($"ModbusService: Conexión falló. Tipo: {ConnectionType}, Error: {ex.Message}", LogLevel.Error);
                OnCommunicationError(ex);
                return false;
            }
        }

        /// <summary>
        /// Closes the connection to the Modbus device
        /// </summary>
        /// <returns>Task representing the asynchronous operation</returns>
        public async Task DisconnectAsync()
        {
            Logger.Log("ModbusService: Attempting to disconnect.", LogLevel.Info);
            if (!isConnected)
            {
                Logger.Log("ModbusService: Already disconnected.", LogLevel.Info);
                return;
            }

            try
            {
                if (ConnectionType == ModbusConnectionType.TCP && tcpClient != null)
                {
                    tcpClient.Close();
                    tcpClient.Dispose();
                    tcpClient = null;
                    Logger.Log("ModbusService: TCP connection closed.", LogLevel.Info);
                }
                else if (ConnectionType == ModbusConnectionType.RTU && serialPort != null && serialPort.IsOpen)
                {
                    serialPort.Close();
                    serialPort.Dispose();
                    serialPort = null;
                    Logger.Log("ModbusService: RTU connection closed.", LogLevel.Info);
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"ModbusService: Error during disconnection. Error: {ex.Message}", LogLevel.Error);
                // We still want to mark as disconnected and notify
            }
            finally
            {
                isConnected = false;
                OnConnectionStatusChanged(false);
            }
        }

        #endregion

        #region Read Operations

        /// <summary>
        /// Reads coils from the Modbus device
        /// </summary>
        /// <param name="startAddress">Starting address (0-based)</param>
        /// <param name="count">Number of coils to read</param>
        /// <returns>Array of boolean values representing coil states</returns>
        public async Task<bool[]> ReadCoilsAsync(ushort startAddress, ushort count)
        {
            return await ReadBooleanValuesAsync(ModbusFunctionCode.ReadCoils, startAddress, count);
        }

        /// <summary>
        /// Reads discrete inputs from the Modbus device
        /// </summary>
        /// <param name="startAddress">Starting address (0-based)</param>
        /// <param name="count">Number of discrete inputs to read</param>
        /// <returns>Array of boolean values representing input states</returns>
        public async Task<bool[]> ReadDiscreteInputsAsync(ushort startAddress, ushort count)
        {
            return await ReadBooleanValuesAsync(ModbusFunctionCode.ReadDiscreteInputs, startAddress, count);
        }

        /// <summary>
        /// Reads holding registers from the Modbus device
        /// </summary>
        /// <param name="startAddress">Starting address (direct address as used in Python client)</param>
        /// <param name="count">Number of registers to read</param>
        /// <returns>Array of ushort values representing register values</returns>
        public async Task<ushort[]> ReadHoldingRegistersAsync(ushort startAddress, ushort count)
        {
            // Use address directly like Python client - no conversion needed
            return await ReadRegistersAsync(ModbusFunctionCode.ReadHoldingRegisters, startAddress, count);
        }

        /// <summary>
        /// Simple test method that replicates Python client behavior exactly
        /// Tests reading register 400 with unit ID 1 like the working Python client
        /// </summary>
        /// <returns>Register value or null if error</returns>
        public async Task<ushort?> TestReadRegister400()
        {
            try
            {
                Logger.Log("ModbusService: Iniciando prueba de lectura registro 400 (como Python)", LogLevel.Info);

                // Exactly like Python: read_holding_registers(400, count=1, slave=1)
                var result = await ReadHoldingRegistersAsync(400, 1);

                if (result != null && result.Length > 0)
                {
                    ushort value = result[0];
                    Logger.Log($"ModbusService: ✅ Registro 400 leído exitosamente: {value}", LogLevel.Success);
                    return value;
                }
                else
                {
                    Logger.Log("ModbusService: ❌ No se recibieron datos del registro 400", LogLevel.Error);
                    return null;
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"ModbusService: ❌ Error leyendo registro 400: {ex.Message}", LogLevel.Error);
                return null;
            }
        }

        /// <summary>
        /// Reads input registers from the Modbus device
        /// </summary>
        /// <param name="startAddress">Starting address (0-based)</param>
        /// <param name="count">Number of registers to read</param>
        /// <returns>Array of ushort values representing register values</returns>
        public async Task<ushort[]> ReadInputRegistersAsync(ushort startAddress, ushort count)
        {
            return await ReadRegistersAsync(ModbusFunctionCode.ReadInputRegisters, startAddress, count);
        }

        /// <summary>
        /// Reads a single holding register and converts it to the specified type
        /// </summary>
        /// <typeparam name="T">Type to convert the register value to</typeparam>
        /// <param name="address">Register address (0-based)</param>
        /// <returns>Value converted to the specified type</returns>
        public async Task<T> ReadHoldingRegisterAsync<T>(ushort address)
        {
            if (typeof(T) == typeof(string)) // Assuming string from 2 registers
            {
                var registers = await ReadHoldingRegistersAsync(address, 2);
                if (registers != null && registers.Length == 2)
                {
                    // Example: Combine two ushorts into a string (simple ASCII, needs refinement for other encodings)
                    byte[] bytes = BitConverter.GetBytes(registers[0]).Concat(BitConverter.GetBytes(registers[1])).ToArray();
                    // This is a simplistic conversion, real string conversion might be more complex based on PLC/device
                    string result = System.Text.Encoding.ASCII.GetString(bytes.Where(b => b != 0).ToArray()); // Remove null terminators
                    Logger.Log($"ModbusService: Leída cadena '{result}' de registros {address}, {address + 1}", LogLevel.Info);
                    return (T)(object)result;
                }
                Logger.Log($"ModbusService: Falló lectura de 2 registros para cadena en dirección {address}", LogLevel.Warning);
                return default(T);
            }
            else
            {
                var registers = await ReadHoldingRegistersAsync(address, 1);
                if (registers != null && registers.Length > 0)
                {
                    T value = ConvertRegisterValue<T>(registers[0]);
                    Logger.Log($"ModbusService: Leído registro {address}, Valor: {value}", LogLevel.Info);
                    return value;
                }
                Logger.Log($"ModbusService: Falló lectura de registro en dirección {address}", LogLevel.Warning);
                return default(T);
            }
        }

        /// <summary>
        /// Reads holding registers with support for MF{address} format for floats
        /// </summary>
        /// <param name="addressString">Address string (e.g., "400" or "MF400")</param>
        /// <returns>Object containing either ushort (for standard) or float (for MF format)</returns>
        public async Task<object> ReadHoldingRegisterWithFormatAsync(string addressString)
        {
            if (!ParseAddress(addressString, out ushort actualAddress, out bool isFloatFormat))
            {
                Logger.Log($"ModbusService: Formato de dirección inválido: {addressString}", LogLevel.Error);
                return null;
            }

            if (isFloatFormat)
            {
                // MF format: read 2 consecutive registers and convert to float
                var registers = await ReadHoldingRegistersAsync(actualAddress, 2);
                if (registers != null && registers.Length == 2)
                {
                    float floatValue = ConvertRegistersToFloat(registers[0], registers[1]);
                    Logger.Log($"ModbusService: Leído float MF{addressString.Substring(2)} desde registros {actualAddress},{actualAddress + 1}: {floatValue}", LogLevel.Info);
                    return floatValue;
                }
                Logger.Log($"ModbusService: Falló lectura de float MF{addressString.Substring(2)} desde dirección {actualAddress}", LogLevel.Warning);
                return null;
            }
            else
            {
                // Standard format: read single register
                var registers = await ReadHoldingRegistersAsync(actualAddress, 1);
                if (registers != null && registers.Length > 0)
                {
                    Logger.Log($"ModbusService: Leído registro {actualAddress}: {registers[0]}", LogLevel.Info);
                    return registers[0];
                }
                Logger.Log($"ModbusService: Falló lectura de registro {actualAddress}", LogLevel.Warning);
                return null;
            }
        }

        /// <summary>
        /// Writes to holding registers with support for MF{address} format for floats
        /// </summary>
        /// <param name="addressString">Address string (e.g., "400" or "MF400")</param>
        /// <param name="value">Value to write (ushort for standard, float for MF format)</param>
        /// <returns>True if successful</returns>
        public async Task<bool> WriteHoldingRegisterWithFormatAsync(string addressString, object value)
        {
            if (!ParseAddress(addressString, out ushort actualAddress, out bool isFloatFormat))
            {
                Logger.Log($"ModbusService: Formato de dirección inválido: {addressString}", LogLevel.Error);
                return false;
            }

            if (isFloatFormat)
            {
                // MF format: convert float to 2 registers and write
                if (value is float floatValue)
                {
                    ushort[] registers = ConvertFloatToRegisters(floatValue);
                    bool success = await WriteMultipleRegistersAsync(actualAddress, registers);
                    if (success)
                    {
                        Logger.Log($"ModbusService: Escrito float MF{addressString.Substring(2)} a registros {actualAddress},{actualAddress + 1}: {floatValue}", LogLevel.Info);
                    }
                    return success;
                }
                else if (float.TryParse(value?.ToString(), out floatValue))
                {
                    ushort[] registers = ConvertFloatToRegisters(floatValue);
                    bool success = await WriteMultipleRegistersAsync(actualAddress, registers);
                    if (success)
                    {
                        Logger.Log($"ModbusService: Escrito float MF{addressString.Substring(2)} a registros {actualAddress},{actualAddress + 1}: {floatValue}", LogLevel.Info);
                    }
                    return success;
                }
                else
                {
                    Logger.Log($"ModbusService: Valor inválido para float MF{addressString.Substring(2)}: {value}", LogLevel.Error);
                    return false;
                }
            }
            else
            {
                // Standard format: write single register
                if (value is ushort ushortValue)
                {
                    return await WriteSingleRegisterAsync(actualAddress, ushortValue);
                }
                else if (ushort.TryParse(value?.ToString(), out ushortValue))
                {
                    return await WriteSingleRegisterAsync(actualAddress, ushortValue);
                }
                else
                {
                    Logger.Log($"ModbusService: Valor inválido para registro {actualAddress}: {value}", LogLevel.Error);
                    return false;
                }
            }
        }

        #endregion

        #region Write Operations

        /// <summary>
        /// Writes a single coil to the Modbus device
        /// </summary>
        /// <param name="address">Coil address (0-based)</param>
        /// <param name="value">Value to write (true = ON, false = OFF)</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> WriteSingleCoilAsync(ushort address, bool value)
        {
            Logger.Log($"Modbus: Escribiendo bobina {address} = {value}", LogLevel.Info);
            byte[] request = CreateModbusRequest(ModbusFunctionCode.WriteSingleCoil, address, (ushort)(value ? 0xFF00 : 0x0000));
            try
            {
                byte[] response = await SendRequestAsync(request);
                // Minimal validation for single write: response should echo request for address and value
                // More robust validation would check the entire response structure if necessary
                bool success = response != null && response.Length >= 6 && // Basic length check for TCP
                               response[0] == DeviceId && // For RTU, this is part of the frame; for TCP MBAP, this is a unit ID
                               response[1] == (byte)ModbusFunctionCode.WriteSingleCoil &&
                               BitConverter.ToUInt16(response.Skip(2).Take(2).Reverse().ToArray(), 0) == address &&
                               BitConverter.ToUInt16(response.Skip(4).Take(2).Reverse().ToArray(), 0) == (ushort)(value ? 0xFF00 : 0x0000);

                if (success) Logger.Log($"ModbusService: Bobina Individual escrita exitosamente. Dirección: {address}, Valor: {value}", LogLevel.Info);
                else Logger.Log($"ModbusService: Falló escritura de Bobina Individual o validación falló. Dirección: {address}", LogLevel.Warning);
                return success;
            }
            catch (Exception ex)
            {
                Logger.Log($"ModbusService: Error escribiendo Bobina Individual. Dirección: {address}, Error: {ex.Message}", LogLevel.Error);
                OnCommunicationError(ex);
                return false;
            }
        }

        /// <summary>
        /// Writes a single register to the Modbus device
        /// </summary>
        /// <param name="address">Register address (0-based)</param>
        /// <param name="value">Value to write</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> WriteSingleRegisterAsync(ushort address, ushort value)
        {
            Logger.Log($"Modbus: Escribiendo registro {address} = {value}", LogLevel.Info);
            byte[] request = CreateModbusRequest(ModbusFunctionCode.WriteSingleRegister, address, value);
            try
            {
                byte[] response = await SendRequestAsync(request);
                bool success = response != null && response.Length >= 6 &&
                              response[0] == DeviceId &&
                              response[1] == (byte)ModbusFunctionCode.WriteSingleRegister &&
                              BitConverter.ToUInt16(response.Skip(2).Take(2).Reverse().ToArray(), 0) == address &&
                              BitConverter.ToUInt16(response.Skip(4).Take(2).Reverse().ToArray(), 0) == value;

                if (success) Logger.Log($"ModbusService: Registro Individual escrito exitosamente. Dirección: {address}, Valor: {value}", LogLevel.Info);
                else Logger.Log($"ModbusService: Falló escritura de Registro Individual o validación falló. Dirección: {address}", LogLevel.Warning);
                return success;
            }
            catch (Exception ex)
            {
                Logger.Log($"ModbusService: Error escribiendo Registro Individual. Dirección: {address}, Valor: {value}, Error: {ex.Message}", LogLevel.Error);
                OnCommunicationError(ex);
                return false;
            }
        }

        /// <summary>
        /// Writes multiple coils to the Modbus device
        /// </summary>
        /// <param name="startAddress">Starting address (0-based)</param>
        /// <param name="values">Array of boolean values to write</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> WriteMultipleCoilsAsync(ushort startAddress, bool[] values)
        {
            // Implementation omitted for brevity
            // This would require creating a more complex Modbus frame
            throw new NotImplementedException("WriteMultipleCoilsAsync is not yet implemented");
        }

        /// <summary>
        /// Writes multiple registers to the Modbus device
        /// </summary>
        /// <param name="startAddress">Starting address (0-based)</param>
        /// <param name="values">Array of ushort values to write</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> WriteMultipleRegistersAsync(ushort startAddress, ushort[] values)
        {
            // Implementation omitted for brevity
            // This would require creating a more complex Modbus frame
            throw new NotImplementedException("WriteMultipleRegistersAsync is not yet implemented");
        }

        #endregion

        #region Address Parsing and Float Handling

        /// <summary>
        /// Parses address string and determines if it's a float format (MF{address})
        /// </summary>
        /// <param name="addressString">Address string (e.g., "400" or "MF400")</param>
        /// <param name="actualAddress">Parsed address (for MF format, this is {address}+1)</param>
        /// <param name="isFloatFormat">True if MF format detected</param>
        /// <returns>True if parsing successful</returns>
        public static bool ParseAddress(string addressString, out ushort actualAddress, out bool isFloatFormat)
        {
            actualAddress = 0;
            isFloatFormat = false;

            if (string.IsNullOrWhiteSpace(addressString))
                return false;

            addressString = addressString.Trim().ToUpper();

            // Check for MF{address} format
            if (addressString.StartsWith("MF"))
            {
                string numberPart = addressString.Substring(2);
                if (ushort.TryParse(numberPart, out ushort baseAddress))
                {
                    actualAddress = (ushort)(baseAddress + 1); // MF{address} reads from {address}+1
                    isFloatFormat = true;
                    return true;
                }
                return false;
            }

            // Standard address format
            if (ushort.TryParse(addressString, out actualAddress))
            {
                isFloatFormat = false;
                return true;
            }

            return false;
        }

        /// <summary>
        /// Converts two consecutive holding registers to a float using Little Endian format
        /// </summary>
        /// <param name="register1">First register (lower address)</param>
        /// <param name="register2">Second register (higher address)</param>
        /// <returns>Float value assembled from the two registers</returns>
        public static float ConvertRegistersToFloat(ushort register1, ushort register2)
        {
            // Little Endian: Lower address register contains lower bytes
            byte[] bytes = new byte[4];

            // Register 1 (lower address) -> bytes 0,1
            bytes[0] = (byte)(register1 & 0xFF);        // Low byte of register 1
            bytes[1] = (byte)((register1 >> 8) & 0xFF); // High byte of register 1

            // Register 2 (higher address) -> bytes 2,3
            bytes[2] = (byte)(register2 & 0xFF);        // Low byte of register 2
            bytes[3] = (byte)((register2 >> 8) & 0xFF); // High byte of register 2

            return BitConverter.ToSingle(bytes, 0);
        }

        /// <summary>
        /// Converts a float to two consecutive holding registers using Little Endian format
        /// </summary>
        /// <param name="value">Float value to convert</param>
        /// <returns>Array of two registers [register1, register2]</returns>
        public static ushort[] ConvertFloatToRegisters(float value)
        {
            byte[] bytes = BitConverter.GetBytes(value);

            // Little Endian: bytes 0,1 go to register 1, bytes 2,3 go to register 2
            ushort register1 = (ushort)(bytes[0] | (bytes[1] << 8));
            ushort register2 = (ushort)(bytes[2] | (bytes[3] << 8));

            return new ushort[] { register1, register2 };
        }

        #endregion

        #region Helper Methods

        private async Task<bool[]> ReadBooleanValuesAsync(ModbusFunctionCode functionCode, ushort startAddress, ushort count)
        {
            if (count == 0 || count > 2000) // Modbus PDU limit for coils/inputs usually allows up to 2000
            {
                Logger.Log($"ModbusService: Cantidad inválida ({count}) para lectura de valores booleanos. Debe ser 1-2000.", LogLevel.Warning);
                throw new ArgumentOutOfRangeException(nameof(count), "Number of items to read must be between 1 and 2000.");
            }
            byte[] request = CreateModbusRequest(functionCode, startAddress, count);
            try
            {
                byte[] response = await SendRequestAsync(request);

                if (response != null && response.Length >= 9)
                {
                    int dataLength = response[8];
                    bool[] result = new bool[count];

                    for (int i = 0; i < count; i++)
                    {
                        int byteIndex = 9 + (i / 8);
                        int bitIndex = i % 8;

                        if (byteIndex < response.Length)
                        {
                            result[i] = ((response[byteIndex] >> bitIndex) & 1) == 1;
                        }
                    }

                    OnDataReceived(functionCode, startAddress, result);
                    return result;
                }

                return new bool[0];
            }
            catch (Exception ex)
            {
                Logger.Log($"ModbusService: Error en ReadBooleanValuesAsync. Función: {functionCode}, Dirección: {startAddress}, Cantidad: {count}, Error: {ex.Message}", LogLevel.Error);
                OnCommunicationError(ex);
                return null;
            }
        }

        private async Task<ushort[]> ReadRegistersAsync(ModbusFunctionCode functionCode, ushort startAddress, ushort count)
        {
            if (count == 0 || count > 125) // Modbus PDU limit for registers usually allows up to 125
            {
                Logger.Log($"ModbusService: Cantidad inválida ({count}) para lectura de registros. Debe ser 1-125.", LogLevel.Warning);
                throw new ArgumentOutOfRangeException(nameof(count), "Number of registers to read must be between 1 and 125.");
            }
            byte[] request = CreateModbusRequest(functionCode, startAddress, count);

            // Log request for debugging Schneider PLC issues
            string requestHex = string.Join(" ", request.Select(b => b.ToString("X2")));
            Logger.Log($"ModbusService: Enviando solicitud - Función: {functionCode}, Dir: {startAddress}, Cant: {count}", LogLevel.Info);
            Logger.Log($"ModbusService: Trama enviada: {requestHex}", LogLevel.Info);

            try
            {
                byte[] response = await SendRequestAsync(request);

                if (response != null)
                {
                    // Simplified validation like Python client
                    // Calculate correct offsets based on connection type
                    int unitIdOffset = ConnectionType == ModbusConnectionType.TCP ? 6 : 0;
                    int functionCodeOffset = unitIdOffset + 1;
                    int dataLengthOffset = functionCodeOffset + 1;
                    int dataStartOffset = dataLengthOffset + 1;

                    // Log response for debugging like Python client
                    string responseHex = string.Join(" ", response.Take(Math.Min(response.Length, 20)).Select(b => b.ToString("X2")));
                    Logger.Log($"ModbusService: Respuesta recibida: {responseHex}", LogLevel.Info);

                    // Basic length check - simplified like Python
                    if (response.Length >= dataStartOffset + 1)
                    {
                        int dataLength = response[dataLengthOffset];
                        int expectedDataBytes = count * 2;

                        if (dataLength == expectedDataBytes && response.Length >= dataStartOffset + dataLength)
                        {
                            ushort[] result = new ushort[count];

                            for (int i = 0; i < count; i++)
                            {
                                int byteIndex = dataStartOffset + (i * 2);
                                if (byteIndex + 1 < response.Length)
                                {
                                    result[i] = (ushort)((response[byteIndex] << 8) | response[byteIndex + 1]);
                                }
                            }

                            Logger.Log($"ModbusService: Lectura exitosa - {count} registros desde dirección {startAddress}", LogLevel.Success);
                            OnDataReceived(functionCode, startAddress, result);
                            return result;
                        }
                        else
                        {
                            Logger.Log($"ModbusService: Error de longitud de datos - Esperado: {expectedDataBytes}, Recibido: {dataLength}", LogLevel.Error);
                        }
                    }
                    else
                    {
                        Logger.Log($"ModbusService: Respuesta muy corta - Longitud: {response.Length}, Mínimo esperado: {dataStartOffset + 1}", LogLevel.Error);
                    }
                }

                return new ushort[0];
            }
            catch (Exception ex)
            {
                Logger.Log($"Error Modbus: Lectura fallida - Función: {functionCode}, Dir: {startAddress}, Cant: {count} - {ex.Message}", LogLevel.Error);
                OnCommunicationError(ex);
                return null;
            }
        }

        private byte[] CreateModbusRequest(ModbusFunctionCode functionCode, ushort address, ushort value)
        {
            // Simple frame construction like pymodbus does internally
            List<byte> frame = new List<byte>();

            if (ConnectionType == ModbusConnectionType.TCP)
            {
                // MBAP Header for TCP (exactly like pymodbus)
                ushort transactionId = 1; // Simple fixed ID
                frame.Add((byte)(transactionId >> 8));   // Transaction ID High
                frame.Add((byte)(transactionId & 0xFF)); // Transaction ID Low
                frame.Add(0x00); // Protocol ID High (always 0 for Modbus)
                frame.Add(0x00); // Protocol ID Low (always 0 for Modbus)
                frame.Add(0x00); // Length High (will be 6 for read requests)
                frame.Add(0x06); // Length Low (UnitID + FuncCode + Address + Count = 6 bytes)
            }

            // PDU (same for TCP and RTU, like pymodbus)
            frame.Add(DeviceId);                    // Unit ID
            frame.Add((byte)functionCode);          // Function Code
            frame.Add((byte)(address >> 8));        // Address High
            frame.Add((byte)(address & 0xFF));      // Address Low
            frame.Add((byte)(value >> 8));          // Count/Value High
            frame.Add((byte)(value & 0xFF));        // Count/Value Low

            if (ConnectionType == ModbusConnectionType.RTU)
            {
                // Add CRC for RTU
                ushort crc = CalculateCRC(frame.ToArray(), 0, frame.Count);
                frame.Add((byte)(crc & 0xFF)); // CRC Low
                frame.Add((byte)(crc >> 8));   // CRC High
            }

            return frame.ToArray();
        }

        private async Task<byte[]> SendRequestAsync(byte[] request)
        {
            if (!isConnected)
            {
                Logger.Log("ModbusService: SendRequestAsync called while not connected.", LogLevel.Warning);
                throw new InvalidOperationException("Not connected to Modbus device.");
            }

            byte[] responseBuffer = new byte[260]; // Max Modbus TCP frame size is 260, RTU is 256.
            int bytesRead = 0;

            for (int attempt = 0; attempt <= RetryCount; attempt++)
            {
                try
                {
                    lock (communicationLock) // Ensure only one communication operation at a time
                    {
                        if (ConnectionType == ModbusConnectionType.TCP)
                        {
                            NetworkStream stream = tcpClient.GetStream();
                            stream.Write(request, 0, request.Length);
                            bytesRead = stream.Read(responseBuffer, 0, responseBuffer.Length);
                        }
                        else // RTU
                        {
                            serialPort.Write(request, 0, request.Length);
                            // RTU reads can be tricky, often need to read until silence or expected length
                            // This simple Read might need adjustment for robust RTU handling
                            bytesRead = serialPort.Read(responseBuffer, 0, responseBuffer.Length);
                        }
                    }

                    if (bytesRead == 0)
                    {
                        Logger.Log("ModbusService: No data received in response.", LogLevel.Warning);
                        throw new IOException("No data received from Modbus device.");
                    }

                    byte[] actualResponse = responseBuffer.Take(bytesRead).ToArray();

                    // Basic validation: Check for Modbus exception response
                    int functionCodeOffset = ConnectionType == ModbusConnectionType.TCP ? 7 : 1; // TCP: MBAP(6) + UnitID(1), RTU: UnitID(1)
                    int exceptionCodeOffset = functionCodeOffset + 1;
                    int minExceptionLength = ConnectionType == ModbusConnectionType.TCP ? 9 : 3; // TCP: MBAP(6) + UnitID(1) + FuncCode(1) + ExceptionCode(1), RTU: UnitID(1) + FuncCode(1) + ExceptionCode(1)

                    if (actualResponse.Length >= minExceptionLength &&
                        (actualResponse[functionCodeOffset] & 0x80) == 0x80) // Exception if MSB of function code is set
                    {
                        byte exceptionCode = actualResponse[exceptionCodeOffset];
                        string errorMessage = $"Excepción Modbus {exceptionCode:X2}: {GetModbusExceptionMessage(exceptionCode)}";
                        Logger.Log($"ModbusService: {errorMessage}", LogLevel.Error);

                        // Log the raw response for debugging Schneider PLC issues
                        string responseHex = string.Join(" ", actualResponse.Take(Math.Min(actualResponse.Length, 16)).Select(b => b.ToString("X2")));
                        Logger.Log($"ModbusService: Respuesta cruda (primeros 16 bytes): {responseHex}", LogLevel.Error);

                        throw new ModbusException(errorMessage, (ModbusFunctionCode)(actualResponse[functionCodeOffset] & 0x7F), exceptionCode);
                    }

                    // CRC Check for RTU
                    if (ConnectionType == ModbusConnectionType.RTU)
                    {
                        if (!VerifyCRC(actualResponse))
                        {
                            Logger.Log("ModbusService: RTU response CRC check failed.", LogLevel.Error);
                            throw new IOException("Modbus RTU CRC check failed.");
                        }
                        // For RTU, remove CRC from the response before returning PDU
                        return actualResponse.Take(bytesRead - 2).ToArray();
                    }

                    // For TCP, the MBAP header needs to be handled if further PDU processing assumes no header.
                    // For now, returning the full frame including MBAP. Adjust if PDU-only is needed by callers.
                    // Or, strip MBAP here: return actualResponse.Skip(6).ToArray(); (if PDU starts after typical 6-byte MBAP)
                    // Assuming standard Modbus/TCP, the transaction ID and other MBAP fields should be validated if strict.
                    // The DeviceId (Unit Identifier) is at actualResponse[6] for TCP.
                    return actualResponse;
                }
                catch (Exception ex)
                {
                    Logger.Log($"ModbusService: Attempt {attempt + 1} of {RetryCount + 1} failed. Error: {ex.Message}", LogLevel.Warning);
                    OnCommunicationError(ex);
                    if (attempt == RetryCount)
                    {
                        Logger.Log("ModbusService: Max retries reached. SendRequestAsync failed.", LogLevel.Error);
                        throw; // Rethrow last exception
                    }
                    await Task.Delay(RetryDelayMs);
                }
            }
            return null; // Should not be reached
        }

        private ushort CalculateCRC(byte[] buffer, int offset, int length)
        {
            ushort crc = 0xFFFF;

            for (int i = offset; i < offset + length; i++)
            {
                crc ^= buffer[i];

                for (int j = 0; j < 8; j++)
                {
                    bool lsb = (crc & 0x0001) != 0;
                    crc >>= 1;

                    if (lsb)
                    {
                        crc ^= 0xA001;
                    }
                }
            }

            return crc;
        }

        private bool VerifyCRC(byte[] buffer)
        {
            if (buffer.Length < 3)
            {
                return false;
            }

            int length = buffer.Length - 2;
            ushort calculatedCrc = CalculateCRC(buffer, 0, length);
            ushort receivedCrc = (ushort)((buffer[length + 1] << 8) | buffer[length]);

            return calculatedCrc == receivedCrc;
        }

        private T ConvertRegisterValue<T>(ushort value)
        {
            Type targetType = typeof(T);

            if (targetType == typeof(bool))
            {
                return (T)(object)(value != 0);
            }
            else if (targetType == typeof(short))
            {
                return (T)(object)(short)value;
            }
            else if (targetType == typeof(ushort))
            {
                return (T)(object)value;
            }
            else if (targetType == typeof(int))
            {
                // Assuming the ushort represents the lower 16 bits of an int
                return (T)(object)(int)value;
            }
            else if (targetType == typeof(uint))
            {
                // Assuming the ushort represents the lower 16 bits of a uint
                return (T)(object)(uint)value;
            }
            else if (targetType == typeof(float))
            {
                // This is tricky. A single Modbus register (ushort) cannot directly represent a float.
                // Typically, floats are stored across two registers.
                // This method would need to be adapted or called differently if floats are expected from a single register.
                // For now, throwing an exception or returning a default.
                Logger.Log("ModbusService: Cannot convert single ushort to float directly. Floats usually span two registers.", LogLevel.Warning);
                throw new ArgumentException($"Direct conversion from single ushort to type {targetType.Name} is not supported for floats. Consider reading two registers.");
            }

            Logger.Log($"ModbusService: Conversión a tipo {targetType.Name} no está soportada para valor {value}.", LogLevel.Warning);
            throw new ArgumentException($"Conversion to type {targetType.Name} is not supported");
        }

        private string GetModbusExceptionMessage(byte exceptionCode)
        {
            switch (exceptionCode)
            {
                case 0x01: return "ILLEGAL FUNCTION";
                case 0x02: return "ILLEGAL DATA ADDRESS";
                case 0x03: return "ILLEGAL DATA VALUE";
                case 0x04: return "SLAVE DEVICE FAILURE";
                case 0x05: return "ACKNOWLEDGE";
                case 0x06: return "SLAVE DEVICE BUSY";
                case 0x08: return "MEMORY PARITY ERROR";
                case 0x0A: return "GATEWAY PATH UNAVAILABLE";
                case 0x0B: return "GATEWAY TARGET DEVICE FAILED TO RESPOND";
                default: return "UNKNOWN MODBUS EXCEPTION";
            }
        }

        #endregion

        #region Event Handlers

        private void OnConnectionStatusChanged(bool connected)
        {
            Logger.Log($"ModbusService: Estado de conexión cambió. Conectado: {connected}", LogLevel.Info);
            ConnectionStatusChanged?.Invoke(this, connected);
        }

        private void OnCommunicationError(Exception ex)
        {
            Logger.Log($"ModbusService: Error de comunicación ocurrió: {ex.Message}", LogLevel.Error);
            CommunicationError?.Invoke(this, ex);
        }

        private void OnDataReceived(ModbusFunctionCode functionCode, ushort startAddress, object values)
        {
            DataReceived?.Invoke(this, new ModbusDataReceivedEventArgs(functionCode, startAddress, values));
        }

        #endregion

        #region IDisposable Implementation

        private bool disposedValue;

        /// <summary>
        /// Disposes of managed resources
        /// </summary>
        /// <param name="disposing">True if called from Dispose(), false if called from finalizer</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    Logger.Log("ModbusService: Disposing managed resources.", LogLevel.Info);
                    // Disconnect if connected
                    if (IsConnected)
                    {
                        Task.Run(async () => await DisconnectAsync()).Wait(); // Synchronously wait for disconnect if called from Dispose
                    }

                    // Dispose managed state (managed objects).
                    if (tcpClient != null)
                    {
                        tcpClient.Dispose();
                        tcpClient = null;
                    }
                    if (serialPort != null)
                    {
                        serialPort.Dispose();
                        serialPort = null;
                    }
                }

                // TODO: free unmanaged resources (unmanaged objects) and override a finalizer below.
                // TODO: set large fields to null.

                disposedValue = true;
            }
        }

        /// <summary>
        /// Finalizer
        /// </summary>
        ~ModbusService()
        {
            Dispose(false);
        }

        /// <summary>
        /// Disposes resources used by the ModbusService
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        #endregion
    }

    /// <summary>
    /// Event arguments for the DataReceived event
    /// </summary>
    public class ModbusDataReceivedEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the function code of the received data
        /// </summary>
        public ModbusService.ModbusFunctionCode FunctionCode { get; }

        /// <summary>
        /// Gets the starting address of the received data
        /// </summary>
        public ushort StartAddress { get; }

        /// <summary>
        /// Gets the values received
        /// </summary>
        public object Values { get; }

        /// <summary>
        /// Initializes a new instance of the ModbusDataReceivedEventArgs class
        /// </summary>
        /// <param name="functionCode">Function code of the received data</param>
        /// <param name="startAddress">Starting address of the received data</param>
        /// <param name="values">Values received</param>
        public ModbusDataReceivedEventArgs(ModbusService.ModbusFunctionCode functionCode, ushort startAddress, object values)
        {
            FunctionCode = functionCode;
            StartAddress = startAddress;
            Values = values;
        }
    }

    /// <summary>
    /// Represents errors that occur during Modbus communication due to server-side exceptions.
    /// </summary>
    public class ModbusException : Exception
    {
        /// <summary>
        /// Gets the Modbus function code that was being executed when the exception occurred.
        /// </summary>
        public ModbusService.ModbusFunctionCode FunctionCode { get; }

        /// <summary>
        /// Gets the Modbus exception code returned by the slave device.
        /// </summary>
        public byte ModbusErrorCode { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="ModbusException"/> class.
        /// </summary>
        public ModbusException()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ModbusException"/> class with a specified error message.
        /// </summary>
        /// <param name="message">The message that describes the error.</param>
        public ModbusException(string message)
            : base(message)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ModbusException"/> class with a specified error message and a reference to the inner exception that is the cause of this exception.
        /// </summary>
        /// <param name="message">The error message that explains the reason for the exception.</param>
        /// <param name="innerException">The exception that is the cause of the current exception, or a null reference if no inner exception is specified.</param>
        public ModbusException(string message, Exception innerException)
            : base(message, innerException)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ModbusException"/> class with a specified error message, function code, and Modbus error code.
        /// </summary>
        /// <param name="message">The error message that explains the reason for the exception.</param>
        /// <param name="functionCode">The Modbus function code.</param>
        /// <param name="modbusErrorCode">The Modbus exception code from the slave.</param>
        public ModbusException(string message, ModbusService.ModbusFunctionCode functionCode, byte modbusErrorCode)
            : base(message)
        {
            FunctionCode = functionCode;
            ModbusErrorCode = modbusErrorCode;
        }
    }
}