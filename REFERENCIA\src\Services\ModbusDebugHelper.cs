using System;
using System.Linq;
using prueba.Services;

namespace prueba.Services
{
    /// <summary>
    /// Helper class for debugging Modbus communication issues with Schneider PLCs
    /// </summary>
    public static class ModbusDebugHelper
    {
        /// <summary>
        /// Creates a test Modbus request frame for debugging purposes
        /// </summary>
        /// <param name="connectionType">TCP or RTU</param>
        /// <param name="deviceId">Device ID (Unit ID)</param>
        /// <param name="address">Register address (0-based)</param>
        /// <param name="count">Number of registers to read</param>
        /// <returns>Hex string representation of the frame</returns>
        public static string CreateTestFrame(ModbusService.ModbusConnectionType connectionType, byte deviceId, ushort address, ushort count)
        {
            var frame = new System.Collections.Generic.List<byte>();
            ushort transactionId = 0x0001; // Fixed for testing

            if (connectionType == ModbusService.ModbusConnectionType.TCP)
            {
                // MBAP Header (6 bytes total)
                frame.AddRange(BitConverter.GetBytes(transactionId).Reverse()); // Transaction ID (2 bytes)
                frame.AddRange(BitConverter.GetBytes((ushort)0x0000).Reverse());  // Protocol ID (2 bytes) - 0 for Modbus
                
                // Calculate PDU length (UnitID + FuncCode + Address + Count = 6 bytes)
                ushort pduLength = 6;
                frame.AddRange(BitConverter.GetBytes(pduLength).Reverse()); // Length field (2 bytes)
            }

            // PDU (Protocol Data Unit)
            frame.Add(deviceId); // Unit ID (Slave Address) - 1 byte
            frame.Add(0x03); // Function Code 03 (Read Holding Registers) - 1 byte
            frame.AddRange(BitConverter.GetBytes(address).Reverse()); // Start Address - 2 bytes
            frame.AddRange(BitConverter.GetBytes(count).Reverse());   // Quantity - 2 bytes

            if (connectionType == ModbusService.ModbusConnectionType.RTU)
            {
                // Add CRC for RTU (simplified - would need actual CRC calculation)
                frame.Add(0x00); // CRC Low byte (placeholder)
                frame.Add(0x00); // CRC High byte (placeholder)
            }

            return string.Join(" ", frame.Select(b => b.ToString("X2")));
        }

        /// <summary>
        /// Validates if an address is within typical Schneider PLC ranges
        /// </summary>
        /// <param name="address">0-based address</param>
        /// <returns>Validation result with recommendations</returns>
        public static string ValidateSchneiderAddress(ushort address)
        {
            // Convert to 1-based traditional Modbus addressing
            ushort traditionalAddress = (ushort)(address + 1);
            ushort holdingRegisterAddress = (ushort)(40000 + traditionalAddress);

            var result = $"Dirección API (0-based): {address}\n";
            result += $"Dirección Modbus tradicional: {holdingRegisterAddress}\n";

            // Common Schneider PLC address ranges
            if (address >= 0 && address <= 999)
            {
                result += "✓ Rango válido para Schneider M340/M580 (registros %MW0-%MW999)\n";
            }
            else if (address >= 1000 && address <= 9999)
            {
                result += "⚠ Rango extendido - verificar configuración del PLC\n";
            }
            else if (address >= 10000)
            {
                result += "❌ Dirección muy alta - posible error de configuración\n";
            }

            // Specific recommendations for address 400
            if (address == 400)
            {
                result += "\n🔍 Para dirección 400 (registro 40401):\n";
                result += "- Verificar que el registro existe en el PLC\n";
                result += "- Comprobar configuración de memoria del PLC\n";
                result += "- Intentar con direcciones más bajas (0-99) primero\n";
                result += "- Verificar Device ID (típicamente 1 para Schneider)\n";
            }

            return result;
        }

        /// <summary>
        /// Provides troubleshooting steps for common Schneider PLC issues
        /// </summary>
        /// <param name="exceptionCode">Modbus exception code received</param>
        /// <returns>Specific troubleshooting steps</returns>
        public static string GetSchneiderTroubleshooting(byte exceptionCode)
        {
            return exceptionCode switch
            {
                0x01 => "ILLEGAL FUNCTION (0x01) - Schneider PLC:\n" +
                       "• Verificar que el PLC soporta función 03 (Read Holding Registers)\n" +
                       "• Comprobar configuración del módulo Ethernet\n" +
                       "• Verificar firmware del PLC\n",
                       
                0x02 => "ILLEGAL DATA ADDRESS (0x02) - Schneider PLC:\n" +
                       "• La dirección 400 (40401) no existe en el PLC\n" +
                       "• Verificar configuración de memoria %MW en Unity Pro\n" +
                       "• Intentar direcciones más bajas: 0, 1, 10, 100\n" +
                       "• Comprobar mapeo de variables en el PLC\n",
                       
                0x03 => "ILLEGAL DATA VALUE (0x03) - Schneider PLC:\n" +
                       "• Cantidad de registros inválida\n" +
                       "• Intentar leer solo 1 registro\n" +
                       "• Verificar límites de lectura del PLC\n",
                       
                0x04 => "SLAVE DEVICE FAILURE (0x04) - Schneider PLC:\n" +
                       "• Error interno del PLC\n" +
                       "• Verificar estado del PLC (RUN/STOP)\n" +
                       "• Comprobar diagnósticos del PLC\n",
                       
                _ => $"Código de excepción desconocido: 0x{exceptionCode:X2}\n" +
                     "• Verificar documentación específica del PLC\n" +
                     "• Comprobar configuración de red\n"
            };
        }
    }
}
