﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <UseWindowsForms>true</UseWindowsForms>
    <ImportWindowsDesktopTargets>true</ImportWindowsDesktopTargets>
    <TargetFrameworks>net9.0-windows</TargetFrameworks>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>computer_folder_file_10469.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="computer_folder_file_10469.ico" />
    <Content Include="Recursos\computer_folder_file_10469.ico" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Microsoft.Exchange.WebServices" Version="2.2.0" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="BouncyCastle.Cryptography" Version="2.5.1" />
    <PackageReference Include="MailKit" Version="4.12.0" />
    <PackageReference Include="MimeKit" Version="4.12.0" />
    <PackageReference Include="MQTTnet" Version="4.3.7.1207" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Formats.Asn1" Version="9.0.5" />
    <PackageReference Include="System.IO.Ports" Version="9.0.5" />
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="6.1.2" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.Runtime.InteropServices.RuntimeInformation" Version="4.3.0" />
  </ItemGroup>
</Project>