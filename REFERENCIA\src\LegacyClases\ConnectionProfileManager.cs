﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Windows.Forms;

namespace prueba
{
    public class ConnectionProfile
    {
        // --- Campos MQTT (existentes) ---
        public string Name { get; set; }
        public string Protocol { get; set; }
        public string Host { get; set; }
        public int Port { get; set; }
        public string ClientId { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public bool UseTls { get; set; }
        public bool IsCASigned { get; set; }
        public bool UseAlpn { get; set; }

        // --- Nuevos campos para Configuración de Correo POP3 ---
        public string Pop3Server { get; set; }
        public int Pop3Port { get; set; }
        public string Pop3Username { get; set; }
        public string Pop3Password { get; set; }
        public bool Pop3UseSsl { get; set; }

        public int EmailCheckIntervalMinutes { get; set; } // Email check interval in minutes
        public bool AutoStartEmailChecking { get; set; } // Whether to automatically start email checking on startup



        // --- Nuevos campos para Configuración de Exchange Web Services (EWS) ---
        public bool UseEws { get; set; } // Whether to use EWS instead of POP3/MAPI
        public string EwsServerUrl { get; set; } // Exchange server URL (e.g., https://outlook.office365.com/EWS/Exchange.asmx)
        public string EwsUsername { get; set; } // EWS username/email
        public string EwsPassword { get; set; } // EWS password
        public bool EwsUseOAuth { get; set; } // Whether to use OAuth instead of basic authentication
        public string EwsDomain { get; set; } // Domain for EWS authentication (optional)

        // --- Email Notification Addresses ---
        public string EmailNotification1 { get; set; } // First email notification address
        public string EmailNotification2 { get; set; } // Second email notification address
        public string EmailNotification3 { get; set; } // Third email notification address
        public string EmailNotification4 { get; set; } // Fourth email notification address
        public string EmailNotification5 { get; set; } // Fifth email notification address

        /// <summary>
        /// Gets or sets the file path for the Paired Modbus Configuration JSON file
        /// associated with this connection profile.
        /// </summary>
        public string PairedModbusConfigPath { get; set; }

        /// <summary>
        /// Gets or sets the name of the left Modbus configuration
        /// </summary>
        public string LeftModbusConfigName { get; set; }

        /// <summary>
        /// Gets or sets the name of the right Modbus configuration
        /// </summary>
        public string RightModbusConfigName { get; set; }

        /// <summary>
        /// Gets or sets whether the left Modbus instance should auto-connect when profile loads
        /// </summary>
        public bool AutoConnectLeftModbus { get; set; }

        /// <summary>
        /// Gets or sets whether the right Modbus instance should auto-connect when profile loads
        /// </summary>
        public bool AutoConnectRightModbus { get; set; }

        public ConnectionProfile()
        {
            // Valores por defecto para MQTT (si los tienes)
            Port = 1883;
            UseTls = false;
            IsCASigned = true; // O tu valor por defecto

            // Valores por defecto para POP3
            Pop3Port = 995; // Puerto común para POP3S (SSL/TLS implícito)
            Pop3UseSsl = true;
            // Valores por defecto para verificación automática de correo
            EmailCheckIntervalMinutes = 5; // Default 5 minutes
            AutoStartEmailChecking = true; // Auto-start by default

            // Valores por defecto para EWS
            UseEws = false; // Por defecto usar POP3
            EwsServerUrl = "https://outlook.office365.com/EWS/Exchange.asmx"; // Default Office 365 EWS endpoint
            EwsUseOAuth = false; // Por defecto usar autenticación básica
            EwsDomain = ""; // Dominio opcional
        }
    }

    public class ConnectionProfileManager
    {
        private readonly string profilesFilePath;
        private readonly string lastProfileFilePath;
        private readonly byte[] entropy = Encoding.UTF8.GetBytes("MQTTApp-ExtraSalt-2024");
        private const int MaxProfiles = 5;

        public ConnectionProfileManager()
        {
            var dir = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "PruebaMQTT");
            if (!Directory.Exists(dir)) Directory.CreateDirectory(dir);
            profilesFilePath = Path.Combine(dir, "profiles.dat");
            lastProfileFilePath = Path.Combine(dir, "last_profile.dat");
        }

        public List<ConnectionProfile> LoadProfiles()
        {
            if (!File.Exists(profilesFilePath))
                return new List<ConnectionProfile>();

            try
            {
                var encrypted = File.ReadAllBytes(profilesFilePath);
                var decrypted = ProtectedData.Unprotect(encrypted, entropy, DataProtectionScope.CurrentUser);
                var json = Encoding.UTF8.GetString(decrypted);
                return JsonConvert.DeserializeObject<List<ConnectionProfile>>(json) ?? new List<ConnectionProfile>();
            }
            catch
            {
                MessageBox.Show("No se pudieron leer los perfiles guardados. Puede que estén dañados.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new List<ConnectionProfile>();
            }
        }

        public void SaveProfiles(List<ConnectionProfile> profiles)
        {
            var json = JsonConvert.SerializeObject(profiles);
            var plain = Encoding.UTF8.GetBytes(json);
            var encrypted = ProtectedData.Protect(plain, entropy, DataProtectionScope.CurrentUser);
            File.WriteAllBytes(profilesFilePath, encrypted);
        }

        public void SaveProfileAt(ConnectionProfile profile, int index)
        {
            var profiles = LoadProfiles();
            while (profiles.Count < MaxProfiles)
                profiles.Add(new ConnectionProfile());
            profiles[index] = profile;
            SaveProfiles(profiles);
        }

        public ConnectionProfile GetProfileAt(int index)
        {
            var profiles = LoadProfiles();
            if (index < profiles.Count)
                return profiles[index];
            return null;
        }

        /// <summary>
        /// Saves the index of the last loaded profile
        /// </summary>
        /// <param name="profileIndex">The index of the profile to remember</param>
        public void SaveLastLoadedProfile(int profileIndex)
        {
            try
            {
                var data = Encoding.UTF8.GetBytes(profileIndex.ToString());
                var encrypted = ProtectedData.Protect(data, entropy, DataProtectionScope.CurrentUser);
                File.WriteAllBytes(lastProfileFilePath, encrypted);

            }
            catch (Exception ex)
            {
                Logger.Log($"Error saving last loaded profile: {ex.Message}", LogLevel.Warning);
            }
        }

        /// <summary>
        /// Gets the index of the last loaded profile, or null if none was saved
        /// </summary>
        /// <returns>The index of the last loaded profile, or null</returns>
        public int? GetLastLoadedProfile()
        {
            if (!File.Exists(lastProfileFilePath))
                return null;

            try
            {
                var encrypted = File.ReadAllBytes(lastProfileFilePath);
                var decrypted = ProtectedData.Unprotect(encrypted, entropy, DataProtectionScope.CurrentUser);
                var indexString = Encoding.UTF8.GetString(decrypted);

                if (int.TryParse(indexString, out int index))
                {
                    Logger.Log($"Last loaded profile index retrieved: {index}", LogLevel.Debug);
                    return index;
                }
            }
            catch (Exception ex)
            {
                Logger.Log($"Error loading last loaded profile: {ex.Message}", LogLevel.Warning);
            }

            return null;
        }

        public bool HasProfileAt(int index)
        {
            var profiles = LoadProfiles();
            return index < profiles.Count && !string.IsNullOrEmpty(profiles[index]?.Host);
        }
    }
}
