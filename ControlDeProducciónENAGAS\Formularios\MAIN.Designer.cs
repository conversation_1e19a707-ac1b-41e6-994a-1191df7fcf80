﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace ControlDeProducciónENAGAS.Formularios
{
    partial class MAIN
    {
        /// <summary>
        /// Variable del diseñador necesaria.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Limpiar los recursos que se estén usando.
        /// </summary>
        /// <param name="disposing">true si los recursos administrados se deben desechar; false en caso contrario.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Código generado por el Diseñador de Windows Forms

        /// <summary>
        /// Método necesario para admitir el Diseñador. No se puede modificar
        /// el contenido de este método con el editor de código.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            panelBarraSuperior = new Panel();
            lblTituloApp = new Label();
            btnCerrar = new Button();
            panelEstados = new Panel();
            panelEstadoMqtt = new Panel();
            lblEstadoMqtt = new Label();
            panelEstadoCorreo = new Panel();
            lblEstadoCorreo = new Label();
            panelEstadoModbus = new Panel();
            lblEstadoModbus = new Label();
            panelNavegacion = new Panel();
            btnNavCorreo = new Button();
            btnNavMqtt = new Button();
            btnNavModbus = new Button();
            btnNavSinoptico = new Button();
            panelLog = new Panel();
            rtbLog = new RichTextBox();
            btnLimpiarLog = new Button();
            tabControlPrincipal = new TabControl();
            tabPageCorreo = new TabPage();
            grpConfigCorreo = new GroupBox();
            lblEwsServerUrl = new Label();
            txtEwsServerUrl = new TextBox();
            lblEwsUsuario = new Label();
            txtEwsUsuario = new TextBox();
            lblEwsPassword = new Label();
            txtEwsPassword = new TextBox();
            lblEwsDominio = new Label();
            txtEwsDominio = new TextBox();
            lblEmailNotificacion = new Label();
            txtEmailNotificacion = new TextBox();
            btnConectarCorreo = new Button();
            btnDesconectarCorreo = new Button();
            btnProcesarCorreos = new Button();
            btnForzarCambioHora = new Button();
            grpEstadosHorarios = new GroupBox();
            dgvEstadosHorarios = new DataGridView();
            tabPageMqtt = new TabPage();
            groupBox1 = new GroupBox();
            btnRefrescarMQTT = new Button();
            dgvMqttTopics = new DataGridView();
            grpConexionMqtt = new GroupBox();
            lblMqttProtocolo = new Label();
            cmbMqttProtocolo = new ComboBox();
            lblMqttHost = new Label();
            txtMqttHost = new TextBox();
            lblMqttPuerto = new Label();
            txtMqttPuerto = new TextBox();
            lblMqttClientId = new Label();
            txtMqttClientId = new TextBox();
            lblMqttUsuario = new Label();
            txtMqttUsuario = new TextBox();
            lblMqttPassword = new Label();
            txtMqttPassword = new TextBox();
            chkMqttSslTls = new CheckBox();
            btnMqttConectar = new Button();
            btnMqttDesconectar = new Button();
            tabPageModbus = new TabPage();
            grpOperacionesModbus = new GroupBox();
            lblModbusDireccionRegistro = new Label();
            txtModbusDireccionRegistro = new TextBox();
            lblModbusValorEscritura = new Label();
            txtModbusValorEscritura = new TextBox();
            btnModbusLeerRegistros = new Button();
            btnModbusEscribirRegistro = new Button();
            lblModbusValorLeido = new Label();
            lblModbusAyuda = new Label();
            grpConexionModbus = new GroupBox();
            lblModbusIp = new Label();
            txtModbusIp = new TextBox();
            lblModbusPuerto = new Label();
            nudModbusPuerto = new NumericUpDown();
            lblModbusDeviceId = new Label();
            nudModbusDeviceId = new NumericUpDown();
            btnModbusConectar = new Button();
            btnModbusDesconectar = new Button();
            tabPageSinoptico = new TabPage();
            grpSinopticoInfo = new GroupBox();
            dgvSinopticoTopics = new DataGridView();
            lblSinopticoUltimaActualizacion = new Label();
            lblSinopticoEstado = new Label();
            lblTopicosActivos = new Label();
            lblUltimaActualizacion = new Label();
            dgvModbusResultados = new DataGridView();
            grpConfigModbus = new GroupBox();
            cmbModbusConfig = new ComboBox();
            txtModbusConfigNombre = new TextBox();
            btnModbusGuardarConfig = new Button();
            btnModbusNuevaConfig = new Button();
            timer1 = new System.Windows.Forms.Timer(components);
            grpSRAP = new GroupBox();
            btnSRAPParadar = new Button();
            btnSRAPParadam = new Button();
            btnSRAPParadal = new Button();
            btnSRAPPreseleccion = new Button();
            lblSRAPEstado = new Label();
            panelBarraSuperior.SuspendLayout();
            panelEstados.SuspendLayout();
            panelNavegacion.SuspendLayout();
            panelLog.SuspendLayout();
            tabControlPrincipal.SuspendLayout();
            tabPageCorreo.SuspendLayout();
            grpConfigCorreo.SuspendLayout();
            grpEstadosHorarios.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvEstadosHorarios).BeginInit();
            tabPageMqtt.SuspendLayout();
            groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvMqttTopics).BeginInit();
            grpConexionMqtt.SuspendLayout();
            tabPageModbus.SuspendLayout();
            grpOperacionesModbus.SuspendLayout();
            grpConexionModbus.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)nudModbusPuerto).BeginInit();
            ((System.ComponentModel.ISupportInitialize)nudModbusDeviceId).BeginInit();
            tabPageSinoptico.SuspendLayout();
            grpSinopticoInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvSinopticoTopics).BeginInit();
            grpSRAP.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvModbusResultados).BeginInit();
            grpConfigModbus.SuspendLayout();
            SuspendLayout();
            // 
            // panelBarraSuperior
            // 
            panelBarraSuperior.BackColor = Color.FromArgb(30, 30, 30);
            panelBarraSuperior.Controls.Add(lblTituloApp);
            panelBarraSuperior.Controls.Add(btnCerrar);
            panelBarraSuperior.Dock = DockStyle.Top;
            panelBarraSuperior.Location = new Point(0, 0);
            panelBarraSuperior.Name = "panelBarraSuperior";
            panelBarraSuperior.Size = new Size(1344, 56);
            panelBarraSuperior.TabIndex = 0;
            // 
            // lblTituloApp
            // 
            lblTituloApp.AutoSize = true;
            lblTituloApp.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            lblTituloApp.ForeColor = Color.White;
            lblTituloApp.Location = new Point(18, 14);
            lblTituloApp.Name = "lblTituloApp";
            lblTituloApp.Size = new Size(376, 32);
            lblTituloApp.TabIndex = 0;
            lblTituloApp.Text = "Control de Producción ENAGAS";
            // 
            // btnCerrar
            // 
            btnCerrar.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnCerrar.BackColor = Color.FromArgb(209, 17, 65);
            btnCerrar.FlatAppearance.BorderSize = 0;
            btnCerrar.FlatStyle = FlatStyle.Flat;
            btnCerrar.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnCerrar.ForeColor = Color.White;
            btnCerrar.Location = new Point(1301, 14);
            btnCerrar.Name = "btnCerrar";
            btnCerrar.Size = new Size(35, 28);
            btnCerrar.TabIndex = 1;
            btnCerrar.Text = "✕";
            btnCerrar.UseVisualStyleBackColor = false;
            btnCerrar.Click += btnCerrar_Click;
            // 
            // panelEstados
            // 
            panelEstados.BackColor = Color.FromArgb(50, 50, 50);
            panelEstados.Controls.Add(panelEstadoMqtt);
            panelEstados.Controls.Add(lblEstadoMqtt);
            panelEstados.Controls.Add(panelEstadoCorreo);
            panelEstados.Controls.Add(lblEstadoCorreo);
            panelEstados.Controls.Add(panelEstadoModbus);
            panelEstados.Controls.Add(lblEstadoModbus);
            panelEstados.Dock = DockStyle.Top;
            panelEstados.Location = new Point(0, 56);
            panelEstados.Name = "panelEstados";
            panelEstados.Size = new Size(1344, 47);
            panelEstados.TabIndex = 1;
            // 
            // panelEstadoMqtt
            // 
            panelEstadoMqtt.BackColor = Color.Gray;
            panelEstadoMqtt.Location = new Point(43, 14);
            panelEstadoMqtt.Name = "panelEstadoMqtt";
            panelEstadoMqtt.Size = new Size(18, 19);
            panelEstadoMqtt.TabIndex = 0;
            // 
            // lblEstadoMqtt
            // 
            lblEstadoMqtt.AutoSize = true;
            lblEstadoMqtt.Font = new Font("Segoe UI", 10F);
            lblEstadoMqtt.ForeColor = Color.White;
            lblEstadoMqtt.Location = new Point(70, 16);
            lblEstadoMqtt.Name = "lblEstadoMqtt";
            lblEstadoMqtt.Size = new Size(139, 19);
            lblEstadoMqtt.TabIndex = 1;
            lblEstadoMqtt.Text = "MQTT: Desconectado";
            // 
            // panelEstadoCorreo
            // 
            panelEstadoCorreo.BackColor = Color.Gray;
            panelEstadoCorreo.Location = new Point(218, 14);
            panelEstadoCorreo.Name = "panelEstadoCorreo";
            panelEstadoCorreo.Size = new Size(18, 19);
            panelEstadoCorreo.TabIndex = 2;
            // 
            // lblEstadoCorreo
            // 
            lblEstadoCorreo.AutoSize = true;
            lblEstadoCorreo.Font = new Font("Segoe UI", 10F);
            lblEstadoCorreo.ForeColor = Color.White;
            lblEstadoCorreo.Location = new Point(245, 16);
            lblEstadoCorreo.Name = "lblEstadoCorreo";
            lblEstadoCorreo.Size = new Size(144, 19);
            lblEstadoCorreo.TabIndex = 3;
            lblEstadoCorreo.Text = "Correo: Desconectado";
            // 
            // panelEstadoModbus
            // 
            panelEstadoModbus.BackColor = Color.Gray;
            panelEstadoModbus.Location = new Point(393, 14);
            panelEstadoModbus.Name = "panelEstadoModbus";
            panelEstadoModbus.Size = new Size(18, 19);
            panelEstadoModbus.TabIndex = 4;
            // 
            // lblEstadoModbus
            //
            lblEstadoModbus.AutoSize = true;
            lblEstadoModbus.Font = new Font("Segoe UI", 10F);
            lblEstadoModbus.ForeColor = Color.Red;
            lblEstadoModbus.Location = new Point(420, 16);
            lblEstadoModbus.Name = "lblEstadoModbus";
            lblEstadoModbus.Size = new Size(153, 19);
            lblEstadoModbus.TabIndex = 5;
            lblEstadoModbus.Text = "Modbus: Desconectado";
            // 
            // panelNavegacion
            // 
            panelNavegacion.BackColor = Color.FromArgb(60, 63, 65);
            panelNavegacion.Controls.Add(btnNavCorreo);
            panelNavegacion.Controls.Add(btnNavMqtt);
            panelNavegacion.Controls.Add(btnNavModbus);
            panelNavegacion.Controls.Add(btnNavSinoptico);
            panelNavegacion.Dock = DockStyle.Top;
            panelNavegacion.Location = new Point(0, 103);
            panelNavegacion.Name = "panelNavegacion";
            panelNavegacion.Size = new Size(1344, 56);
            panelNavegacion.TabIndex = 2;
            // 
            // btnNavCorreo
            // 
            btnNavCorreo.BackColor = Color.FromArgb(0, 122, 204);
            btnNavCorreo.FlatAppearance.BorderSize = 0;
            btnNavCorreo.FlatStyle = FlatStyle.Flat;
            btnNavCorreo.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            btnNavCorreo.ForeColor = Color.White;
            btnNavCorreo.Location = new Point(43, 14);
            btnNavCorreo.Name = "btnNavCorreo";
            btnNavCorreo.Size = new Size(175, 28);
            btnNavCorreo.TabIndex = 0;
            btnNavCorreo.Text = "📧 CORREO";
            btnNavCorreo.UseVisualStyleBackColor = false;
            btnNavCorreo.Click += btnNavCorreo_Click;
            // 
            // btnNavMqtt
            // 
            btnNavMqtt.BackColor = Color.FromArgb(60, 63, 65);
            btnNavMqtt.FlatAppearance.BorderSize = 0;
            btnNavMqtt.FlatStyle = FlatStyle.Flat;
            btnNavMqtt.Font = new Font("Segoe UI", 12F);
            btnNavMqtt.ForeColor = Color.White;
            btnNavMqtt.Location = new Point(237, 14);
            btnNavMqtt.Name = "btnNavMqtt";
            btnNavMqtt.Size = new Size(175, 28);
            btnNavMqtt.TabIndex = 1;
            btnNavMqtt.Text = "🌐 MQTT";
            btnNavMqtt.UseVisualStyleBackColor = false;
            btnNavMqtt.Click += btnNavMqtt_Click;
            // 
            // btnNavModbus
            // 
            btnNavModbus.BackColor = Color.FromArgb(60, 63, 65);
            btnNavModbus.FlatAppearance.BorderSize = 0;
            btnNavModbus.FlatStyle = FlatStyle.Flat;
            btnNavModbus.Font = new Font("Segoe UI", 12F);
            btnNavModbus.ForeColor = Color.White;
            btnNavModbus.Location = new Point(428, 14);
            btnNavModbus.Name = "btnNavModbus";
            btnNavModbus.Size = new Size(175, 28);
            btnNavModbus.TabIndex = 2;
            btnNavModbus.Text = "⚙️ MODBUS";
            btnNavModbus.UseVisualStyleBackColor = false;
            btnNavModbus.Click += btnNavModbus_Click;
            // 
            // btnNavSinoptico
            // 
            btnNavSinoptico.BackColor = Color.FromArgb(60, 63, 65);
            btnNavSinoptico.FlatAppearance.BorderSize = 0;
            btnNavSinoptico.FlatStyle = FlatStyle.Flat;
            btnNavSinoptico.Font = new Font("Segoe UI", 12F);
            btnNavSinoptico.ForeColor = Color.White;
            btnNavSinoptico.Location = new Point(622, 14);
            btnNavSinoptico.Name = "btnNavSinoptico";
            btnNavSinoptico.Size = new Size(175, 28);
            btnNavSinoptico.TabIndex = 3;
            btnNavSinoptico.Text = "📊 SINÓPTICO";
            btnNavSinoptico.UseVisualStyleBackColor = false;
            btnNavSinoptico.Click += btnNavSinoptico_Click;
            // 
            // panelLog
            // 
            panelLog.BackColor = Color.FromArgb(40, 40, 40);
            panelLog.Controls.Add(rtbLog);
            panelLog.Controls.Add(btnLimpiarLog);
            panelLog.Dock = DockStyle.Bottom;
            panelLog.Location = new Point(0, 505);
            panelLog.Name = "panelLog";
            panelLog.Size = new Size(1344, 187);
            panelLog.TabIndex = 4;
            panelLog.Paint += panelLog_Paint;
            // 
            // rtbLog
            // 
            rtbLog.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            rtbLog.BackColor = Color.FromArgb(30, 30, 30);
            rtbLog.BorderStyle = BorderStyle.None;
            rtbLog.Font = new Font("Consolas", 9F);
            rtbLog.ForeColor = Color.LightGray;
            rtbLog.Location = new Point(18, 19);
            rtbLog.Name = "rtbLog";
            rtbLog.ReadOnly = true;
            rtbLog.Size = new Size(1233, 150);
            rtbLog.TabIndex = 0;
            rtbLog.Text = "=== LOG DEL SISTEMA ===\nSistema iniciado correctamente...\n";
            // 
            // btnLimpiarLog
            // 
            btnLimpiarLog.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnLimpiarLog.BackColor = Color.FromArgb(209, 17, 65);
            btnLimpiarLog.FlatAppearance.BorderSize = 0;
            btnLimpiarLog.FlatStyle = FlatStyle.Flat;
            btnLimpiarLog.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnLimpiarLog.ForeColor = Color.White;
            btnLimpiarLog.Location = new Point(1266, 19);
            btnLimpiarLog.Name = "btnLimpiarLog";
            btnLimpiarLog.Size = new Size(70, 28);
            btnLimpiarLog.TabIndex = 1;
            btnLimpiarLog.Text = "Limpiar";
            btnLimpiarLog.UseVisualStyleBackColor = false;
            btnLimpiarLog.Click += btnLimpiarLog_Click;
            // 
            // tabControlPrincipal
            //
            tabControlPrincipal.Appearance = TabAppearance.FlatButtons;
            tabControlPrincipal.Controls.Add(tabPageCorreo);
            tabControlPrincipal.Controls.Add(tabPageMqtt);
            tabControlPrincipal.Controls.Add(tabPageModbus);
            tabControlPrincipal.Controls.Add(tabPageSinoptico);
            tabControlPrincipal.Dock = DockStyle.Fill;
            tabControlPrincipal.ItemSize = new Size(0, 1);
            tabControlPrincipal.Location = new Point(0, 159);
            tabControlPrincipal.Name = "tabControlPrincipal";
            tabControlPrincipal.SelectedIndex = 0;
            tabControlPrincipal.Size = new Size(1344, 346);
            tabControlPrincipal.SizeMode = TabSizeMode.Fixed;
            tabControlPrincipal.TabIndex = 5;
            // 
            // tabPageCorreo
            // 
            tabPageCorreo.BackColor = Color.FromArgb(45, 45, 48);
            tabPageCorreo.Controls.Add(grpConfigCorreo);
            tabPageCorreo.Controls.Add(grpEstadosHorarios);
            tabPageCorreo.Location = new Point(4, 24);
            tabPageCorreo.Name = "tabPageCorreo";
            tabPageCorreo.Padding = new Padding(3);
            tabPageCorreo.Size = new Size(1336, 318);
            tabPageCorreo.TabIndex = 0;
            tabPageCorreo.Text = "Correo";
            // 
            // grpConfigCorreo
            // 
            grpConfigCorreo.Controls.Add(lblEwsServerUrl);
            grpConfigCorreo.Controls.Add(txtEwsServerUrl);
            grpConfigCorreo.Controls.Add(lblEwsUsuario);
            grpConfigCorreo.Controls.Add(txtEwsUsuario);
            grpConfigCorreo.Controls.Add(lblEwsPassword);
            grpConfigCorreo.Controls.Add(txtEwsPassword);
            grpConfigCorreo.Controls.Add(lblEwsDominio);
            grpConfigCorreo.Controls.Add(txtEwsDominio);
            grpConfigCorreo.Controls.Add(lblEmailNotificacion);
            grpConfigCorreo.Controls.Add(txtEmailNotificacion);
            grpConfigCorreo.Controls.Add(btnConectarCorreo);
            grpConfigCorreo.Controls.Add(btnDesconectarCorreo);
            grpConfigCorreo.Controls.Add(btnProcesarCorreos);
            grpConfigCorreo.Controls.Add(btnForzarCambioHora);
            grpConfigCorreo.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            grpConfigCorreo.ForeColor = Color.White;
            grpConfigCorreo.Location = new Point(43, 28);
            grpConfigCorreo.Name = "grpConfigCorreo";
            grpConfigCorreo.Size = new Size(700, 281);
            grpConfigCorreo.TabIndex = 0;
            grpConfigCorreo.TabStop = false;
            grpConfigCorreo.Text = "Configuración Exchange (EWS)";
            // 
            // lblEwsServerUrl
            // 
            lblEwsServerUrl.AutoSize = true;
            lblEwsServerUrl.Font = new Font("Segoe UI", 11F);
            lblEwsServerUrl.ForeColor = Color.White;
            lblEwsServerUrl.Location = new Point(27, 47);
            lblEwsServerUrl.Name = "lblEwsServerUrl";
            lblEwsServerUrl.Size = new Size(122, 20);
            lblEwsServerUrl.TabIndex = 0;
            lblEwsServerUrl.Text = "URL del Servidor:";
            // 
            // txtEwsServerUrl
            // 
            txtEwsServerUrl.BackColor = Color.FromArgb(60, 63, 65);
            txtEwsServerUrl.BorderStyle = BorderStyle.FixedSingle;
            txtEwsServerUrl.Font = new Font("Segoe UI", 11F);
            txtEwsServerUrl.ForeColor = Color.White;
            txtEwsServerUrl.Location = new Point(175, 45);
            txtEwsServerUrl.Name = "txtEwsServerUrl";
            txtEwsServerUrl.Size = new Size(482, 27);
            txtEwsServerUrl.TabIndex = 1;
            txtEwsServerUrl.Text = "https://outlook.office365.com/EWS/Exchange.asmx";
            // 
            // lblEwsUsuario
            // 
            lblEwsUsuario.AutoSize = true;
            lblEwsUsuario.Font = new Font("Segoe UI", 11F);
            lblEwsUsuario.ForeColor = Color.White;
            lblEwsUsuario.Location = new Point(27, 94);
            lblEwsUsuario.Name = "lblEwsUsuario";
            lblEwsUsuario.Size = new Size(62, 20);
            lblEwsUsuario.TabIndex = 2;
            lblEwsUsuario.Text = "Usuario:";
            // 
            // txtEwsUsuario
            // 
            txtEwsUsuario.BackColor = Color.FromArgb(60, 63, 65);
            txtEwsUsuario.BorderStyle = BorderStyle.FixedSingle;
            txtEwsUsuario.Font = new Font("Segoe UI", 11F);
            txtEwsUsuario.ForeColor = Color.White;
            txtEwsUsuario.Location = new Point(175, 92);
            txtEwsUsuario.Name = "txtEwsUsuario";
            txtEwsUsuario.Size = new Size(351, 27);
            txtEwsUsuario.TabIndex = 3;
            // 
            // lblEwsPassword
            // 
            lblEwsPassword.AutoSize = true;
            lblEwsPassword.Font = new Font("Segoe UI", 11F);
            lblEwsPassword.ForeColor = Color.White;
            lblEwsPassword.Location = new Point(27, 140);
            lblEwsPassword.Name = "lblEwsPassword";
            lblEwsPassword.Size = new Size(86, 20);
            lblEwsPassword.TabIndex = 4;
            lblEwsPassword.Text = "Contraseña:";
            // 
            // txtEwsPassword
            // 
            txtEwsPassword.BackColor = Color.FromArgb(60, 63, 65);
            txtEwsPassword.BorderStyle = BorderStyle.FixedSingle;
            txtEwsPassword.Font = new Font("Segoe UI", 11F);
            txtEwsPassword.ForeColor = Color.White;
            txtEwsPassword.Location = new Point(175, 139);
            txtEwsPassword.Name = "txtEwsPassword";
            txtEwsPassword.PasswordChar = '*';
            txtEwsPassword.Size = new Size(351, 27);
            txtEwsPassword.TabIndex = 5;
            //
            // lblEwsDominio
            //
            lblEwsDominio.AutoSize = true;
            lblEwsDominio.Font = new Font("Segoe UI", 11F);
            lblEwsDominio.ForeColor = Color.White;
            lblEwsDominio.Location = new Point(27, 186);
            lblEwsDominio.Name = "lblEwsDominio";
            lblEwsDominio.Size = new Size(70, 20);
            lblEwsDominio.TabIndex = 6;
            lblEwsDominio.Text = "Dominio:";
            //
            // txtEwsDominio
            //
            txtEwsDominio.BackColor = Color.FromArgb(60, 63, 65);
            txtEwsDominio.BorderStyle = BorderStyle.FixedSingle;
            txtEwsDominio.Font = new Font("Segoe UI", 11F);
            txtEwsDominio.ForeColor = Color.White;
            txtEwsDominio.Location = new Point(175, 184);
            txtEwsDominio.Name = "txtEwsDominio";
            txtEwsDominio.Size = new Size(351, 27);
            txtEwsDominio.TabIndex = 7;
            //
            // lblEmailNotificacion
            //
            lblEmailNotificacion.AutoSize = true;
            lblEmailNotificacion.Font = new Font("Segoe UI", 11F);
            lblEmailNotificacion.ForeColor = Color.White;
            lblEmailNotificacion.Location = new Point(27, 232);
            lblEmailNotificacion.Name = "lblEmailNotificacion";
            lblEmailNotificacion.Size = new Size(142, 20);
            lblEmailNotificacion.TabIndex = 8;
            lblEmailNotificacion.Text = "Email Notificación:";
            //
            // txtEmailNotificacion
            //
            txtEmailNotificacion.BackColor = Color.FromArgb(60, 63, 65);
            txtEmailNotificacion.BorderStyle = BorderStyle.FixedSingle;
            txtEmailNotificacion.Font = new Font("Segoe UI", 11F);
            txtEmailNotificacion.ForeColor = Color.White;
            txtEmailNotificacion.Location = new Point(175, 230);
            txtEmailNotificacion.Name = "txtEmailNotificacion";
            txtEmailNotificacion.Size = new Size(351, 27);
            txtEmailNotificacion.TabIndex = 9;
            txtEmailNotificacion.Text = "<EMAIL>";
            //
            // btnConectarCorreo
            //
            btnConectarCorreo.BackColor = Color.FromArgb(0, 122, 204);
            btnConectarCorreo.FlatAppearance.BorderSize = 0;
            btnConectarCorreo.FlatStyle = FlatStyle.Flat;
            btnConectarCorreo.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnConectarCorreo.ForeColor = Color.White;
            btnConectarCorreo.Location = new Point(550, 139);
            btnConectarCorreo.Name = "btnConectarCorreo";
            btnConectarCorreo.Size = new Size(107, 28);
            btnConectarCorreo.TabIndex = 10;
            btnConectarCorreo.Text = "Conectar";
            btnConectarCorreo.UseVisualStyleBackColor = false;
            //
            // btnDesconectarCorreo
            //
            btnDesconectarCorreo.BackColor = Color.FromArgb(204, 122, 0);
            btnDesconectarCorreo.FlatAppearance.BorderSize = 0;
            btnDesconectarCorreo.FlatStyle = FlatStyle.Flat;
            btnDesconectarCorreo.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnDesconectarCorreo.ForeColor = Color.White;
            btnDesconectarCorreo.Location = new Point(550, 184);
            btnDesconectarCorreo.Name = "btnDesconectarCorreo";
            btnDesconectarCorreo.Size = new Size(107, 28);
            btnDesconectarCorreo.TabIndex = 11;
            btnDesconectarCorreo.Text = "Desconectar";
            btnDesconectarCorreo.UseVisualStyleBackColor = false;
            //
            // btnProcesarCorreos
            //
            btnProcesarCorreos.BackColor = Color.FromArgb(0, 204, 122);
            btnProcesarCorreos.FlatAppearance.BorderSize = 0;
            btnProcesarCorreos.FlatStyle = FlatStyle.Flat;
            btnProcesarCorreos.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnProcesarCorreos.ForeColor = Color.White;
            btnProcesarCorreos.Location = new Point(550, 230);
            btnProcesarCorreos.Name = "btnProcesarCorreos";
            btnProcesarCorreos.Size = new Size(107, 28);
            btnProcesarCorreos.TabIndex = 12;
            btnProcesarCorreos.Text = "Procesar";
            btnProcesarCorreos.UseVisualStyleBackColor = false;
            //
            // btnForzarCambioHora
            //
            btnForzarCambioHora.BackColor = Color.FromArgb(255, 165, 0);
            btnForzarCambioHora.FlatAppearance.BorderSize = 0;
            btnForzarCambioHora.FlatStyle = FlatStyle.Flat;
            btnForzarCambioHora.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnForzarCambioHora.ForeColor = Color.White;
            btnForzarCambioHora.Location = new Point(430, 230);
            btnForzarCambioHora.Name = "btnForzarCambioHora";
            btnForzarCambioHora.Size = new Size(107, 28);
            btnForzarCambioHora.TabIndex = 13;
            btnForzarCambioHora.Text = "Mañana→Hoy";
            btnForzarCambioHora.UseVisualStyleBackColor = false;
            //
            // grpEstadosHorarios
            //
            grpEstadosHorarios.Controls.Add(dgvEstadosHorarios);
            grpEstadosHorarios.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            grpEstadosHorarios.ForeColor = Color.White;
            grpEstadosHorarios.Location = new Point(770, 28);
            grpEstadosHorarios.Name = "grpEstadosHorarios";
            grpEstadosHorarios.Size = new Size(540, 281);
            grpEstadosHorarios.TabIndex = 1;
            grpEstadosHorarios.TabStop = false;
            grpEstadosHorarios.Text = "Estados Horarios (H1-H24)";
            //
            // dgvEstadosHorarios
            //
            dgvEstadosHorarios.AllowUserToAddRows = false;
            dgvEstadosHorarios.AllowUserToDeleteRows = false;
            dgvEstadosHorarios.BackgroundColor = Color.FromArgb(60, 63, 65);
            dgvEstadosHorarios.BorderStyle = BorderStyle.None;
            dgvEstadosHorarios.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(45, 45, 48);
            dgvEstadosHorarios.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvEstadosHorarios.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            dgvEstadosHorarios.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvEstadosHorarios.DefaultCellStyle.BackColor = Color.FromArgb(60, 63, 65);
            dgvEstadosHorarios.DefaultCellStyle.ForeColor = Color.White;
            dgvEstadosHorarios.DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            dgvEstadosHorarios.GridColor = Color.FromArgb(100, 100, 100);
            dgvEstadosHorarios.Location = new Point(15, 30);
            dgvEstadosHorarios.Name = "dgvEstadosHorarios";
            dgvEstadosHorarios.ReadOnly = true;
            dgvEstadosHorarios.RowHeadersVisible = false;
            dgvEstadosHorarios.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvEstadosHorarios.Size = new Size(510, 240);
            dgvEstadosHorarios.TabIndex = 0;
            //
            // tabPageMqtt
            // 
            tabPageMqtt.BackColor = Color.FromArgb(45, 45, 48);
            tabPageMqtt.Controls.Add(groupBox1);
            tabPageMqtt.Controls.Add(grpConexionMqtt);
            tabPageMqtt.Location = new Point(4, 24);
            tabPageMqtt.Name = "tabPageMqtt";
            tabPageMqtt.Padding = new Padding(3);
            tabPageMqtt.Size = new Size(1336, 318);
            tabPageMqtt.TabIndex = 1;
            tabPageMqtt.Text = "MQTT";
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(btnRefrescarMQTT);
            groupBox1.Controls.Add(dgvMqttTopics);
            groupBox1.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            groupBox1.ForeColor = Color.White;
            groupBox1.Location = new Point(762, 28);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(558, 300);
            groupBox1.TabIndex = 2;
            groupBox1.TabStop = false;
            groupBox1.Text = "Topics";
            // 
            // btnRefrescarMQTT
            // 
            btnRefrescarMQTT.BackColor = Color.FromArgb(0, 122, 204);
            btnRefrescarMQTT.FlatAppearance.BorderSize = 0;
            btnRefrescarMQTT.FlatStyle = FlatStyle.Flat;
            btnRefrescarMQTT.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnRefrescarMQTT.ForeColor = Color.White;
            btnRefrescarMQTT.Location = new Point(17, 24);
            btnRefrescarMQTT.Margin = new Padding(2);
            btnRefrescarMQTT.Name = "btnRefrescarMQTT";
            btnRefrescarMQTT.Size = new Size(105, 28);
            btnRefrescarMQTT.TabIndex = 14;
            btnRefrescarMQTT.Text = "Refrescar";
            btnRefrescarMQTT.UseVisualStyleBackColor = false;
            btnRefrescarMQTT.Click += btnRefrescarMQTT_Click;
            // 
            // dgvMqttTopics
            // 
            dgvMqttTopics.AllowUserToAddRows = false;
            dgvMqttTopics.AllowUserToDeleteRows = false;
            dgvMqttTopics.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvMqttTopics.Location = new Point(17, 58);
            dgvMqttTopics.Margin = new Padding(2);
            dgvMqttTopics.Name = "dgvMqttTopics";
            dgvMqttTopics.ReadOnly = true;
            dgvMqttTopics.RowHeadersWidth = 62;
            dgvMqttTopics.Size = new Size(529, 230);
            dgvMqttTopics.TabIndex = 0;
            // 
            // grpConexionMqtt
            // 
            grpConexionMqtt.Controls.Add(lblMqttProtocolo);
            grpConexionMqtt.Controls.Add(cmbMqttProtocolo);
            grpConexionMqtt.Controls.Add(lblMqttHost);
            grpConexionMqtt.Controls.Add(txtMqttHost);
            grpConexionMqtt.Controls.Add(lblMqttPuerto);
            grpConexionMqtt.Controls.Add(txtMqttPuerto);
            grpConexionMqtt.Controls.Add(lblMqttClientId);
            grpConexionMqtt.Controls.Add(txtMqttClientId);
            grpConexionMqtt.Controls.Add(lblMqttUsuario);
            grpConexionMqtt.Controls.Add(txtMqttUsuario);
            grpConexionMqtt.Controls.Add(lblMqttPassword);
            grpConexionMqtt.Controls.Add(txtMqttPassword);
            grpConexionMqtt.Controls.Add(chkMqttSslTls);
            grpConexionMqtt.Controls.Add(btnMqttConectar);
            grpConexionMqtt.Controls.Add(btnMqttDesconectar);
            grpConexionMqtt.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            grpConexionMqtt.ForeColor = Color.White;
            grpConexionMqtt.Location = new Point(43, 28);
            grpConexionMqtt.Name = "grpConexionMqtt";
            grpConexionMqtt.Size = new Size(700, 300);
            grpConexionMqtt.TabIndex = 1;
            grpConexionMqtt.TabStop = false;
            grpConexionMqtt.Text = "Conexión MQTT";
            // 
            // lblMqttProtocolo
            // 
            lblMqttProtocolo.AutoSize = true;
            lblMqttProtocolo.Font = new Font("Segoe UI", 11F);
            lblMqttProtocolo.ForeColor = Color.White;
            lblMqttProtocolo.Location = new Point(27, 47);
            lblMqttProtocolo.Name = "lblMqttProtocolo";
            lblMqttProtocolo.Size = new Size(77, 20);
            lblMqttProtocolo.TabIndex = 0;
            lblMqttProtocolo.Text = "Protocolo:";
            // 
            // cmbMqttProtocolo
            // 
            cmbMqttProtocolo.BackColor = Color.FromArgb(60, 63, 65);
            cmbMqttProtocolo.ForeColor = Color.White;
            cmbMqttProtocolo.FormattingEnabled = true;
            cmbMqttProtocolo.Items.AddRange(new object[] { "mqtt://", "mqtts://", "ws://", "wss://" });
            cmbMqttProtocolo.Location = new Point(132, 45);
            cmbMqttProtocolo.Name = "cmbMqttProtocolo";
            cmbMqttProtocolo.Size = new Size(106, 29);
            cmbMqttProtocolo.TabIndex = 1;
            // 
            // lblMqttHost
            // 
            lblMqttHost.AutoSize = true;
            lblMqttHost.Font = new Font("Segoe UI", 11F);
            lblMqttHost.Location = new Point(252, 47);
            lblMqttHost.Margin = new Padding(2, 0, 2, 0);
            lblMqttHost.Name = "lblMqttHost";
            lblMqttHost.Size = new Size(43, 20);
            lblMqttHost.TabIndex = 2;
            lblMqttHost.Text = "Host:";
            // 
            // txtMqttHost
            // 
            txtMqttHost.BackColor = Color.FromArgb(60, 63, 65);
            txtMqttHost.BorderStyle = BorderStyle.FixedSingle;
            txtMqttHost.Font = new Font("Segoe UI", 11F);
            txtMqttHost.ForeColor = Color.White;
            txtMqttHost.Location = new Point(301, 45);
            txtMqttHost.Margin = new Padding(2);
            txtMqttHost.Name = "txtMqttHost";
            txtMqttHost.Size = new Size(176, 27);
            txtMqttHost.TabIndex = 3;
            // 
            // lblMqttPuerto
            // 
            lblMqttPuerto.AutoSize = true;
            lblMqttPuerto.Font = new Font("Segoe UI", 11F);
            lblMqttPuerto.Location = new Point(490, 47);
            lblMqttPuerto.Margin = new Padding(2, 0, 2, 0);
            lblMqttPuerto.Name = "lblMqttPuerto";
            lblMqttPuerto.Size = new Size(55, 20);
            lblMqttPuerto.TabIndex = 4;
            lblMqttPuerto.Text = "Puerto:";
            // 
            // txtMqttPuerto
            // 
            txtMqttPuerto.BackColor = Color.FromArgb(60, 63, 65);
            txtMqttPuerto.BorderStyle = BorderStyle.FixedSingle;
            txtMqttPuerto.Font = new Font("Segoe UI", 11F);
            txtMqttPuerto.ForeColor = Color.White;
            txtMqttPuerto.Location = new Point(553, 45);
            txtMqttPuerto.Margin = new Padding(2);
            txtMqttPuerto.Name = "txtMqttPuerto";
            txtMqttPuerto.Size = new Size(71, 27);
            txtMqttPuerto.TabIndex = 5;
            // 
            // lblMqttClientId
            // 
            lblMqttClientId.AutoSize = true;
            lblMqttClientId.Font = new Font("Segoe UI", 11F);
            lblMqttClientId.Location = new Point(27, 84);
            lblMqttClientId.Margin = new Padding(2, 0, 2, 0);
            lblMqttClientId.Name = "lblMqttClientId";
            lblMqttClientId.Size = new Size(69, 20);
            lblMqttClientId.TabIndex = 6;
            lblMqttClientId.Text = "Client ID:";
            // 
            // txtMqttClientId
            // 
            txtMqttClientId.BackColor = Color.FromArgb(60, 63, 65);
            txtMqttClientId.BorderStyle = BorderStyle.FixedSingle;
            txtMqttClientId.Font = new Font("Segoe UI", 11F);
            txtMqttClientId.ForeColor = Color.White;
            txtMqttClientId.Location = new Point(132, 83);
            txtMqttClientId.Margin = new Padding(2);
            txtMqttClientId.Name = "txtMqttClientId";
            txtMqttClientId.Size = new Size(281, 27);
            txtMqttClientId.TabIndex = 7;
            // 
            // lblMqttUsuario
            // 
            lblMqttUsuario.AutoSize = true;
            lblMqttUsuario.Font = new Font("Segoe UI", 11F);
            lblMqttUsuario.Location = new Point(27, 120);
            lblMqttUsuario.Margin = new Padding(2, 0, 2, 0);
            lblMqttUsuario.Name = "lblMqttUsuario";
            lblMqttUsuario.Size = new Size(62, 20);
            lblMqttUsuario.TabIndex = 8;
            lblMqttUsuario.Text = "Usuario:";
            // 
            // txtMqttUsuario
            // 
            txtMqttUsuario.BackColor = Color.FromArgb(60, 63, 65);
            txtMqttUsuario.BorderStyle = BorderStyle.FixedSingle;
            txtMqttUsuario.Font = new Font("Segoe UI", 11F);
            txtMqttUsuario.ForeColor = Color.White;
            txtMqttUsuario.Location = new Point(132, 119);
            txtMqttUsuario.Margin = new Padding(2);
            txtMqttUsuario.Name = "txtMqttUsuario";
            txtMqttUsuario.Size = new Size(281, 27);
            txtMqttUsuario.TabIndex = 9;
            // 
            // lblMqttPassword
            // 
            lblMqttPassword.AutoSize = true;
            lblMqttPassword.Font = new Font("Segoe UI", 11F);
            lblMqttPassword.Location = new Point(27, 156);
            lblMqttPassword.Margin = new Padding(2, 0, 2, 0);
            lblMqttPassword.Name = "lblMqttPassword";
            lblMqttPassword.Size = new Size(86, 20);
            lblMqttPassword.TabIndex = 10;
            lblMqttPassword.Text = "Contraseña:";
            // 
            // txtMqttPassword
            // 
            txtMqttPassword.BackColor = Color.FromArgb(60, 63, 65);
            txtMqttPassword.BorderStyle = BorderStyle.FixedSingle;
            txtMqttPassword.Font = new Font("Segoe UI", 11F);
            txtMqttPassword.ForeColor = Color.White;
            txtMqttPassword.Location = new Point(132, 155);
            txtMqttPassword.Margin = new Padding(2);
            txtMqttPassword.Name = "txtMqttPassword";
            txtMqttPassword.PasswordChar = '*';
            txtMqttPassword.Size = new Size(281, 27);
            txtMqttPassword.TabIndex = 11;
            // 
            // chkMqttSslTls
            // 
            chkMqttSslTls.AutoSize = true;
            chkMqttSslTls.Font = new Font("Segoe UI", 11F);
            chkMqttSslTls.Location = new Point(29, 198);
            chkMqttSslTls.Margin = new Padding(2);
            chkMqttSslTls.Name = "chkMqttSslTls";
            chkMqttSslTls.Size = new Size(142, 24);
            chkMqttSslTls.TabIndex = 12;
            chkMqttSslTls.Text = "Habilitar SSL/TLS";
            chkMqttSslTls.UseVisualStyleBackColor = true;
            // 
            // btnMqttConectar
            // 
            btnMqttConectar.BackColor = Color.FromArgb(0, 122, 204);
            btnMqttConectar.FlatAppearance.BorderSize = 0;
            btnMqttConectar.FlatStyle = FlatStyle.Flat;
            btnMqttConectar.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnMqttConectar.ForeColor = Color.White;
            btnMqttConectar.Location = new Point(29, 240);
            btnMqttConectar.Margin = new Padding(2);
            btnMqttConectar.Name = "btnMqttConectar";
            btnMqttConectar.Size = new Size(105, 28);
            btnMqttConectar.TabIndex = 13;
            btnMqttConectar.Text = "Conectar";
            btnMqttConectar.UseVisualStyleBackColor = false;
            btnMqttConectar.Click += btnMqttConectar_Click;
            // 
            // btnMqttDesconectar
            // 
            btnMqttDesconectar.BackColor = Color.FromArgb(209, 17, 65);
            btnMqttDesconectar.FlatAppearance.BorderSize = 0;
            btnMqttDesconectar.FlatStyle = FlatStyle.Flat;
            btnMqttDesconectar.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnMqttDesconectar.ForeColor = Color.White;
            btnMqttDesconectar.Location = new Point(148, 240);
            btnMqttDesconectar.Margin = new Padding(2);
            btnMqttDesconectar.Name = "btnMqttDesconectar";
            btnMqttDesconectar.Size = new Size(105, 28);
            btnMqttDesconectar.TabIndex = 14;
            btnMqttDesconectar.Text = "Desconectar";
            btnMqttDesconectar.UseVisualStyleBackColor = false;
            btnMqttDesconectar.Click += btnMqttDesconectar_Click;
            // 
            // tabPageModbus
            // 
            tabPageModbus.BackColor = Color.FromArgb(45, 45, 48);
            tabPageModbus.Controls.Add(grpOperacionesModbus);
            tabPageModbus.Controls.Add(grpConexionModbus);
            tabPageModbus.Location = new Point(4, 24);
            tabPageModbus.Name = "tabPageModbus";
            tabPageModbus.Size = new Size(1336, 318);
            tabPageModbus.TabIndex = 2;
            tabPageModbus.Text = "Modbus";
            // 
            // grpOperacionesModbus
            // 
            grpOperacionesModbus.Controls.Add(lblModbusDireccionRegistro);
            grpOperacionesModbus.Controls.Add(txtModbusDireccionRegistro);
            grpOperacionesModbus.Controls.Add(lblModbusValorEscritura);
            grpOperacionesModbus.Controls.Add(txtModbusValorEscritura);
            grpOperacionesModbus.Controls.Add(btnModbusLeerRegistros);
            grpOperacionesModbus.Controls.Add(btnModbusEscribirRegistro);
            grpOperacionesModbus.Controls.Add(lblModbusValorLeido);
            grpOperacionesModbus.Controls.Add(lblModbusAyuda);
            grpOperacionesModbus.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            grpOperacionesModbus.ForeColor = Color.White;
            grpOperacionesModbus.Location = new Point(378, 12);
            grpOperacionesModbus.Margin = new Padding(2);
            grpOperacionesModbus.Name = "grpOperacionesModbus";
            grpOperacionesModbus.Padding = new Padding(2);
            grpOperacionesModbus.Size = new Size(522, 168);
            grpOperacionesModbus.TabIndex = 1;
            grpOperacionesModbus.TabStop = false;
            grpOperacionesModbus.Text = "📊 Operaciones de Lectura/Escritura";
            // 
            // lblModbusDireccionRegistro
            // 
            lblModbusDireccionRegistro.AutoSize = true;
            lblModbusDireccionRegistro.Font = new Font("Segoe UI", 11F);
            lblModbusDireccionRegistro.Location = new Point(14, 29);
            lblModbusDireccionRegistro.Margin = new Padding(2, 0, 2, 0);
            lblModbusDireccionRegistro.Name = "lblModbusDireccionRegistro";
            lblModbusDireccionRegistro.Size = new Size(75, 20);
            lblModbusDireccionRegistro.TabIndex = 0;
            lblModbusDireccionRegistro.Text = "Dirección:";
            // 
            // txtModbusDireccionRegistro
            // 
            txtModbusDireccionRegistro.BackColor = Color.FromArgb(60, 63, 65);
            txtModbusDireccionRegistro.BorderStyle = BorderStyle.FixedSingle;
            txtModbusDireccionRegistro.Font = new Font("Segoe UI", 11F);
            txtModbusDireccionRegistro.ForeColor = Color.White;
            txtModbusDireccionRegistro.Location = new Point(93, 27);
            txtModbusDireccionRegistro.Margin = new Padding(2);
            txtModbusDireccionRegistro.Name = "txtModbusDireccionRegistro";
            txtModbusDireccionRegistro.Size = new Size(85, 27);
            txtModbusDireccionRegistro.TabIndex = 1;
            // 
            // lblModbusValorEscritura
            // 
            lblModbusValorEscritura.AutoSize = true;
            lblModbusValorEscritura.Font = new Font("Segoe UI", 11F);
            lblModbusValorEscritura.Location = new Point(196, 30);
            lblModbusValorEscritura.Margin = new Padding(2, 0, 2, 0);
            lblModbusValorEscritura.Name = "lblModbusValorEscritura";
            lblModbusValorEscritura.Size = new Size(46, 20);
            lblModbusValorEscritura.TabIndex = 2;
            lblModbusValorEscritura.Text = "Valor:";
            // 
            // txtModbusValorEscritura
            // 
            txtModbusValorEscritura.BackColor = Color.FromArgb(60, 63, 65);
            txtModbusValorEscritura.BorderStyle = BorderStyle.FixedSingle;
            txtModbusValorEscritura.Font = new Font("Segoe UI", 11F);
            txtModbusValorEscritura.ForeColor = Color.White;
            txtModbusValorEscritura.Location = new Point(246, 28);
            txtModbusValorEscritura.Margin = new Padding(2);
            txtModbusValorEscritura.Name = "txtModbusValorEscritura";
            txtModbusValorEscritura.Size = new Size(85, 27);
            txtModbusValorEscritura.TabIndex = 3;
            // 
            // btnModbusLeerRegistros
            // 
            btnModbusLeerRegistros.BackColor = Color.FromArgb(0, 122, 204);
            btnModbusLeerRegistros.FlatAppearance.BorderSize = 0;
            btnModbusLeerRegistros.FlatStyle = FlatStyle.Flat;
            btnModbusLeerRegistros.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnModbusLeerRegistros.Location = new Point(17, 66);
            btnModbusLeerRegistros.Margin = new Padding(2);
            btnModbusLeerRegistros.Name = "btnModbusLeerRegistros";
            btnModbusLeerRegistros.Size = new Size(105, 28);
            btnModbusLeerRegistros.TabIndex = 4;
            btnModbusLeerRegistros.Text = "📖 Leer";
            btnModbusLeerRegistros.UseVisualStyleBackColor = false;
            btnModbusLeerRegistros.Click += btnModbusLeerRegistros_Click;
            // 
            // btnModbusEscribirRegistro
            // 
            btnModbusEscribirRegistro.BackColor = Color.FromArgb(209, 17, 65);
            btnModbusEscribirRegistro.Enabled = false;
            btnModbusEscribirRegistro.FlatAppearance.BorderSize = 0;
            btnModbusEscribirRegistro.FlatStyle = FlatStyle.Flat;
            btnModbusEscribirRegistro.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnModbusEscribirRegistro.Location = new Point(133, 66);
            btnModbusEscribirRegistro.Margin = new Padding(2);
            btnModbusEscribirRegistro.Name = "btnModbusEscribirRegistro";
            btnModbusEscribirRegistro.Size = new Size(105, 28);
            btnModbusEscribirRegistro.TabIndex = 5;
            btnModbusEscribirRegistro.Text = "✏️ Escribir";
            btnModbusEscribirRegistro.UseVisualStyleBackColor = false;
            btnModbusEscribirRegistro.Click += btnModbusEscribirRegistro_Click;
            // 
            // lblModbusValorLeido
            //
            lblModbusValorLeido.AutoSize = true;
            lblModbusValorLeido.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            lblModbusValorLeido.ForeColor = Color.Gray;
            lblModbusValorLeido.Location = new Point(354, 30);
            lblModbusValorLeido.Margin = new Padding(2, 0, 2, 0);
            lblModbusValorLeido.Name = "lblModbusValorLeido";
            lblModbusValorLeido.Size = new Size(86, 25);
            lblModbusValorLeido.TabIndex = 6;
            lblModbusValorLeido.Text = "Valor: --";
            // 
            // lblModbusAyuda
            // 
            lblModbusAyuda.AutoSize = true;
            lblModbusAyuda.Font = new Font("Segoe UI", 9F);
            lblModbusAyuda.ForeColor = Color.LightGray;
            lblModbusAyuda.Location = new Point(14, 102);
            lblModbusAyuda.Margin = new Padding(2, 0, 2, 0);
            lblModbusAyuda.Name = "lblModbusAyuda";
            lblModbusAyuda.Size = new Size(483, 15);
            lblModbusAyuda.TabIndex = 7;
            lblModbusAyuda.Text = "💡 Registro 430: Prueba Holding Register → Input Register | Valor mostrado en LABEL arriba";
            // 
            // grpConexionModbus
            // 
            grpConexionModbus.Controls.Add(lblModbusIp);
            grpConexionModbus.Controls.Add(txtModbusIp);
            grpConexionModbus.Controls.Add(lblModbusPuerto);
            grpConexionModbus.Controls.Add(nudModbusPuerto);
            grpConexionModbus.Controls.Add(lblModbusDeviceId);
            grpConexionModbus.Controls.Add(nudModbusDeviceId);
            grpConexionModbus.Controls.Add(btnModbusConectar);
            grpConexionModbus.Controls.Add(btnModbusDesconectar);
            grpConexionModbus.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            grpConexionModbus.ForeColor = Color.White;
            grpConexionModbus.Location = new Point(14, 12);
            grpConexionModbus.Margin = new Padding(2);
            grpConexionModbus.Name = "grpConexionModbus";
            grpConexionModbus.Padding = new Padding(2);
            grpConexionModbus.Size = new Size(350, 168);
            grpConexionModbus.TabIndex = 0;
            grpConexionModbus.TabStop = false;
            grpConexionModbus.Text = "🔌 Conexión PLC Modbus TCP";
            // 
            // lblModbusIp
            // 
            lblModbusIp.AutoSize = true;
            lblModbusIp.Font = new Font("Segoe UI", 11F);
            lblModbusIp.Location = new Point(14, 30);
            lblModbusIp.Margin = new Padding(2, 0, 2, 0);
            lblModbusIp.Name = "lblModbusIp";
            lblModbusIp.Size = new Size(91, 20);
            lblModbusIp.TabIndex = 0;
            lblModbusIp.Text = "Dirección IP:";
            // 
            // txtModbusIp
            // 
            txtModbusIp.BackColor = Color.FromArgb(60, 63, 65);
            txtModbusIp.BorderStyle = BorderStyle.FixedSingle;
            txtModbusIp.Font = new Font("Segoe UI", 11F);
            txtModbusIp.ForeColor = Color.White;
            txtModbusIp.Location = new Point(119, 29);
            txtModbusIp.Margin = new Padding(2);
            txtModbusIp.Name = "txtModbusIp";
            txtModbusIp.Size = new Size(141, 27);
            txtModbusIp.TabIndex = 1;
            txtModbusIp.Text = "*************";
            // 
            // lblModbusPuerto
            // 
            lblModbusPuerto.AutoSize = true;
            lblModbusPuerto.Font = new Font("Segoe UI", 11F);
            lblModbusPuerto.Location = new Point(14, 60);
            lblModbusPuerto.Margin = new Padding(2, 0, 2, 0);
            lblModbusPuerto.Name = "lblModbusPuerto";
            lblModbusPuerto.Size = new Size(55, 20);
            lblModbusPuerto.TabIndex = 2;
            lblModbusPuerto.Text = "Puerto:";
            // 
            // nudModbusPuerto
            // 
            nudModbusPuerto.BackColor = Color.FromArgb(60, 63, 65);
            nudModbusPuerto.Font = new Font("Segoe UI", 11F);
            nudModbusPuerto.ForeColor = Color.White;
            nudModbusPuerto.Location = new Point(119, 59);
            nudModbusPuerto.Margin = new Padding(2);
            nudModbusPuerto.Maximum = new decimal(new int[] { 65535, 0, 0, 0 });
            nudModbusPuerto.Name = "nudModbusPuerto";
            nudModbusPuerto.Size = new Size(84, 27);
            nudModbusPuerto.TabIndex = 3;
            nudModbusPuerto.Value = new decimal(new int[] { 502, 0, 0, 0 });
            // 
            // lblModbusDeviceId
            // 
            lblModbusDeviceId.AutoSize = true;
            lblModbusDeviceId.Font = new Font("Segoe UI", 11F);
            lblModbusDeviceId.Location = new Point(14, 90);
            lblModbusDeviceId.Margin = new Padding(2, 0, 2, 0);
            lblModbusDeviceId.Name = "lblModbusDeviceId";
            lblModbusDeviceId.Size = new Size(105, 20);
            lblModbusDeviceId.TabIndex = 4;
            lblModbusDeviceId.Text = "ID Dispositivo:";
            // 
            // nudModbusDeviceId
            // 
            nudModbusDeviceId.BackColor = Color.FromArgb(60, 63, 65);
            nudModbusDeviceId.Font = new Font("Segoe UI", 11F);
            nudModbusDeviceId.ForeColor = Color.White;
            nudModbusDeviceId.Location = new Point(119, 89);
            nudModbusDeviceId.Margin = new Padding(2);
            nudModbusDeviceId.Maximum = new decimal(new int[] { 255, 0, 0, 0 });
            nudModbusDeviceId.Name = "nudModbusDeviceId";
            nudModbusDeviceId.Size = new Size(84, 27);
            nudModbusDeviceId.TabIndex = 5;
            nudModbusDeviceId.Value = new decimal(new int[] { 255, 0, 0, 0 });
            // 
            // btnModbusConectar
            // 
            btnModbusConectar.BackColor = Color.FromArgb(0, 122, 204);
            btnModbusConectar.FlatAppearance.BorderSize = 0;
            btnModbusConectar.FlatStyle = FlatStyle.Flat;
            btnModbusConectar.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnModbusConectar.Location = new Point(17, 125);
            btnModbusConectar.Margin = new Padding(2);
            btnModbusConectar.Name = "btnModbusConectar";
            btnModbusConectar.Size = new Size(105, 28);
            btnModbusConectar.TabIndex = 6;
            btnModbusConectar.Text = "Conectar";
            btnModbusConectar.UseVisualStyleBackColor = false;
            btnModbusConectar.Click += btnModbusConectar_Click;
            // 
            // btnModbusDesconectar
            // 
            btnModbusDesconectar.BackColor = Color.FromArgb(209, 17, 65);
            btnModbusDesconectar.Enabled = false;
            btnModbusDesconectar.FlatAppearance.BorderSize = 0;
            btnModbusDesconectar.FlatStyle = FlatStyle.Flat;
            btnModbusDesconectar.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            btnModbusDesconectar.Location = new Point(133, 125);
            btnModbusDesconectar.Margin = new Padding(2);
            btnModbusDesconectar.Name = "btnModbusDesconectar";
            btnModbusDesconectar.Size = new Size(105, 28);
            btnModbusDesconectar.TabIndex = 7;
            btnModbusDesconectar.Text = "Desconectar";
            btnModbusDesconectar.UseVisualStyleBackColor = false;
            btnModbusDesconectar.Click += btnModbusDesconectar_Click;
            // 
            // tabPageSinoptico
            // 
            tabPageSinoptico.BackColor = Color.FromArgb(45, 45, 48);
            tabPageSinoptico.Controls.Add(grpSinopticoInfo);
            tabPageSinoptico.Controls.Add(grpSRAP);
            tabPageSinoptico.Controls.Add(lblTopicosActivos);
            tabPageSinoptico.Controls.Add(lblUltimaActualizacion);
            tabPageSinoptico.Location = new Point(4, 24);
            tabPageSinoptico.Name = "tabPageSinoptico";
            tabPageSinoptico.Size = new Size(1336, 318);
            tabPageSinoptico.TabIndex = 3;
            tabPageSinoptico.Text = "Sinóptico";
            // 
            // grpSinopticoInfo
            // 
            grpSinopticoInfo.BackColor = Color.FromArgb(55, 55, 58);
            grpSinopticoInfo.Controls.Add(dgvSinopticoTopics);
            grpSinopticoInfo.Controls.Add(lblSinopticoUltimaActualizacion);
            grpSinopticoInfo.Controls.Add(lblSinopticoEstado);
            grpSinopticoInfo.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            grpSinopticoInfo.ForeColor = Color.White;
            grpSinopticoInfo.Location = new Point(7, 6);
            grpSinopticoInfo.Margin = new Padding(2);
            grpSinopticoInfo.Name = "grpSinopticoInfo";
            grpSinopticoInfo.Padding = new Padding(2);
            grpSinopticoInfo.Size = new Size(1323, 180);
            grpSinopticoInfo.TabIndex = 4;
            grpSinopticoInfo.TabStop = false;
            grpSinopticoInfo.Text = "🔄 Estado del Sistema Sinóptico MQTT";
            // 
            // dgvSinopticoTopics
            //
            dgvSinopticoTopics.AllowUserToAddRows = false;
            dgvSinopticoTopics.AllowUserToDeleteRows = false;
            dgvSinopticoTopics.BackgroundColor = Color.FromArgb(60, 60, 63);
            dgvSinopticoTopics.BorderStyle = BorderStyle.None;
            dgvSinopticoTopics.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(45, 45, 48);
            dgvSinopticoTopics.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvSinopticoTopics.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            dgvSinopticoTopics.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvSinopticoTopics.DefaultCellStyle.BackColor = Color.FromArgb(37, 37, 38);
            dgvSinopticoTopics.DefaultCellStyle.ForeColor = Color.White;
            dgvSinopticoTopics.DefaultCellStyle.SelectionBackColor = Color.FromArgb(0, 122, 204);
            dgvSinopticoTopics.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvSinopticoTopics.Location = new Point(14, 48);
            dgvSinopticoTopics.Margin = new Padding(2);
            dgvSinopticoTopics.Name = "dgvSinopticoTopics";
            dgvSinopticoTopics.ReadOnly = true;
            dgvSinopticoTopics.RowHeadersWidth = 62;
            dgvSinopticoTopics.Size = new Size(1295, 120);
            dgvSinopticoTopics.TabIndex = 2;
            // 
            // lblSinopticoUltimaActualizacion
            // 
            lblSinopticoUltimaActualizacion.AutoSize = true;
            lblSinopticoUltimaActualizacion.Font = new Font("Segoe UI", 10F);
            lblSinopticoUltimaActualizacion.ForeColor = Color.LightGray;
            lblSinopticoUltimaActualizacion.Location = new Point(980, 24);
            lblSinopticoUltimaActualizacion.Margin = new Padding(2, 0, 2, 0);
            lblSinopticoUltimaActualizacion.Name = "lblSinopticoUltimaActualizacion";
            lblSinopticoUltimaActualizacion.Size = new Size(151, 19);
            lblSinopticoUltimaActualizacion.TabIndex = 1;
            lblSinopticoUltimaActualizacion.Text = "Última Actualización: --";
            // 
            // lblSinopticoEstado
            // 
            lblSinopticoEstado.AutoSize = true;
            lblSinopticoEstado.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblSinopticoEstado.ForeColor = Color.Yellow;
            lblSinopticoEstado.Location = new Point(14, 21);
            lblSinopticoEstado.Margin = new Padding(2, 0, 2, 0);
            lblSinopticoEstado.Name = "lblSinopticoEstado";
            lblSinopticoEstado.Size = new Size(306, 21);
            lblSinopticoEstado.TabIndex = 0;
            lblSinopticoEstado.Text = "🔴 Sistema Sinóptico: DESHABILITADO";
            // 
            // lblTopicosActivos
            // 
            lblTopicosActivos.AutoSize = true;
            lblTopicosActivos.Font = new Font("Segoe UI", 10F);
            lblTopicosActivos.ForeColor = Color.White;
            lblTopicosActivos.Location = new Point(43, 348);
            lblTopicosActivos.Margin = new Padding(2, 0, 2, 0);
            lblTopicosActivos.Name = "lblTopicosActivos";
            lblTopicosActivos.Size = new Size(117, 19);
            lblTopicosActivos.TabIndex = 2;
            lblTopicosActivos.Text = "Tópicos Activos: 0";
            // 
            // lblUltimaActualizacion
            // 
            lblUltimaActualizacion.AutoSize = true;
            lblUltimaActualizacion.Font = new Font("Segoe UI", 10F);
            lblUltimaActualizacion.ForeColor = Color.White;
            lblUltimaActualizacion.Location = new Point(210, 348);
            lblUltimaActualizacion.Margin = new Padding(2, 0, 2, 0);
            lblUltimaActualizacion.Name = "lblUltimaActualizacion";
            lblUltimaActualizacion.Size = new Size(151, 19);
            lblUltimaActualizacion.TabIndex = 3;
            lblUltimaActualizacion.Text = "Última Actualización: --";
            //
            // grpSRAP
            //
            grpSRAP.BackColor = Color.FromArgb(55, 55, 58);
            grpSRAP.Controls.Add(btnSRAPParadar);
            grpSRAP.Controls.Add(btnSRAPParadam);
            grpSRAP.Controls.Add(btnSRAPParadal);
            grpSRAP.Controls.Add(btnSRAPPreseleccion);
            grpSRAP.Controls.Add(lblSRAPEstado);
            grpSRAP.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            grpSRAP.ForeColor = Color.White;
            grpSRAP.Location = new Point(7, 195);
            grpSRAP.Margin = new Padding(2);
            grpSRAP.Name = "grpSRAP";
            grpSRAP.Padding = new Padding(2);
            grpSRAP.Size = new Size(1323, 115);
            grpSRAP.TabIndex = 5;
            grpSRAP.TabStop = false;
            grpSRAP.Text = "⚙️ Sistema SRAP (Regulación Automática de Presión)";
            //
            // btnSRAPParadar
            //
            btnSRAPParadar.BackColor = Color.FromArgb(70, 70, 73);
            btnSRAPParadar.FlatAppearance.BorderSize = 0;
            btnSRAPParadar.FlatStyle = FlatStyle.Flat;
            btnSRAPParadar.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnSRAPParadar.ForeColor = Color.White;
            btnSRAPParadar.Location = new Point(14, 48);
            btnSRAPParadar.Name = "btnSRAPParadar";
            btnSRAPParadar.Size = new Size(150, 35);
            btnSRAPParadar.TabIndex = 1;
            btnSRAPParadar.Text = "🔴 PARADAR";
            btnSRAPParadar.UseVisualStyleBackColor = false;
            btnSRAPParadar.Click += btnSRAPParadar_Click;
            //
            // btnSRAPParadam
            //
            btnSRAPParadam.BackColor = Color.FromArgb(70, 70, 73);
            btnSRAPParadam.FlatAppearance.BorderSize = 0;
            btnSRAPParadam.FlatStyle = FlatStyle.Flat;
            btnSRAPParadam.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnSRAPParadam.ForeColor = Color.White;
            btnSRAPParadam.Location = new Point(180, 48);
            btnSRAPParadam.Name = "btnSRAPParadam";
            btnSRAPParadam.Size = new Size(150, 35);
            btnSRAPParadam.TabIndex = 2;
            btnSRAPParadam.Text = "🟡 PARADAM";
            btnSRAPParadam.UseVisualStyleBackColor = false;
            btnSRAPParadam.Click += btnSRAPParadam_Click;
            //
            // btnSRAPParadal
            //
            btnSRAPParadal.BackColor = Color.FromArgb(70, 70, 73);
            btnSRAPParadal.FlatAppearance.BorderSize = 0;
            btnSRAPParadal.FlatStyle = FlatStyle.Flat;
            btnSRAPParadal.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnSRAPParadal.ForeColor = Color.White;
            btnSRAPParadal.Location = new Point(346, 48);
            btnSRAPParadal.Name = "btnSRAPParadal";
            btnSRAPParadal.Size = new Size(150, 35);
            btnSRAPParadal.TabIndex = 3;
            btnSRAPParadal.Text = "🟢 PARADAL";
            btnSRAPParadal.UseVisualStyleBackColor = false;
            btnSRAPParadal.Click += btnSRAPParadal_Click;
            //
            // btnSRAPPreseleccion
            //
            btnSRAPPreseleccion.BackColor = Color.FromArgb(70, 70, 73);
            btnSRAPPreseleccion.FlatAppearance.BorderSize = 0;
            btnSRAPPreseleccion.FlatStyle = FlatStyle.Flat;
            btnSRAPPreseleccion.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnSRAPPreseleccion.ForeColor = Color.White;
            btnSRAPPreseleccion.Location = new Point(512, 48);
            btnSRAPPreseleccion.Name = "btnSRAPPreseleccion";
            btnSRAPPreseleccion.Size = new Size(150, 35);
            btnSRAPPreseleccion.TabIndex = 4;
            btnSRAPPreseleccion.Text = "🔵 PRESELECCIÓN";
            btnSRAPPreseleccion.UseVisualStyleBackColor = false;
            btnSRAPPreseleccion.Click += btnSRAPPreseleccion_Click;
            //
            // lblSRAPEstado
            //
            lblSRAPEstado.AutoSize = true;
            lblSRAPEstado.Font = new Font("Segoe UI", 10F);
            lblSRAPEstado.ForeColor = Color.LightGray;
            lblSRAPEstado.Location = new Point(14, 21);
            lblSRAPEstado.Name = "lblSRAPEstado";
            lblSRAPEstado.Size = new Size(200, 19);
            lblSRAPEstado.TabIndex = 0;
            lblSRAPEstado.Text = "Estado: Esperando comandos...";
            //
            // dgvModbusResultados
            // 
            dgvModbusResultados.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dgvModbusResultados.BackgroundColor = Color.FromArgb(60, 63, 65);
            dgvModbusResultados.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvModbusResultados.Location = new Point(1088, 47);
            dgvModbusResultados.Name = "dgvModbusResultados";
            dgvModbusResultados.RowHeadersWidth = 62;
            dgvModbusResultados.RowTemplate.Height = 28;
            dgvModbusResultados.Size = new Size(800, 500);
            dgvModbusResultados.TabIndex = 1;
            // 
            // grpConfigModbus
            // 
            grpConfigModbus.Controls.Add(cmbModbusConfig);
            grpConfigModbus.Controls.Add(txtModbusConfigNombre);
            grpConfigModbus.Controls.Add(btnModbusGuardarConfig);
            grpConfigModbus.Controls.Add(btnModbusNuevaConfig);
            grpConfigModbus.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            grpConfigModbus.ForeColor = Color.White;
            grpConfigModbus.Location = new Point(62, 47);
            grpConfigModbus.Name = "grpConfigModbus";
            grpConfigModbus.Size = new Size(550, 180);
            grpConfigModbus.TabIndex = 0;
            grpConfigModbus.TabStop = false;
            grpConfigModbus.Text = "Configuración";
            // 
            // cmbModbusConfig
            // 
            cmbModbusConfig.BackColor = Color.FromArgb(60, 63, 65);
            cmbModbusConfig.Font = new Font("Segoe UI", 11F);
            cmbModbusConfig.ForeColor = Color.White;
            cmbModbusConfig.FormattingEnabled = true;
            cmbModbusConfig.Location = new Point(24, 50);
            cmbModbusConfig.Name = "cmbModbusConfig";
            cmbModbusConfig.Size = new Size(250, 28);
            cmbModbusConfig.TabIndex = 0;
            // 
            // txtModbusConfigNombre
            // 
            txtModbusConfigNombre.BackColor = Color.FromArgb(60, 63, 65);
            txtModbusConfigNombre.BorderStyle = BorderStyle.FixedSingle;
            txtModbusConfigNombre.Font = new Font("Segoe UI", 11F);
            txtModbusConfigNombre.ForeColor = Color.White;
            txtModbusConfigNombre.Location = new Point(290, 50);
            txtModbusConfigNombre.Name = "txtModbusConfigNombre";
            txtModbusConfigNombre.Size = new Size(250, 27);
            txtModbusConfigNombre.TabIndex = 1;
            // 
            // btnModbusGuardarConfig
            // 
            btnModbusGuardarConfig.BackColor = Color.FromArgb(0, 122, 204);
            btnModbusGuardarConfig.FlatAppearance.BorderSize = 0;
            btnModbusGuardarConfig.FlatStyle = FlatStyle.Flat;
            btnModbusGuardarConfig.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnModbusGuardarConfig.Location = new Point(24, 110);
            btnModbusGuardarConfig.Name = "btnModbusGuardarConfig";
            btnModbusGuardarConfig.Size = new Size(120, 47);
            btnModbusGuardarConfig.TabIndex = 2;
            btnModbusGuardarConfig.Text = "Guardar";
            btnModbusGuardarConfig.UseVisualStyleBackColor = false;
            // 
            // btnModbusNuevaConfig
            // 
            btnModbusNuevaConfig.BackColor = Color.FromArgb(60, 63, 65);
            btnModbusNuevaConfig.FlatStyle = FlatStyle.Flat;
            btnModbusNuevaConfig.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnModbusNuevaConfig.Location = new Point(160, 110);
            btnModbusNuevaConfig.Name = "btnModbusNuevaConfig";
            btnModbusNuevaConfig.Size = new Size(120, 47);
            btnModbusNuevaConfig.TabIndex = 3;
            btnModbusNuevaConfig.Text = "Nuevo";
            btnModbusNuevaConfig.UseVisualStyleBackColor = false;
            // 
            // timer1
            // 
            timer1.Enabled = true;
            timer1.Interval = 1000;
            timer1.Tick += timer1_Tick;
            // 
            // MAIN
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(45, 45, 48);
            ClientSize = new Size(1344, 692);
            Controls.Add(tabControlPrincipal);
            Controls.Add(panelNavegacion);
            Controls.Add(panelEstados);
            Controls.Add(panelBarraSuperior);
            Controls.Add(panelLog);
            FormBorderStyle = FormBorderStyle.None;
            Name = "MAIN";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "Control de Producción ENAGAS";
            WindowState = FormWindowState.Maximized;
            panelBarraSuperior.ResumeLayout(false);
            panelBarraSuperior.PerformLayout();
            panelEstados.ResumeLayout(false);
            panelEstados.PerformLayout();
            panelNavegacion.ResumeLayout(false);
            panelLog.ResumeLayout(false);
            tabControlPrincipal.ResumeLayout(false);
            tabPageCorreo.ResumeLayout(false);
            grpConfigCorreo.ResumeLayout(false);
            grpConfigCorreo.PerformLayout();
            grpEstadosHorarios.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgvEstadosHorarios).EndInit();
            tabPageMqtt.ResumeLayout(false);
            groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgvMqttTopics).EndInit();
            grpConexionMqtt.ResumeLayout(false);
            grpConexionMqtt.PerformLayout();
            tabPageModbus.ResumeLayout(false);
            grpOperacionesModbus.ResumeLayout(false);
            grpOperacionesModbus.PerformLayout();
            grpConexionModbus.ResumeLayout(false);
            grpConexionModbus.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)nudModbusPuerto).EndInit();
            ((System.ComponentModel.ISupportInitialize)nudModbusDeviceId).EndInit();
            tabPageSinoptico.ResumeLayout(false);
            tabPageSinoptico.PerformLayout();
            grpSinopticoInfo.ResumeLayout(false);
            grpSinopticoInfo.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dgvSinopticoTopics).EndInit();
            grpSRAP.ResumeLayout(false);
            grpSRAP.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dgvModbusResultados).EndInit();
            grpConfigModbus.ResumeLayout(false);
            grpConfigModbus.PerformLayout();
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panelBarraSuperior;
        private System.Windows.Forms.Label lblTituloApp;
        private System.Windows.Forms.Button btnCerrar;
        private System.Windows.Forms.Panel panelEstados;
        private System.Windows.Forms.Panel panelEstadoMqtt;
        private System.Windows.Forms.Label lblEstadoMqtt;
        private System.Windows.Forms.Panel panelEstadoCorreo;
        private System.Windows.Forms.Label lblEstadoCorreo;
        private System.Windows.Forms.Panel panelEstadoModbus;
        private System.Windows.Forms.Label lblEstadoModbus;
        private System.Windows.Forms.Panel panelNavegacion;
        private System.Windows.Forms.Button btnNavCorreo;
        private System.Windows.Forms.Button btnNavMqtt;
        private System.Windows.Forms.Button btnNavModbus;
        private System.Windows.Forms.Button btnNavSinoptico;
        private System.Windows.Forms.Panel panelLog;
        private System.Windows.Forms.RichTextBox rtbLog;
        private System.Windows.Forms.Button btnLimpiarLog;
        private System.Windows.Forms.TabControl tabControlPrincipal;
        private System.Windows.Forms.TabPage tabPageCorreo;
        private System.Windows.Forms.TabPage tabPageMqtt;
        private System.Windows.Forms.TabPage tabPageModbus;
        private System.Windows.Forms.TabPage tabPageSinoptico;
        private System.Windows.Forms.GroupBox grpConfigCorreo;
        private System.Windows.Forms.Label lblEwsServerUrl;
        private System.Windows.Forms.TextBox txtEwsServerUrl;
        private System.Windows.Forms.Label lblEwsUsuario;
        private System.Windows.Forms.TextBox txtEwsUsuario;
        private System.Windows.Forms.Label lblEwsPassword;
        private System.Windows.Forms.TextBox txtEwsPassword;
        private System.Windows.Forms.Label lblEwsDominio;
        private System.Windows.Forms.TextBox txtEwsDominio;
        private System.Windows.Forms.Label lblEmailNotificacion;
        private System.Windows.Forms.TextBox txtEmailNotificacion;
        private System.Windows.Forms.Button btnConectarCorreo;
        private System.Windows.Forms.Button btnDesconectarCorreo;
        private System.Windows.Forms.Button btnProcesarCorreos;
        private System.Windows.Forms.Button btnForzarCambioHora;
        private System.Windows.Forms.GroupBox grpEstadosHorarios;
        private System.Windows.Forms.DataGridView dgvEstadosHorarios;
        private System.Windows.Forms.GroupBox grpConexionMqtt;
        private System.Windows.Forms.Label lblMqttProtocolo;
        private System.Windows.Forms.ComboBox cmbMqttProtocolo;
        private System.Windows.Forms.Label lblMqttHost;
        private System.Windows.Forms.TextBox txtMqttHost;
        private System.Windows.Forms.Label lblMqttPuerto;
        private System.Windows.Forms.TextBox txtMqttPuerto;
        private System.Windows.Forms.Label lblMqttClientId;
        private System.Windows.Forms.TextBox txtMqttClientId;
        private System.Windows.Forms.Label lblMqttUsuario;
        private System.Windows.Forms.TextBox txtMqttUsuario;
        private System.Windows.Forms.Label lblMqttPassword;
        private System.Windows.Forms.TextBox txtMqttPassword;
        private System.Windows.Forms.CheckBox chkMqttSslTls;
        private System.Windows.Forms.Button btnMqttConectar;
        private System.Windows.Forms.Button btnMqttDesconectar;
        private System.Windows.Forms.GroupBox grpConfigModbus;
        private System.Windows.Forms.ComboBox cmbModbusConfig;
        private System.Windows.Forms.TextBox txtModbusConfigNombre;
        private System.Windows.Forms.Button btnModbusGuardarConfig;
        private System.Windows.Forms.Button btnModbusNuevaConfig;
        private System.Windows.Forms.DataGridView dgvModbusResultados;
        private System.Windows.Forms.GroupBox grpConexionModbus;
        private System.Windows.Forms.Label lblModbusIp;
        private System.Windows.Forms.TextBox txtModbusIp;
        private System.Windows.Forms.Label lblModbusPuerto;
        private System.Windows.Forms.NumericUpDown nudModbusPuerto;
        private System.Windows.Forms.Label lblModbusDeviceId;
        private System.Windows.Forms.NumericUpDown nudModbusDeviceId;
        private System.Windows.Forms.Button btnModbusConectar;
        private System.Windows.Forms.Button btnModbusDesconectar;
        private System.Windows.Forms.GroupBox grpOperacionesModbus;
        private System.Windows.Forms.Label lblModbusDireccionRegistro;
        private System.Windows.Forms.TextBox txtModbusDireccionRegistro;
        private System.Windows.Forms.Label lblModbusValorEscritura;
        private System.Windows.Forms.TextBox txtModbusValorEscritura;
        private System.Windows.Forms.Button btnModbusLeerRegistros;
        private System.Windows.Forms.Button btnModbusEscribirRegistro;
        private System.Windows.Forms.Label lblModbusValorLeido;
        private System.Windows.Forms.Label lblModbusAyuda;
        private System.Windows.Forms.Label lblTopicosActivos;
        private System.Windows.Forms.Label lblUltimaActualizacion;
        private System.Windows.Forms.Timer timer1;
        private GroupBox groupBox1;
        private Button btnRefrescarMQTT;
        private DataGridView dgvMqttTopics;

        // Controles del Sinóptico MQTT
        private GroupBox grpSinopticoInfo;
        private Label lblSinopticoEstado;
        private Label lblSinopticoUltimaActualizacion;
        private DataGridView dgvSinopticoTopics;

        // Controles del Sistema SRAP
        private GroupBox grpSRAP;
        private Button btnSRAPParadar;
        private Button btnSRAPParadam;
        private Button btnSRAPParadal;
        private Button btnSRAPPreseleccion;
        private Label lblSRAPEstado;
    }
}
