using EasyModbus;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace SimuladorModbus
{
    /// <summary>
    /// Simulador de PLC Modbus TCP para pruebas
    /// </summary>
    public class SimuladorPLC
    {
        private ModbusServer? servidor;
        private bool ejecutando = false;
        private readonly int puerto;

        public SimuladorPLC(int puerto = 502)
        {
            this.puerto = puerto;
        }

        /// <summary>
        /// Inicia el simulador de PLC
        /// </summary>
        public void Iniciar()
        {
            try
            {
                servidor = new ModbusServer();
                servidor.Port = puerto;
                servidor.Listen();
                ejecutando = true;

                // Inicializar algunos valores de prueba
                InicializarValoresPrueba();

                Console.WriteLine($"✅ Simulador PLC iniciado en puerto {puerto}");
                Console.WriteLine("📊 Valores de prueba inicializados:");
                Console.WriteLine("   - Registro 430: 1234");
                Console.WriteLine("   - Registro 431: 5678");
                Console.WriteLine("   - Registro 432: 9999");
                Console.WriteLine("🔄 Simulando cambios automáticos cada 5 segundos...");

                // Iniciar simulación de cambios automáticos
                Task.Run(SimularCambiosAutomaticos);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error iniciando simulador: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Detiene el simulador de PLC
        /// </summary>
        public void Detener()
        {
            try
            {
                ejecutando = false;
                servidor?.StopListening();
                Console.WriteLine("🔴 Simulador PLC detenido");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error deteniendo simulador: {ex.Message}");
            }
        }

        /// <summary>
        /// Inicializa valores de prueba en los registros
        /// </summary>
        private void InicializarValoresPrueba()
        {
            if (servidor == null) return;

            // Registros Holding (40001-49999)
            servidor.holdingRegisters[430] = 1234;  // MW430 -> 40431
            servidor.holdingRegisters[431] = 5678;  // MW431 -> 40432
            servidor.holdingRegisters[432] = 9999;  // MW432 -> 40433
            servidor.holdingRegisters[100] = 100;   // Registro 100
            servidor.holdingRegisters[200] = 200;   // Registro 200
            servidor.holdingRegisters[300] = 300;   // Registro 300

            // Input Registers (30001-39999)
            servidor.inputRegisters[430] = 4321;
            servidor.inputRegisters[431] = 8765;
            servidor.inputRegisters[432] = 1111;

            // Coils (00001-09999)
            servidor.coils[1] = true;
            servidor.coils[2] = false;
            servidor.coils[3] = true;

            // Discrete Inputs (10001-19999)
            servidor.discreteInputs[1] = true;
            servidor.discreteInputs[2] = true;
            servidor.discreteInputs[3] = false;
        }

        /// <summary>
        /// Simula cambios automáticos en los registros para hacer la prueba más realista
        /// </summary>
        private async Task SimularCambiosAutomaticos()
        {
            var random = new Random();
            int contador = 0;

            while (ejecutando)
            {
                try
                {
                    await Task.Delay(5000); // Esperar 5 segundos

                    if (servidor != null && ejecutando)
                    {
                        contador++;

                        // Cambiar algunos valores automáticamente
                        servidor.holdingRegisters[430] = (short)(1234 + contador);
                        servidor.holdingRegisters[431] = (short)random.Next(1000, 9999);
                        servidor.holdingRegisters[432] = (short)(contador * 10);

                        // Cambiar input registers también
                        servidor.inputRegisters[430] = (short)(4321 + contador);
                        servidor.inputRegisters[431] = (short)random.Next(5000, 9999);

                        // Alternar coils
                        servidor.coils[1] = contador % 2 == 0;
                        servidor.coils[2] = contador % 3 == 0;

                        Console.WriteLine($"🔄 Actualización automática #{contador}:");
                        Console.WriteLine($"   - Registro 430: {servidor.holdingRegisters[430]}");
                        Console.WriteLine($"   - Registro 431: {servidor.holdingRegisters[431]}");
                        Console.WriteLine($"   - Registro 432: {servidor.holdingRegisters[432]}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Error en simulación automática: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Muestra el estado actual de los registros
        /// </summary>
        public void MostrarEstado()
        {
            if (servidor == null)
            {
                Console.WriteLine("❌ Simulador no iniciado");
                return;
            }

            Console.WriteLine("\n📊 ESTADO ACTUAL DEL SIMULADOR PLC:");
            Console.WriteLine("═══════════════════════════════════");
            Console.WriteLine("🔧 HOLDING REGISTERS:");
            Console.WriteLine($"   - Registro 430 (MW430): {servidor.holdingRegisters[430]}");
            Console.WriteLine($"   - Registro 431 (MW431): {servidor.holdingRegisters[431]}");
            Console.WriteLine($"   - Registro 432 (MW432): {servidor.holdingRegisters[432]}");
            Console.WriteLine($"   - Registro 100: {servidor.holdingRegisters[100]}");
            Console.WriteLine($"   - Registro 200: {servidor.holdingRegisters[200]}");
            Console.WriteLine($"   - Registro 300: {servidor.holdingRegisters[300]}");

            Console.WriteLine("\n📥 INPUT REGISTERS:");
            Console.WriteLine($"   - Registro 430: {servidor.inputRegisters[430]}");
            Console.WriteLine($"   - Registro 431: {servidor.inputRegisters[431]}");
            Console.WriteLine($"   - Registro 432: {servidor.inputRegisters[432]}");

            Console.WriteLine("\n🔘 COILS:");
            Console.WriteLine($"   - Coil 1: {servidor.coils[1]}");
            Console.WriteLine($"   - Coil 2: {servidor.coils[2]}");
            Console.WriteLine($"   - Coil 3: {servidor.coils[3]}");

            Console.WriteLine("\n🔍 DISCRETE INPUTS:");
            Console.WriteLine($"   - Input 1: {servidor.discreteInputs[1]}");
            Console.WriteLine($"   - Input 2: {servidor.discreteInputs[2]}");
            Console.WriteLine($"   - Input 3: {servidor.discreteInputs[3]}");
            Console.WriteLine("═══════════════════════════════════\n");
        }
    }
}
