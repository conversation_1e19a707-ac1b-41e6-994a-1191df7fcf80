using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace ControlDeProducciónENAGAS.Clases
{
    /// <summary>
    /// Gestor principal para el procesamiento de correos electrónicos
    /// Analiza tablas HTML para extraer restricciones horarias
    /// Gestiona limitaciones de MAÑANA (de correos) y HOY (variables independientes)
    /// </summary>
    public class GestorDeCorreo
    {
        private readonly ServicioEWS servicioEws;
        private readonly ConfiguracionCorreo configuracion;
        private readonly string rutaLimitacionesMañana;
        private readonly string rutaLimitacionesHoy;

        // Estados para MAÑANA (obtenidos de correos - solo lectura desde correos)
        private bool[] limitacionesMañana; // Array de 24 elementos para H1-H24 del día siguiente

        // Estados para HOY (variables independientes manipulables por el programa)
        private bool[] limitacionesHoy; // Array de 24 elementos para limitaciones actuales del día

        // Timers para automatización
        private System.Timers.Timer timerRevisionCorreos;
        private System.Timers.Timer timerCambioDeHora; // Timer para las 00:00

        // Eventos para notificar cambios
        public event EventHandler<EventArgs> LimitacionesActualizadas;
        public event EventHandler<EventArgs> CambioDeHoraEjecutado;

        public GestorDeCorreo(ConfiguracionCorreo config)
        {
            configuracion = config ?? throw new ArgumentNullException(nameof(config));

            // Inicializar servicio EWS
            servicioEws = new ServicioEWS(
                config.EwsServerUrl,
                config.EwsUsername,
                config.EwsPassword,
                config.EwsDomain
            );

            // Configurar rutas para persistencia de estados
            var directorioBase = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                "ControlDeProducciónENAGAS"
            );

            rutaLimitacionesMañana = Path.Combine(directorioBase, "limitaciones_mañana.csv");
            rutaLimitacionesHoy = Path.Combine(directorioBase, "limitaciones_hoy.csv");

            // Inicializar arrays de estados (24 horas cada uno)
            limitacionesMañana = new bool[24];
            limitacionesHoy = new bool[24];

            // Crear directorio si no existe
            if (!Directory.Exists(directorioBase))
            {
                Directory.CreateDirectory(directorioBase);
            }

            // Cargar estados previos si existen
            CargarLimitacionesDesdeArchivos();

            // Inicializar timers
            InicializarTimers();
        }

        /// <summary>
        /// Inicializa los timers para revisión automática y cambio de hora
        /// </summary>
        private void InicializarTimers()
        {
            // Timer para revisión automática de correos cada 5 minutos
            timerRevisionCorreos = new System.Timers.Timer(5 * 60 * 1000); // 5 minutos
            timerRevisionCorreos.Elapsed += async (sender, e) => await RevisarCorreosAutomaticamente();
            timerRevisionCorreos.AutoReset = true;

            // Timer para verificar cambio de hora (cada minuto)
            timerCambioDeHora = new System.Timers.Timer(60 * 1000); // 1 minuto
            timerCambioDeHora.Elapsed += VerificarCambioDeHora;
            timerCambioDeHora.AutoReset = true;
            timerCambioDeHora.Start(); // Iniciar inmediatamente
        }

        /// <summary>
        /// Inicia la revisión automática de correos
        /// </summary>
        public void IniciarRevisionAutomatica()
        {
            timerRevisionCorreos?.Start();
        }

        /// <summary>
        /// Detiene la revisión automática de correos
        /// </summary>
        public void DetenerRevisionAutomatica()
        {
            timerRevisionCorreos?.Stop();
        }

        /// <summary>
        /// Verifica si es hora de hacer el cambio de limitaciones (00:00)
        /// </summary>
        private void VerificarCambioDeHora(object sender, System.Timers.ElapsedEventArgs e)
        {
            var ahora = DateTime.Now;
            if (ahora.Hour == 0 && ahora.Minute == 0)
            {
                EjecutarCambioDeHora();
            }
        }

        /// <summary>
        /// Ejecuta el cambio de limitaciones de mañana a hoy (para depuración y automático)
        /// </summary>
        public void EjecutarCambioDeHora()
        {
            try
            {
                // Copiar limitaciones de mañana a hoy
                for (int i = 0; i < 24; i++)
                {
                    limitacionesHoy[i] = limitacionesMañana[i];
                }

                // Limpiar limitaciones de mañana
                for (int i = 0; i < 24; i++)
                {
                    limitacionesMañana[i] = false;
                }

                // Guardar ambos archivos
                GuardarLimitacionesHoy();
                GuardarLimitacionesMañana();

                // Notificar cambio
                CambioDeHoraEjecutado?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception)
            {
                // Error silencioso
            }
        }

        /// <summary>
        /// Revisión automática de correos (llamada por timer)
        /// </summary>
        private async Task RevisarCorreosAutomaticamente()
        {
            try
            {
                await ConectarYProcesarCorreosAsync();
            }
            catch (Exception)
            {
                // Error silencioso en revisión automática
            }
        }

        /// <summary>
        /// Prueba la conexión EWS
        /// </summary>
        public async Task<bool> ProbarConexionAsync()
        {
            try
            {
                return await servicioEws.ProbarConexionAsync();
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Conecta y procesa correos de restricciones horarias
        /// </summary>
        public async Task<ResultadoProcesamiento> ConectarYProcesarCorreosAsync()
        {
            var resultado = new ResultadoProcesamiento();

            try
            {
                // Obtener correos sin procesar
                var correosSinProcesar = servicioEws.ObtenerCorreosSinProcesar();
                resultado.TotalCorreosEncontrados = correosSinProcesar.Count;

                // Filtrar correos por asunto (LIMITACIÓN/LIMITACIONES)
                var correosLimitacion = correosSinProcesar.Where(correo => 
                    FiltrarPorAsunto(correo.Subject)).ToList();

                resultado.CorreosFiltrados = correosLimitacion.Count;

                foreach (var correo in correosLimitacion)
                {
                    try
                    {
                        // Evitar procesar correos de feedback (prevenir bucles)
                        if (correo.Subject.ToUpper().Contains("FEEDBACK"))
                        {
                            continue;
                        }

                        // Procesar contenido HTML del correo
                        var datosAnalizados = ProcesarContenidoCorreo(correo.HtmlContent, correo.Subject);
                        
                        if (datosAnalizados.Count > 0)
                        {
                            // Actualizar limitaciones de MAÑANA (los correos siempre son para el día siguiente)
                            ActualizarLimitacionesMañana(datosAnalizados);

                            // Guardar limitaciones de mañana actualizadas
                            GuardarLimitacionesMañana();

                            // Enviar correo de respuesta con análisis
                            await EnviarCorreoRespuesta(datosAnalizados, correo.Subject, correo.Sender);

                            // Marcar correo como leído
                            servicioEws.MarcarCorreoComoLeido(correo.MessageId);

                            // Notificar actualización
                            LimitacionesActualizadas?.Invoke(this, EventArgs.Empty);

                            resultado.CorreosProcesados++;
                        }
                    }
                    catch (Exception ex)
                    {
                        resultado.ErroresProcesamiento.Add($"Error procesando correo '{correo.Subject}': {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                resultado.ErroresProcesamiento.Add($"Error general: {ex.Message}");
            }

            return resultado;
        }

        /// <summary>
        /// Filtra correos por asunto (debe contener LIMITACIÓN o LIMITACIONES)
        /// </summary>
        private bool FiltrarPorAsunto(string asunto)
        {
            if (string.IsNullOrEmpty(asunto))
                return false;

            string asuntoUpper = asunto.ToUpper();
            return asuntoUpper.Contains("LIMITACIÓN") || asuntoUpper.Contains("LIMITACIONES");
        }

        /// <summary>
        /// Procesa el contenido HTML del correo para extraer restricciones horarias
        /// </summary>
        private List<string> ProcesarContenidoCorreo(string contenidoHtml, string asunto)
        {
            var datosAnalizados = new List<string>();

            try
            {
                if (string.IsNullOrEmpty(contenidoHtml))
                    return datosAnalizados;

                // Buscar tablas en el HTML
                var regexTablas = new Regex(@"<table[^>]*>.*?</table>", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                var coincidenciasTablas = regexTablas.Matches(contenidoHtml);

                // Limitar a máximo 10 tablas para evitar bucles infinitos
                int tablasProcessadas = 0;
                foreach (Match tablaMatch in coincidenciasTablas)
                {
                    if (tablasProcessadas >= 10) break;

                    string contenidoTabla = tablaMatch.Value;
                    var datosTabla = ProcesarTablaIndividual(contenidoTabla);
                    datosAnalizados.AddRange(datosTabla);

                    tablasProcessadas++;
                }
            }
            catch (Exception)
            {
                // Error silencioso en procesamiento de contenido
            }

            return datosAnalizados;
        }

        /// <summary>
        /// Procesa una tabla individual para extraer encabezados H1-H24
        /// </summary>
        private List<string> ProcesarTablaIndividual(string contenidoTabla)
        {
            var columnasConValor = new List<string>();

            try
            {
                // Buscar encabezados H1, H2, H3... H24 en celdas de tabla
                var regexEncabezados = new Regex(@"<t[hd][^>]*>.*?(H\d+).*?</t[hd]>", RegexOptions.IgnoreCase);
                var coincidenciasEncabezados = regexEncabezados.Matches(contenidoTabla);

                foreach (Match match in coincidenciasEncabezados)
                {
                    string encabezado = match.Groups[1].Value.ToUpper();
                    
                    // Validar que sea H1-H24
                    if (encabezado.StartsWith("H") && encabezado.Length >= 2)
                    {
                        string numeroStr = encabezado.Substring(1);
                        if (int.TryParse(numeroStr, out int numero) && numero >= 1 && numero <= 24)
                        {
                            if (!columnasConValor.Contains(encabezado))
                            {
                                columnasConValor.Add(encabezado);
                            }
                        }
                    }
                }

                // Buscar filas con datos para identificar restricciones
                var regexFilas = new Regex(@"<tr[^>]*>.*?</tr>", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                var coincidenciasFilas = regexFilas.Matches(contenidoTabla);

                // Limitar a máximo 1000 filas para evitar bucles infinitos
                int filasProcessadas = 0;
                foreach (Match filaMatch in coincidenciasFilas)
                {
                    if (filasProcessadas >= 1000) break;

                    string contenidoFila = filaMatch.Value;
                    
                    // Buscar celdas con valores que indiquen restricciones
                    var regexCeldas = new Regex(@"<t[hd][^>]*>(.*?)</t[hd]>", RegexOptions.IgnoreCase);
                    var coincidenciasCeldas = regexCeldas.Matches(contenidoFila);

                    foreach (Match celdaMatch in coincidenciasCeldas)
                    {
                        string contenidoCelda = celdaMatch.Groups[1].Value.Trim();
                        
                        // Si la celda contiene un valor significativo (no vacío, no solo espacios)
                        if (!string.IsNullOrWhiteSpace(contenidoCelda) && 
                            contenidoCelda.Length > 0 && 
                            !contenidoCelda.Equals("&nbsp;", StringComparison.OrdinalIgnoreCase))
                        {
                            // Esta fila tiene datos, por lo que las horas encontradas tienen restricciones
                            break;
                        }
                    }

                    filasProcessadas++;
                }
            }
            catch (Exception)
            {
                // Error silencioso en procesamiento de tabla
            }

            return columnasConValor;
        }

        /// <summary>
        /// Actualiza las limitaciones de MAÑANA basado en los datos analizados de correos
        /// </summary>
        private void ActualizarLimitacionesMañana(List<string> datosAnalizados)
        {
            // Resetear todas las limitaciones de mañana a disponible
            for (int i = 0; i < 24; i++)
            {
                limitacionesMañana[i] = false;
            }

            // Marcar horas restringidas para mañana
            foreach (var dato in datosAnalizados)
            {
                if (dato.StartsWith("H", StringComparison.OrdinalIgnoreCase) && dato.Length >= 2)
                {
                    string numeroStr = dato.Substring(1);
                    if (int.TryParse(numeroStr, out int hora) && hora >= 1 && hora <= 24)
                    {
                        limitacionesMañana[hora - 1] = true; // Marcar como restringida para mañana
                    }
                }
            }
        }

        /// <summary>
        /// Obtiene las horas con limitaciones de HOY (variables manipulables)
        /// </summary>
        public List<int> ObtenerHorasConLimitaciones()
        {
            var horasConLimitaciones = new List<int>();
            for (int i = 0; i < 24; i++)
            {
                if (limitacionesHoy[i])
                {
                    horasConLimitaciones.Add(i + 1); // Convertir a 1-24
                }
            }
            return horasConLimitaciones;
        }

        /// <summary>
        /// Obtiene las horas sin limitaciones de HOY (variables manipulables)
        /// </summary>
        public List<int> ObtenerHorasSinLimitaciones()
        {
            var horasSinLimitaciones = new List<int>();
            for (int i = 0; i < 24; i++)
            {
                if (!limitacionesHoy[i])
                {
                    horasSinLimitaciones.Add(i + 1); // Convertir a 1-24
                }
            }
            return horasSinLimitaciones;
        }

        /// <summary>
        /// Obtiene las limitaciones de HOY (para manipulación directa por el programa)
        /// </summary>
        public bool[] ObtenerLimitacionesHoy()
        {
            return (bool[])limitacionesHoy.Clone();
        }

        /// <summary>
        /// Establece las limitaciones de HOY (para manipulación directa por el programa)
        /// </summary>
        public void EstablecerLimitacionesHoy(bool[] nuevasLimitaciones)
        {
            if (nuevasLimitaciones?.Length == 24)
            {
                Array.Copy(nuevasLimitaciones, limitacionesHoy, 24);
                GuardarLimitacionesHoy();
                LimitacionesActualizadas?.Invoke(this, EventArgs.Empty);
            }
        }

        /// <summary>
        /// Establece una limitación específica de HOY
        /// </summary>
        public void EstablecerLimitacionHoy(int hora, bool limitada)
        {
            if (hora >= 1 && hora <= 24)
            {
                limitacionesHoy[hora - 1] = limitada;
                GuardarLimitacionesHoy();
                LimitacionesActualizadas?.Invoke(this, EventArgs.Empty);
            }
        }

        /// <summary>
        /// Obtiene las limitaciones de MAÑANA (solo lectura - vienen de correos)
        /// </summary>
        public bool[] ObtenerLimitacionesMañana()
        {
            return (bool[])limitacionesMañana.Clone();
        }

        /// <summary>
        /// Obtiene el estado actual de todas las horas de HOY (para UI)
        /// </summary>
        public bool[] ObtenerEstadosActuales()
        {
            return (bool[])limitacionesHoy.Clone();
        }

        /// <summary>
        /// Envía correo de respuesta con análisis de datos
        /// </summary>
        private async Task EnviarCorreoRespuesta(List<string> datosAnalizados, string asuntoOriginal, string remitenteOriginal)
        {
            try
            {
                if (!string.IsNullOrEmpty(configuracion.EmailNotification))
                {
                    await servicioEws.EnviarCorreoDatosAnalizadosAsync(
                        datosAnalizados,
                        asuntoOriginal,
                        remitenteOriginal,
                        configuracion.EmailNotification
                    );
                }
            }
            catch (Exception)
            {
                // Error silencioso - no es crítico si no se puede enviar respuesta
            }
        }

        /// <summary>
        /// Guarda las limitaciones de HOY en archivo CSV
        /// </summary>
        private void GuardarLimitacionesHoy()
        {
            try
            {
                var lineas = new List<string>
                {
                    "Hora,Estado,Timestamp"
                };

                for (int i = 0; i < 24; i++)
                {
                    string estado = limitacionesHoy[i] ? "LIMITADA" : "DISPONIBLE";
                    lineas.Add($"{i + 1:00},{estado},{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                }

                File.WriteAllLines(rutaLimitacionesHoy, lineas);
            }
            catch (Exception)
            {
                // Error silencioso en guardado
            }
        }

        /// <summary>
        /// Guarda las limitaciones de MAÑANA en archivo CSV
        /// </summary>
        private void GuardarLimitacionesMañana()
        {
            try
            {
                var lineas = new List<string>
                {
                    "Hora,Estado,Timestamp"
                };

                for (int i = 0; i < 24; i++)
                {
                    string estado = limitacionesMañana[i] ? "LIMITADA" : "DISPONIBLE";
                    lineas.Add($"{i + 1:00},{estado},{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                }

                File.WriteAllLines(rutaLimitacionesMañana, lineas);
            }
            catch (Exception)
            {
                // Error silencioso en guardado
            }
        }

        /// <summary>
        /// Carga las limitaciones desde ambos archivos CSV
        /// </summary>
        private void CargarLimitacionesDesdeArchivos()
        {
            CargarLimitacionesHoy();
            CargarLimitacionesMañana();
        }

        /// <summary>
        /// Carga las limitaciones de HOY desde archivo CSV
        /// </summary>
        private void CargarLimitacionesHoy()
        {
            try
            {
                if (File.Exists(rutaLimitacionesHoy))
                {
                    var lineas = File.ReadAllLines(rutaLimitacionesHoy);

                    // Saltar encabezado
                    for (int i = 1; i < lineas.Length && i <= 24; i++)
                    {
                        var partes = lineas[i].Split(',');
                        if (partes.Length >= 2)
                        {
                            if (int.TryParse(partes[0], out int hora) && hora >= 1 && hora <= 24)
                            {
                                limitacionesHoy[hora - 1] = partes[1].Equals("LIMITADA", StringComparison.OrdinalIgnoreCase);
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                // Error silencioso en carga - usar valores por defecto
                limitacionesHoy = new bool[24];
            }
        }

        /// <summary>
        /// Carga las limitaciones de MAÑANA desde archivo CSV
        /// </summary>
        private void CargarLimitacionesMañana()
        {
            try
            {
                if (File.Exists(rutaLimitacionesMañana))
                {
                    var lineas = File.ReadAllLines(rutaLimitacionesMañana);

                    // Saltar encabezado
                    for (int i = 1; i < lineas.Length && i <= 24; i++)
                    {
                        var partes = lineas[i].Split(',');
                        if (partes.Length >= 2)
                        {
                            if (int.TryParse(partes[0], out int hora) && hora >= 1 && hora <= 24)
                            {
                                limitacionesMañana[hora - 1] = partes[1].Equals("LIMITADA", StringComparison.OrdinalIgnoreCase);
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                // Error silencioso en carga - usar valores por defecto
                limitacionesMañana = new bool[24];
            }
        }

        /// <summary>
        /// Libera recursos
        /// </summary>
        public void Dispose()
        {
            timerRevisionCorreos?.Stop();
            timerRevisionCorreos?.Dispose();
            timerCambioDeHora?.Stop();
            timerCambioDeHora?.Dispose();
            servicioEws?.Dispose();
        }
    }

    /// <summary>
    /// Resultado del procesamiento de correos
    /// </summary>
    public class ResultadoProcesamiento
    {
        public int TotalCorreosEncontrados { get; set; }
        public int CorreosFiltrados { get; set; }
        public int CorreosProcesados { get; set; }
        public List<string> ErroresProcesamiento { get; set; } = new List<string>();

        public bool TieneErrores => ErroresProcesamiento.Count > 0;

        public string ResumenProcesamiento =>
            $"Encontrados: {TotalCorreosEncontrados}, Filtrados: {CorreosFiltrados}, Procesados: {CorreosProcesados}";
    }
}
