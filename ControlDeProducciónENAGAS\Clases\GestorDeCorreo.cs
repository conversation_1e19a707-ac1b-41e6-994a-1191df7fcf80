using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace ControlDeProducciónENAGAS.Clases
{
    /// <summary>
    /// Gestor principal para el procesamiento de correos electrónicos
    /// Analiza tablas HTML para extraer restricciones horarias
    /// </summary>
    public class GestorDeCorreo
    {
        private readonly ServicioEWS servicioEws;
        private readonly ConfiguracionCorreo configuracion;
        private readonly string rutaEstados;
        private bool[] estadosHorarios; // Array de 24 horas (true = restringida, false = disponible)

        public GestorDeCorreo(ConfiguracionCorreo config)
        {
            configuracion = config ?? throw new ArgumentNullException(nameof(config));
            
            // Inicializar servicio EWS
            servicioEws = new ServicioEWS(
                config.EwsServerUrl,
                config.EwsUsername,
                config.EwsPassword,
                config.EwsDomain
            );

            // Configurar ruta para persistencia de estados
            rutaEstados = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                "ControlDeProducciónENAGAS",
                "estados_horarios.csv"
            );

            // Inicializar array de estados (24 horas)
            estadosHorarios = new bool[24];

            // Crear directorio si no existe
            var directorio = Path.GetDirectoryName(rutaEstados);
            if (!Directory.Exists(directorio))
            {
                Directory.CreateDirectory(directorio);
            }

            // Cargar estados previos si existen
            CargarEstadosHorarios();
        }

        /// <summary>
        /// Prueba la conexión EWS
        /// </summary>
        public async Task<bool> ProbarConexionAsync()
        {
            try
            {
                return await servicioEws.ProbarConexionAsync();
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Conecta y procesa correos de restricciones horarias
        /// </summary>
        public async Task<ResultadoProcesamiento> ConectarYProcesarCorreosAsync()
        {
            var resultado = new ResultadoProcesamiento();

            try
            {
                // Obtener correos sin procesar
                var correosSinProcesar = servicioEws.ObtenerCorreosSinProcesar();
                resultado.TotalCorreosEncontrados = correosSinProcesar.Count;

                // Filtrar correos por asunto (LIMITACIÓN/LIMITACIONES)
                var correosLimitacion = correosSinProcesar.Where(correo => 
                    FiltrarPorAsunto(correo.Subject)).ToList();

                resultado.CorreosFiltrados = correosLimitacion.Count;

                foreach (var correo in correosLimitacion)
                {
                    try
                    {
                        // Evitar procesar correos de feedback (prevenir bucles)
                        if (correo.Subject.ToUpper().Contains("FEEDBACK"))
                        {
                            continue;
                        }

                        // Procesar contenido HTML del correo
                        var datosAnalizados = ProcesarContenidoCorreo(correo.HtmlContent, correo.Subject);
                        
                        if (datosAnalizados.Count > 0)
                        {
                            // Actualizar estados horarios
                            ActualizarEstadosHorarios(datosAnalizados);
                            
                            // Guardar estados actualizados
                            GuardarEstadosHorarios();

                            // Enviar correo de respuesta con análisis
                            await EnviarCorreoRespuesta(datosAnalizados, correo.Subject, correo.Sender);

                            // Marcar correo como leído
                            servicioEws.MarcarCorreoComoLeido(correo.MessageId);

                            resultado.CorreosProcesados++;
                        }
                    }
                    catch (Exception ex)
                    {
                        resultado.ErroresProcesamiento.Add($"Error procesando correo '{correo.Subject}': {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                resultado.ErroresProcesamiento.Add($"Error general: {ex.Message}");
            }

            return resultado;
        }

        /// <summary>
        /// Filtra correos por asunto (debe contener LIMITACIÓN o LIMITACIONES)
        /// </summary>
        private bool FiltrarPorAsunto(string asunto)
        {
            if (string.IsNullOrEmpty(asunto))
                return false;

            string asuntoUpper = asunto.ToUpper();
            return asuntoUpper.Contains("LIMITACIÓN") || asuntoUpper.Contains("LIMITACIONES");
        }

        /// <summary>
        /// Procesa el contenido HTML del correo para extraer restricciones horarias
        /// </summary>
        private List<string> ProcesarContenidoCorreo(string contenidoHtml, string asunto)
        {
            var datosAnalizados = new List<string>();

            try
            {
                if (string.IsNullOrEmpty(contenidoHtml))
                    return datosAnalizados;

                // Buscar tablas en el HTML
                var regexTablas = new Regex(@"<table[^>]*>.*?</table>", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                var coincidenciasTablas = regexTablas.Matches(contenidoHtml);

                // Limitar a máximo 10 tablas para evitar bucles infinitos
                int tablasProcessadas = 0;
                foreach (Match tablaMatch in coincidenciasTablas)
                {
                    if (tablasProcessadas >= 10) break;

                    string contenidoTabla = tablaMatch.Value;
                    var datosTabla = ProcesarTablaIndividual(contenidoTabla);
                    datosAnalizados.AddRange(datosTabla);

                    tablasProcessadas++;
                }
            }
            catch (Exception)
            {
                // Error silencioso en procesamiento de contenido
            }

            return datosAnalizados;
        }

        /// <summary>
        /// Procesa una tabla individual para extraer encabezados H1-H24
        /// </summary>
        private List<string> ProcesarTablaIndividual(string contenidoTabla)
        {
            var columnasConValor = new List<string>();

            try
            {
                // Buscar encabezados H1, H2, H3... H24 en celdas de tabla
                var regexEncabezados = new Regex(@"<t[hd][^>]*>.*?(H\d+).*?</t[hd]>", RegexOptions.IgnoreCase);
                var coincidenciasEncabezados = regexEncabezados.Matches(contenidoTabla);

                foreach (Match match in coincidenciasEncabezados)
                {
                    string encabezado = match.Groups[1].Value.ToUpper();
                    
                    // Validar que sea H1-H24
                    if (encabezado.StartsWith("H") && encabezado.Length >= 2)
                    {
                        string numeroStr = encabezado.Substring(1);
                        if (int.TryParse(numeroStr, out int numero) && numero >= 1 && numero <= 24)
                        {
                            if (!columnasConValor.Contains(encabezado))
                            {
                                columnasConValor.Add(encabezado);
                            }
                        }
                    }
                }

                // Buscar filas con datos para identificar restricciones
                var regexFilas = new Regex(@"<tr[^>]*>.*?</tr>", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                var coincidenciasFilas = regexFilas.Matches(contenidoTabla);

                // Limitar a máximo 1000 filas para evitar bucles infinitos
                int filasProcessadas = 0;
                foreach (Match filaMatch in coincidenciasFilas)
                {
                    if (filasProcessadas >= 1000) break;

                    string contenidoFila = filaMatch.Value;
                    
                    // Buscar celdas con valores que indiquen restricciones
                    var regexCeldas = new Regex(@"<t[hd][^>]*>(.*?)</t[hd]>", RegexOptions.IgnoreCase);
                    var coincidenciasCeldas = regexCeldas.Matches(contenidoFila);

                    foreach (Match celdaMatch in coincidenciasCeldas)
                    {
                        string contenidoCelda = celdaMatch.Groups[1].Value.Trim();
                        
                        // Si la celda contiene un valor significativo (no vacío, no solo espacios)
                        if (!string.IsNullOrWhiteSpace(contenidoCelda) && 
                            contenidoCelda.Length > 0 && 
                            !contenidoCelda.Equals("&nbsp;", StringComparison.OrdinalIgnoreCase))
                        {
                            // Esta fila tiene datos, por lo que las horas encontradas tienen restricciones
                            break;
                        }
                    }

                    filasProcessadas++;
                }
            }
            catch (Exception)
            {
                // Error silencioso en procesamiento de tabla
            }

            return columnasConValor;
        }

        /// <summary>
        /// Actualiza los estados horarios basado en los datos analizados
        /// </summary>
        private void ActualizarEstadosHorarios(List<string> datosAnalizados)
        {
            // Resetear todos los estados a disponible
            for (int i = 0; i < 24; i++)
            {
                estadosHorarios[i] = false;
            }

            // Marcar horas restringidas
            foreach (var dato in datosAnalizados)
            {
                if (dato.StartsWith("H", StringComparison.OrdinalIgnoreCase) && dato.Length >= 2)
                {
                    string numeroStr = dato.Substring(1);
                    if (int.TryParse(numeroStr, out int hora) && hora >= 1 && hora <= 24)
                    {
                        estadosHorarios[hora - 1] = true; // Marcar como restringida
                    }
                }
            }
        }

        /// <summary>
        /// Obtiene las horas con limitaciones
        /// </summary>
        public List<int> ObtenerHorasConLimitaciones()
        {
            var horasConLimitaciones = new List<int>();
            for (int i = 0; i < 24; i++)
            {
                if (estadosHorarios[i])
                {
                    horasConLimitaciones.Add(i + 1); // Convertir a 1-24
                }
            }
            return horasConLimitaciones;
        }

        /// <summary>
        /// Obtiene las horas sin limitaciones
        /// </summary>
        public List<int> ObtenerHorasSinLimitaciones()
        {
            var horasSinLimitaciones = new List<int>();
            for (int i = 0; i < 24; i++)
            {
                if (!estadosHorarios[i])
                {
                    horasSinLimitaciones.Add(i + 1); // Convertir a 1-24
                }
            }
            return horasSinLimitaciones;
        }

        /// <summary>
        /// Obtiene el estado actual de todas las horas (para UI)
        /// </summary>
        public bool[] ObtenerEstadosActuales()
        {
            return (bool[])estadosHorarios.Clone();
        }

        /// <summary>
        /// Envía correo de respuesta con análisis de datos
        /// </summary>
        private async Task EnviarCorreoRespuesta(List<string> datosAnalizados, string asuntoOriginal, string remitenteOriginal)
        {
            try
            {
                if (!string.IsNullOrEmpty(configuracion.EmailNotification))
                {
                    await servicioEws.EnviarCorreoDatosAnalizadosAsync(
                        datosAnalizados,
                        asuntoOriginal,
                        remitenteOriginal,
                        configuracion.EmailNotification
                    );
                }
            }
            catch (Exception)
            {
                // Error silencioso - no es crítico si no se puede enviar respuesta
            }
        }

        /// <summary>
        /// Guarda los estados horarios en archivo CSV
        /// </summary>
        private void GuardarEstadosHorarios()
        {
            try
            {
                var lineas = new List<string>
                {
                    "Hora,Estado,Timestamp"
                };

                for (int i = 0; i < 24; i++)
                {
                    string estado = estadosHorarios[i] ? "RESTRINGIDA" : "DISPONIBLE";
                    lineas.Add($"{i + 1:00},{estado},{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                }

                File.WriteAllLines(rutaEstados, lineas);
            }
            catch (Exception)
            {
                // Error silencioso en guardado
            }
        }

        /// <summary>
        /// Carga los estados horarios desde archivo CSV
        /// </summary>
        private void CargarEstadosHorarios()
        {
            try
            {
                if (File.Exists(rutaEstados))
                {
                    var lineas = File.ReadAllLines(rutaEstados);

                    // Saltar encabezado
                    for (int i = 1; i < lineas.Length && i <= 24; i++)
                    {
                        var partes = lineas[i].Split(',');
                        if (partes.Length >= 2)
                        {
                            if (int.TryParse(partes[0], out int hora) && hora >= 1 && hora <= 24)
                            {
                                estadosHorarios[hora - 1] = partes[1].Equals("RESTRINGIDA", StringComparison.OrdinalIgnoreCase);
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                // Error silencioso en carga - usar valores por defecto
                estadosHorarios = new bool[24];
            }
        }

        /// <summary>
        /// Libera recursos
        /// </summary>
        public void Dispose()
        {
            servicioEws?.Dispose();
        }
    }

    /// <summary>
    /// Resultado del procesamiento de correos
    /// </summary>
    public class ResultadoProcesamiento
    {
        public int TotalCorreosEncontrados { get; set; }
        public int CorreosFiltrados { get; set; }
        public int CorreosProcesados { get; set; }
        public List<string> ErroresProcesamiento { get; set; } = new List<string>();

        public bool TieneErrores => ErroresProcesamiento.Count > 0;

        public string ResumenProcesamiento =>
            $"Encontrados: {TotalCorreosEncontrados}, Filtrados: {CorreosFiltrados}, Procesados: {CorreosProcesados}";
    }
}
