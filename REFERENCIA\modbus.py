#!/usr/bin/env python3
"""
Cliente Modbus TCP/IP Simplificado
Lee el holding register 400 y lo muestra en tiempo real
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime

try:
    from pymodbus.client import ModbusTcpClient
except ImportError:
    messagebox.showerror("Error", "Instala pymodbus: pip install pymodbus")
    exit(1)


class SimpleModbusClient:
    def __init__(self, root):
        self.root = root
        self.root.title("Cliente Modbus Simple - Holding Register 400")
        self.root.geometry("600x400")
        
        # Variables
        self.client = None
        self.is_connected = False
        self.is_reading = False
        self.read_thread = None
        
        # Variables de configuración
        self.server_ip = tk.StringVar(value="127.0.0.1")
        self.server_port = tk.StringVar(value="502")
        self.unit_id = tk.StringVar(value="1")
        self.register_address = tk.StringVar(value="400")
        self.read_interval = tk.StringVar(value="1")  # segundos
        
        # Variable para mostrar el valor
        self.register_value = tk.StringVar(value="No conectado")
        
        self.setup_gui()
    
    def setup_gui(self):
        """Configurar la interfaz gráfica"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configurar pesos de grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Título
        title_label = ttk.Label(main_frame, text="Cliente Modbus TCP/IP", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Configuración de conexión
        config_frame = ttk.LabelFrame(main_frame, text="Configuración del Servidor", padding="10")
        config_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # IP del servidor
        ttk.Label(config_frame, text="IP del Servidor:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Entry(config_frame, textvariable=self.server_ip, width=15).grid(row=0, column=1, padx=(0, 20))
        
        # Puerto
        ttk.Label(config_frame, text="Puerto:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        ttk.Entry(config_frame, textvariable=self.server_port, width=8).grid(row=0, column=3, padx=(0, 20))
        
        # Unit ID
        ttk.Label(config_frame, text="Unit ID:").grid(row=0, column=4, sticky=tk.W, padx=(0, 10))
        ttk.Entry(config_frame, textvariable=self.unit_id, width=8).grid(row=0, column=5)
        
        # Configuración de lectura
        read_frame = ttk.LabelFrame(main_frame, text="Configuración de Lectura", padding="10")
        read_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # Dirección del registro
        ttk.Label(read_frame, text="Dirección del Registro:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Entry(read_frame, textvariable=self.register_address, width=8).grid(row=0, column=1, padx=(0, 20))
        
        # Intervalo de lectura
        ttk.Label(read_frame, text="Intervalo (segundos):").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        ttk.Entry(read_frame, textvariable=self.read_interval, width=8).grid(row=0, column=3)
        
        # Botones de control
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(0, 20))
        
        self.connect_btn = ttk.Button(button_frame, text="Conectar", command=self.connect_to_server)
        self.connect_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.disconnect_btn = ttk.Button(button_frame, text="Desconectar", 
                                        command=self.disconnect_from_server, state="disabled")
        self.disconnect_btn.grid(row=0, column=1, padx=(0, 10))
        
        self.start_read_btn = ttk.Button(button_frame, text="Iniciar Lectura", 
                                        command=self.start_reading, state="disabled")
        self.start_read_btn.grid(row=0, column=2, padx=(0, 10))
        
        self.stop_read_btn = ttk.Button(button_frame, text="Parar Lectura", 
                                       command=self.stop_reading, state="disabled")
        self.stop_read_btn.grid(row=0, column=3, padx=(0, 10))
        
        self.read_once_btn = ttk.Button(button_frame, text="Leer Una Vez", 
                                       command=self.read_once, state="disabled")
        self.read_once_btn.grid(row=0, column=4)
        
        # Display del valor
        value_frame = ttk.LabelFrame(main_frame, text="Valor del Holding Register", padding="20")
        value_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # Valor actual
        value_label = ttk.Label(value_frame, textvariable=self.register_value, 
                               font=("Arial", 24, "bold"), foreground="blue")
        value_label.grid(row=0, column=0)
        
        # Log de mensajes
        log_frame = ttk.LabelFrame(main_frame, text="Log de Mensajes", padding="10")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Text widget para log
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Configurar pesos para el log
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        # Botón para limpiar log
        ttk.Button(log_frame, text="Limpiar Log", command=self.clear_log).grid(row=1, column=0, pady=(5, 0))
        
        # Mensaje inicial
        self.log_message("Cliente Modbus inicializado. Configure la conexión y presione Conectar.")
    
    def log_message(self, message):
        """Agregar mensaje al log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """Limpiar el log"""
        self.log_text.delete(1.0, tk.END)
    
    def connect_to_server(self):
        """Conectar al servidor Modbus"""
        try:
            ip = self.server_ip.get().strip()
            port = int(self.server_port.get())
            
            if not ip:
                raise ValueError("La IP del servidor no puede estar vacía")
            
            if port <= 0 or port > 65535:
                raise ValueError("El puerto debe estar entre 1 y 65535")
            
            self.log_message(f"Conectando a {ip}:{port}...")
            
            # Crear cliente
            self.client = ModbusTcpClient(ip, port=port)
            
            # Intentar conectar
            if self.client.connect():
                self.is_connected = True
                self.log_message("✅ Conectado exitosamente!")
                self.register_value.set("Conectado - Listo para leer")
                
                # Actualizar botones
                self.connect_btn.config(state="disabled")
                self.disconnect_btn.config(state="normal")
                self.start_read_btn.config(state="normal")
                self.read_once_btn.config(state="normal")
            else:
                raise Exception("No se pudo establecer la conexión")
                
        except ValueError as e:
            messagebox.showerror("Error de Configuración", str(e))
            self.log_message(f"❌ Error de configuración: {e}")
        except Exception as e:
            messagebox.showerror("Error de Conexión", f"No se pudo conectar: {str(e)}")
            self.log_message(f"❌ Error de conexión: {e}")
    
    def disconnect_from_server(self):
        """Desconectar del servidor"""
        try:
            # Parar lectura si está activa
            if self.is_reading:
                self.stop_reading()
            
            # Cerrar conexión
            if self.client:
                self.client.close()
            
            self.is_connected = False
            self.log_message("🔌 Desconectado del servidor")
            self.register_value.set("Desconectado")
            
            # Actualizar botones
            self.connect_btn.config(state="normal")
            self.disconnect_btn.config(state="disabled")
            self.start_read_btn.config(state="disabled")
            self.stop_read_btn.config(state="disabled")
            self.read_once_btn.config(state="disabled")
            
        except Exception as e:
            self.log_message(f"❌ Error al desconectar: {e}")
    
    def read_register(self):
        """Leer el holding register especificado"""
        try:
            if not self.is_connected or not self.client:
                raise Exception("No hay conexión al servidor")
            
            address = int(self.register_address.get())
            unit_id = int(self.unit_id.get())
            
            # Leer el registro
            result = self.client.read_holding_registers(address, count=1, slave=unit_id)
            
            if result.isError():
                raise Exception(f"Error Modbus: {result}")
            
            # Obtener el valor
            value = result.registers[0]
            
            # Actualizar display
            self.register_value.set(f"{value}")
            self.log_message(f"📊 Registro {address}: {value}")
            
            return value
            
        except ValueError as e:
            error_msg = f"Error de configuración: {e}"
            self.log_message(f"❌ {error_msg}")
            self.register_value.set("Error de config")
            return None
        except Exception as e:
            error_msg = f"Error de lectura: {e}"
            self.log_message(f"❌ {error_msg}")
            self.register_value.set("Error de lectura")
            return None
    
    def read_once(self):
        """Leer el registro una sola vez"""
        self.read_register()
    
    def start_reading(self):
        """Iniciar lectura continua"""
        try:
            interval = float(self.read_interval.get())
            if interval <= 0:
                raise ValueError("El intervalo debe ser mayor que 0")
            
            self.is_reading = True
            self.log_message(f"🔄 Iniciando lectura continua cada {interval} segundos")
            
            # Actualizar botones
            self.start_read_btn.config(state="disabled")
            self.stop_read_btn.config(state="normal")
            
            # Iniciar hilo de lectura
            self.read_thread = threading.Thread(target=self._reading_worker, daemon=True)
            self.read_thread.start()
            
        except ValueError as e:
            messagebox.showerror("Error", str(e))
            self.log_message(f"❌ Error: {e}")
    
    def stop_reading(self):
        """Parar lectura continua"""
        self.is_reading = False
        self.log_message("⏹️ Lectura continua detenida")
        
        # Actualizar botones
        self.start_read_btn.config(state="normal")
        self.stop_read_btn.config(state="disabled")
    
    def _reading_worker(self):
        """Hilo worker para lectura continua"""
        while self.is_reading and self.is_connected:
            try:
                interval = float(self.read_interval.get())
                self.read_register()
                time.sleep(interval)
            except Exception as e:
                self.log_message(f"❌ Error en lectura continua: {e}")
                break
        
        # Asegurar que los botones se actualicen al salir
        if self.is_reading:
            self.root.after(0, self.stop_reading)


def main():
    """Función principal"""
    root = tk.Tk()
    app = SimpleModbusClient(root)
    
    # Manejar cierre de ventana
    def on_closing():
        if app.is_connected:
            app.disconnect_from_server()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()
