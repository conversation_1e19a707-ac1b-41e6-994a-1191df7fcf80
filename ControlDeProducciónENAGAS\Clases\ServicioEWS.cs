using Microsoft.Exchange.WebServices.Data;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace ControlDeProducciónENAGAS.Clases
{
    /// <summary>
    /// Servicio para procesar correos usando Exchange Web Services (EWS)
    /// </summary>
    public class ServicioEWS
    {
        private readonly string serverUrl;
        private readonly string username;
        private readonly string password;
        private readonly string domain;
        private readonly string rutaCarpeta;
        private readonly string archivoCorreosProcesados;
        private ExchangeService exchangeService;

        public ServicioEWS(string serverUrl, string username, string password, string domain = "", string rutaCarpeta = "")
        {
            this.serverUrl = serverUrl;
            this.username = username;
            this.password = password;
            this.domain = domain;
            this.rutaCarpeta = string.IsNullOrEmpty(rutaCarpeta) ? 
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "CorreosDescargados") : 
                rutaCarpeta;
            this.archivoCorreosProcesados = Path.Combine(this.rutaCarpeta, "correos_procesados.txt");

            // Asegurar que el directorio existe
            if (!Directory.Exists(this.rutaCarpeta))
            {
                Directory.CreateDirectory(this.rutaCarpeta);
            }

            // Asegurar que el archivo de correos procesados existe
            if (!File.Exists(archivoCorreosProcesados))
            {
                File.Create(archivoCorreosProcesados).Close();
            }
        }

        /// <summary>
        /// Prueba la conexión EWS con timeout
        /// </summary>
        public async Task<bool> ProbarConexionAsync()
        {
            try
            {
                // Timeout de 20 segundos para pruebas de conexión
                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(20)))
                {
                    // Inicializar servicio con timeout
                    await InicializarServicioExchangeAsync(cts.Token).ConfigureAwait(false);

                    // Intentar acceder a la bandeja de entrada para verificar conexión
                    var inbox = await System.Threading.Tasks.Task.Run(() =>
                    {
                        cts.Token.ThrowIfCancellationRequested();

                        // Establecer timeout para operaciones EWS
                        if (exchangeService != null)
                        {
                            exchangeService.Timeout = 8000; // 8 segundos timeout
                        }

                        return Folder.Bind(exchangeService, WellKnownFolderName.Inbox);
                    }, cts.Token).ConfigureAwait(false);

                    return true;
                }
            }
            catch (OperationCanceledException)
            {
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Obtiene correos sin procesar para el pipeline unificado
        /// </summary>
        public List<DatosCorreoSinProcesar> ObtenerCorreosSinProcesar()
        {
            var correosSinProcesar = new List<DatosCorreoSinProcesar>();

            try
            {
                if (exchangeService == null)
                {
                    // Inicializar sincrónicamente para evitar deadlocks
                    InicializarServicioExchange();
                }

                // Establecer timeout para operaciones EWS
                if (exchangeService != null)
                {
                    exchangeService.Timeout = 15000; // 15 segundos timeout
                }

                // Buscar correos en bandeja de entrada (obtener TODOS los correos, no solo no leídos)
                var resultadosBusqueda = exchangeService.FindItems(WellKnownFolderName.Inbox, new ItemView(50));

                foreach (var item in resultadosBusqueda.Items.OfType<EmailMessage>())
                {
                    try
                    {
                        // Cargar propiedades del correo (llamada sincrónica directa)
                        item.Load(new PropertySet(BasePropertySet.FirstClassProperties, ItemSchema.Body));

                        // Crear objeto de datos de correo sin procesar
                        var correoSinProcesar = new DatosCorreoSinProcesar
                        {
                            MessageId = item.Id?.UniqueId ?? $"EWS_{DateTime.Now.Ticks}_{correosSinProcesar.Count}",
                            Subject = item.Subject ?? "",
                            HtmlContent = ObtenerContenidoHtmlCorreo(item),
                            Sender = item.From?.Address ?? "",
                            IsRead = item.IsRead
                        };

                        correosSinProcesar.Add(correoSinProcesar);
                    }
                    catch (Exception)
                    {
                        // Continuar con el siguiente correo si hay error
                        continue;
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }

            return correosSinProcesar;
        }

        /// <summary>
        /// Marca un correo como leído en el servidor
        /// </summary>
        public void MarcarCorreoComoLeido(string messageId)
        {
            try
            {
                if (exchangeService == null)
                {
                    return;
                }

                // Establecer timeout para operaciones EWS
                exchangeService.Timeout = 10000; // 10 segundos timeout

                // Buscar el correo por ID y marcarlo como leído
                var resultadosBusqueda = exchangeService.FindItems(WellKnownFolderName.Inbox, new ItemView(100));
                var correoAMarcar = resultadosBusqueda.Items.OfType<EmailMessage>()
                    .FirstOrDefault(e => e.Id?.UniqueId == messageId);

                if (correoAMarcar != null)
                {
                    correoAMarcar.IsRead = true;
                    correoAMarcar.Update(ConflictResolutionMode.AutoResolve);
                }
            }
            catch (Exception)
            {
                // Error silencioso - no es crítico si no se puede marcar como leído
            }
        }

        /// <summary>
        /// Envía un correo con datos analizados usando EWS
        /// </summary>
        public async Task<bool> EnviarCorreoDatosAnalizadosAsync(List<string> datosAnalizados, string asuntoOriginal, string remitenteOriginal, string destinatario)
        {
            try
            {
                // Asegurar que el servicio EWS está inicializado
                if (exchangeService == null)
                {
                    await InicializarServicioExchangeAsync();
                }

                // Crear el mensaje de correo
                var email = new EmailMessage(exchangeService);

                // Establecer destinatario
                email.ToRecipients.Add(destinatario);

                // Establecer asunto con prefijo "Feedback" para prevenir bucles infinitos
                email.Subject = $"Feedback - Análisis de Restricciones Horarias - {DateTime.Now:yyyy-MM-dd HH:mm}";

                // Crear cuerpo del correo con datos analizados
                var contenidoCuerpo = CrearCuerpoCorreoDatosAnalizados(datosAnalizados, asuntoOriginal, remitenteOriginal);
                email.Body = new MessageBody(BodyType.HTML, contenidoCuerpo);

                // Establecer importancia
                email.Importance = Importance.Normal;

                // Enviar el correo con timeout
                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)))
                {
                    await System.Threading.Tasks.Task.Run(() =>
                    {
                        cts.Token.ThrowIfCancellationRequested();
                        email.Send();
                    }, cts.Token);
                }

                return true;
            }
            catch (OperationCanceledException)
            {
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Reinicia el servicio EWS para forzar reinicialización
        /// </summary>
        public void ReiniciarServicio()
        {
            try
            {
                // ExchangeService no implementa IDisposable, así que solo lo ponemos en null
                exchangeService = null;
            }
            catch (Exception)
            {
                exchangeService = null;
            }
        }

        /// <summary>
        /// Inicializa el servicio Exchange con autenticación (versión asíncrona)
        /// </summary>
        private async System.Threading.Tasks.Task InicializarServicioExchangeAsync(CancellationToken cancellationToken = default)
        {
            if (exchangeService != null)
                return;

            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                exchangeService = new ExchangeService(ExchangeVersion.Exchange2013_SP1);

                // Establecer timeout para todas las operaciones EWS
                exchangeService.Timeout = 10000; // 10 segundos

                // Autenticación básica
                if (!string.IsNullOrEmpty(domain))
                {
                    exchangeService.Credentials = new NetworkCredential(username, password, domain);
                }
                else
                {
                    exchangeService.Credentials = new NetworkCredential(username, password);
                }

                cancellationToken.ThrowIfCancellationRequested();

                // Establecer la URL si se proporciona, de lo contrario usar autodiscover
                if (!string.IsNullOrEmpty(serverUrl))
                {
                    exchangeService.Url = new Uri(serverUrl);
                }
                else
                {
                    // Usar autodiscover con timeout
                    await System.Threading.Tasks.Task.Run(() =>
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        exchangeService.AutodiscoverUrl(username, CallbackValidacionUrlRedireccion);
                    }, cancellationToken).ConfigureAwait(false);
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// Versión sincrónica de InicializarServicioExchange para evitar deadlocks
        /// </summary>
        private void InicializarServicioExchange()
        {
            try
            {
                exchangeService = new ExchangeService(ExchangeVersion.Exchange2013_SP1);

                // Establecer timeout para todas las operaciones EWS
                exchangeService.Timeout = 15000; // 15 segundos

                // Autenticación básica
                if (!string.IsNullOrEmpty(domain))
                {
                    exchangeService.Credentials = new NetworkCredential(username, password, domain);
                }
                else
                {
                    exchangeService.Credentials = new NetworkCredential(username, password);
                }

                // Establecer la URL si se proporciona, de lo contrario usar autodiscover
                if (!string.IsNullOrEmpty(serverUrl))
                {
                    exchangeService.Url = new Uri(serverUrl);
                }
                else
                {
                    // Usar autodiscover con timeout para prevenir colgado
                    using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)))
                    {
                        var tareaAutodiscover = System.Threading.Tasks.Task.Run(() =>
                            exchangeService.AutodiscoverUrl(username, CallbackValidacionUrlRedireccion), cts.Token);

                        try
                        {
                            tareaAutodiscover.Wait(cts.Token);
                        }
                        catch (OperationCanceledException)
                        {
                            throw new TimeoutException("La operación de autodiscover EWS expiró");
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// Callback de validación para redirección de autodiscover
        /// </summary>
        private static bool CallbackValidacionUrlRedireccion(string redirectionUrl)
        {
            // Validar que la URL de redirección sea HTTPS
            var uri = new Uri(redirectionUrl);
            return uri.Scheme == "https";
        }

        /// <summary>
        /// Extrae contenido HTML del mensaje de correo (prefiere HTML sobre texto)
        /// </summary>
        private string ObtenerContenidoHtmlCorreo(EmailMessage email)
        {
            try
            {
                if (email.Body.BodyType == BodyType.HTML)
                {
                    // Devolver contenido HTML directamente
                    string contenidoHtml = email.Body.Text ?? "";
                    return contenidoHtml;
                }
                else if (email.Body.BodyType == BodyType.Text)
                {
                    // Convertir texto plano a HTML básico
                    string contenidoTexto = email.Body.Text ?? "";
                    string htmlConvertido = $"<html><body><pre>{System.Net.WebUtility.HtmlEncode(contenidoTexto)}</pre></body></html>";
                    return htmlConvertido;
                }
            }
            catch (Exception)
            {
                // Error silencioso
            }

            return "";
        }

        /// <summary>
        /// Crea el contenido HTML del cuerpo para el correo de datos analizados
        /// </summary>
        private string CrearCuerpoCorreoDatosAnalizados(List<string> datosAnalizados, string asuntoOriginal, string remitenteOriginal)
        {
            var html = new System.Text.StringBuilder();

            html.AppendLine("<html>");
            html.AppendLine("<head>");
            html.AppendLine("<meta charset='UTF-8'>");
            html.AppendLine("<title>Análisis de Restricciones Horarias</title>");
            html.AppendLine("<style>");
            html.AppendLine("body { font-family: Arial, sans-serif; margin: 20px; }");
            html.AppendLine("table { border-collapse: collapse; width: 100%; margin: 20px 0; }");
            html.AppendLine("th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }");
            html.AppendLine("th { background-color: #f2f2f2; }");
            html.AppendLine(".restricted { background-color: #ffcccc; color: #cc0000; font-weight: bold; }");
            html.AppendLine(".available { background-color: #ccffcc; color: #006600; }");
            html.AppendLine(".info-section { background-color: #f9f9f9; padding: 15px; margin: 10px 0; border-left: 4px solid #007acc; }");
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");

            // Encabezado
            html.AppendLine("<h1>📊 Análisis de Restricciones Horarias</h1>");
            html.AppendLine($"<p><strong>Fecha y Hora del Análisis:</strong> {DateTime.Now:dddd, dd MMMM yyyy HH:mm:ss}</p>");

            // Información del correo original
            html.AppendLine("<div class='info-section'>");
            html.AppendLine("<h2>📧 Información del Correo Analizado</h2>");
            html.AppendLine($"<p><strong>Asunto:</strong> {System.Net.WebUtility.HtmlEncode(asuntoOriginal ?? "N/A")}</p>");
            html.AppendLine($"<p><strong>Remitente:</strong> {System.Net.WebUtility.HtmlEncode(remitenteOriginal ?? "N/A")}</p>");
            html.AppendLine("</div>");

            // Resumen de datos analizados
            html.AppendLine("<div class='info-section'>");
            html.AppendLine("<h2>📈 Resumen del Análisis</h2>");

            if (datosAnalizados != null && datosAnalizados.Count > 0)
            {
                var horasRestringidas = datosAnalizados.Where(h => h.StartsWith("H", StringComparison.OrdinalIgnoreCase)).ToList();
                html.AppendLine($"<p><strong>Total de Horas con Restricciones:</strong> {horasRestringidas.Count}</p>");
                html.AppendLine($"<p><strong>Horas Disponibles:</strong> {24 - horasRestringidas.Count}</p>");

                if (horasRestringidas.Count > 0)
                {
                    html.AppendLine("<p><strong>Horas Restringidas:</strong> ");
                    html.AppendLine(string.Join(", ", horasRestringidas.Select(h => h.Substring(1))));
                    html.AppendLine("</p>");
                }
            }
            else
            {
                html.AppendLine("<p><strong>Estado:</strong> No se encontraron restricciones horarias en el correo analizado.</p>");
            }
            html.AppendLine("</div>");

            // Tabla de restricciones horarias
            html.AppendLine("<h2>🕐 Tabla de Restricciones por Hora</h2>");
            html.AppendLine("<table>");
            html.AppendLine("<tr>");
            html.AppendLine("<th>Hora</th>");
            html.AppendLine("<th>Estado</th>");
            html.AppendLine("<th>Descripción</th>");
            html.AppendLine("</tr>");

            // Crear tabla de 24 horas
            for (int hora = 1; hora <= 24; hora++)
            {
                string codigoHora = $"H{hora}";
                bool estaRestringida = datosAnalizados?.Contains(codigoHora, StringComparer.OrdinalIgnoreCase) == true;
                string claseCSS = estaRestringida ? "restricted" : "available";
                string estado = estaRestringida ? "RESTRINGIDA" : "Disponible";
                string descripcion = estaRestringida ? "Hora con restricciones activas" : "Hora sin restricciones";

                html.AppendLine("<tr>");
                html.AppendLine($"<td>{hora:00}:00</td>");
                html.AppendLine($"<td class='{claseCSS}'>{estado}</td>");
                html.AppendLine($"<td>{descripcion}</td>");
                html.AppendLine("</tr>");
            }

            html.AppendLine("</table>");

            // Pie de página
            html.AppendLine("<div class='info-section'>");
            html.AppendLine("<h3>ℹ️ Información Adicional</h3>");
            html.AppendLine("<p>Este correo ha sido generado automáticamente por el sistema de análisis de restricciones horarias.</p>");
            html.AppendLine("<p>Los datos mostrados corresponden a las restricciones extraídas del correo recibido.</p>");
            html.AppendLine($"<p><strong>Generado por:</strong> Sistema de Análisis de Correos v1.0</p>");
            html.AppendLine("</div>");

            html.AppendLine("</body>");
            html.AppendLine("</html>");

            return html.ToString();
        }

        /// <summary>
        /// Libera el servicio Exchange
        /// </summary>
        public void Dispose()
        {
            exchangeService = null;
        }
    }

    /// <summary>
    /// Estructura de datos de correo sin procesar para procesamiento unificado
    /// </summary>
    public class DatosCorreoSinProcesar
    {
        public string MessageId { get; set; } = "";
        public string Subject { get; set; } = "";
        public string HtmlContent { get; set; } = "";
        public string Sender { get; set; } = "";
        public bool IsRead { get; set; }
    }
}
