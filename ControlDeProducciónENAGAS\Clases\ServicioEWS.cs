using Microsoft.Exchange.WebServices.Data;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace ControlDeProducciónENAGAS.Clases
{
    /// <summary>
    /// Servicio para procesar correos usando Exchange Web Services (EWS)
    /// </summary>
    public class ServicioEWS
    {
        private readonly string serverUrl;
        private readonly string username;
        private readonly string password;
        private readonly string domain;
        private readonly string rutaCarpeta;
        private readonly string archivoCorreosProcesados;
        private ExchangeService exchangeService;

        public ServicioEWS(string serverUrl, string username, string password, string domain = "", string rutaCarpeta = "")
        {
            this.serverUrl = serverUrl;
            this.username = username;
            this.password = password;
            this.domain = domain;
            this.rutaCarpeta = string.IsNullOrEmpty(rutaCarpeta) ? 
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "CorreosDescargados") : 
                rutaCarpeta;
            this.archivoCorreosProcesados = Path.Combine(this.rutaCarpeta, "correos_procesados.txt");

            // Asegurar que el directorio existe
            if (!Directory.Exists(this.rutaCarpeta))
            {
                Directory.CreateDirectory(this.rutaCarpeta);
            }

            // Asegurar que el archivo de correos procesados existe
            if (!File.Exists(archivoCorreosProcesados))
            {
                File.Create(archivoCorreosProcesados).Close();
            }
        }

        /// <summary>
        /// Prueba la conexión EWS con timeout
        /// </summary>
        public async Task<bool> ProbarConexionAsync()
        {
            try
            {
                // Timeout de 20 segundos para pruebas de conexión
                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(20)))
                {
                    // Inicializar servicio con timeout
                    await InicializarServicioExchangeAsync(cts.Token).ConfigureAwait(false);

                    // Intentar acceder a la bandeja de entrada para verificar conexión
                    var inbox = await Task.Run(() =>
                    {
                        cts.Token.ThrowIfCancellationRequested();

                        // Establecer timeout para operaciones EWS
                        if (exchangeService != null)
                        {
                            exchangeService.Timeout = 8000; // 8 segundos timeout
                        }

                        return Folder.Bind(exchangeService, WellKnownFolderName.Inbox);
                    }, cts.Token).ConfigureAwait(false);

                    return true;
                }
            }
            catch (OperationCanceledException)
            {
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Obtiene correos sin procesar para el pipeline unificado
        /// </summary>
        public List<DatosCorreoSinProcesar> ObtenerCorreosSinProcesar()
        {
            var correosSinProcesar = new List<DatosCorreoSinProcesar>();

            try
            {
                if (exchangeService == null)
                {
                    // Inicializar sincrónicamente para evitar deadlocks
                    InicializarServicioExchange();
                }

                // Establecer timeout para operaciones EWS
                if (exchangeService != null)
                {
                    exchangeService.Timeout = 15000; // 15 segundos timeout
                }

                // Buscar correos en bandeja de entrada (obtener TODOS los correos, no solo no leídos)
                var resultadosBusqueda = exchangeService.FindItems(WellKnownFolderName.Inbox, new ItemView(50));

                foreach (var item in resultadosBusqueda.Items.OfType<EmailMessage>())
                {
                    try
                    {
                        // Cargar propiedades del correo (llamada sincrónica directa)
                        item.Load(new PropertySet(BasePropertySet.FirstClassProperties, ItemSchema.Body));

                        // Crear objeto de datos de correo sin procesar
                        var correoSinProcesar = new DatosCorreoSinProcesar
                        {
                            MessageId = item.Id?.UniqueId ?? $"EWS_{DateTime.Now.Ticks}_{correosSinProcesar.Count}",
                            Subject = item.Subject ?? "",
                            HtmlContent = ObtenerContenidoHtmlCorreo(item),
                            Sender = item.From?.Address ?? "",
                            IsRead = item.IsRead
                        };

                        correosSinProcesar.Add(correoSinProcesar);
                    }
                    catch (Exception)
                    {
                        // Continuar con el siguiente correo si hay error
                        continue;
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }

            return correosSinProcesar;
        }

        /// <summary>
        /// Marca un correo como leído en el servidor
        /// </summary>
        public void MarcarCorreoComoLeido(string messageId)
        {
            try
            {
                if (exchangeService == null)
                {
                    return;
                }

                // Establecer timeout para operaciones EWS
                exchangeService.Timeout = 10000; // 10 segundos timeout

                // Buscar el correo por ID y marcarlo como leído
                var resultadosBusqueda = exchangeService.FindItems(WellKnownFolderName.Inbox, new ItemView(100));
                var correoAMarcar = resultadosBusqueda.Items.OfType<EmailMessage>()
                    .FirstOrDefault(e => e.Id?.UniqueId == messageId);

                if (correoAMarcar != null)
                {
                    correoAMarcar.IsRead = true;
                    correoAMarcar.Update(ConflictResolutionMode.AutoResolve);
                }
            }
            catch (Exception)
            {
                // Error silencioso - no es crítico si no se puede marcar como leído
            }
        }

        /// <summary>
        /// Envía un correo con datos analizados usando EWS
        /// </summary>
        public async Task<bool> EnviarCorreoDatosAnalizadosAsync(List<string> datosAnalizados, string asuntoOriginal, string remitenteOriginal, string destinatario)
        {
            try
            {
                // Asegurar que el servicio EWS está inicializado
                if (exchangeService == null)
                {
                    await InicializarServicioExchangeAsync();
                }

                // Crear el mensaje de correo
                var email = new EmailMessage(exchangeService);

                // Establecer destinatario
                email.ToRecipients.Add(destinatario);

                // Establecer asunto con prefijo "Feedback" para prevenir bucles infinitos
                email.Subject = $"Feedback - Análisis de Restricciones Horarias - {DateTime.Now:yyyy-MM-dd HH:mm}";

                // Crear cuerpo del correo con datos analizados
                var contenidoCuerpo = CrearCuerpoCorreoDatosAnalizados(datosAnalizados, asuntoOriginal, remitenteOriginal);
                email.Body = new MessageBody(BodyType.HTML, contenidoCuerpo);

                // Establecer importancia
                email.Importance = Importance.Normal;

                // Enviar el correo con timeout
                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)))
                {
                    await Task.Run(() =>
                    {
                        cts.Token.ThrowIfCancellationRequested();
                        email.Send();
                    }, cts.Token);
                }

                return true;
            }
            catch (OperationCanceledException)
            {
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Reinicia el servicio EWS para forzar reinicialización
        /// </summary>
        public void ReiniciarServicio()
        {
            try
            {
                // ExchangeService no implementa IDisposable, así que solo lo ponemos en null
                exchangeService = null;
            }
            catch (Exception)
            {
                exchangeService = null;
            }
        }

        /// <summary>
        /// Inicializa el servicio Exchange con autenticación (versión asíncrona)
        /// </summary>
        private async Task InicializarServicioExchangeAsync(CancellationToken cancellationToken = default)
        {
            if (exchangeService != null)
                return;

            try
            {
                cancellationToken.ThrowIfCancellationRequested();

                exchangeService = new ExchangeService(ExchangeVersion.Exchange2013_SP1);

                // Establecer timeout para todas las operaciones EWS
                exchangeService.Timeout = 10000; // 10 segundos

                // Autenticación básica
                if (!string.IsNullOrEmpty(domain))
                {
                    exchangeService.Credentials = new NetworkCredential(username, password, domain);
                }
                else
                {
                    exchangeService.Credentials = new NetworkCredential(username, password);
                }

                cancellationToken.ThrowIfCancellationRequested();

                // Establecer la URL si se proporciona, de lo contrario usar autodiscover
                if (!string.IsNullOrEmpty(serverUrl))
                {
                    exchangeService.Url = new Uri(serverUrl);
                }
                else
                {
                    // Usar autodiscover con timeout
                    await Task.Run(() =>
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        exchangeService.AutodiscoverUrl(username, CallbackValidacionUrlRedireccion);
                    }, cancellationToken).ConfigureAwait(false);
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// Versión sincrónica de InicializarServicioExchange para evitar deadlocks
        /// </summary>
        private void InicializarServicioExchange()
        {
            try
            {
                exchangeService = new ExchangeService(ExchangeVersion.Exchange2013_SP1);

                // Establecer timeout para todas las operaciones EWS
                exchangeService.Timeout = 15000; // 15 segundos

                // Autenticación básica
                if (!string.IsNullOrEmpty(domain))
                {
                    exchangeService.Credentials = new NetworkCredential(username, password, domain);
                }
                else
                {
                    exchangeService.Credentials = new NetworkCredential(username, password);
                }

                // Establecer la URL si se proporciona, de lo contrario usar autodiscover
                if (!string.IsNullOrEmpty(serverUrl))
                {
                    exchangeService.Url = new Uri(serverUrl);
                }
                else
                {
                    // Usar autodiscover con timeout para prevenir colgado
                    using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)))
                    {
                        var tareaAutodiscover = Task.Run(() =>
                            exchangeService.AutodiscoverUrl(username, CallbackValidacionUrlRedireccion), cts.Token);

                        try
                        {
                            tareaAutodiscover.Wait(cts.Token);
                        }
                        catch (OperationCanceledException)
                        {
                            throw new TimeoutException("La operación de autodiscover EWS expiró");
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// Callback de validación para redirección de autodiscover
        /// </summary>
        private static bool CallbackValidacionUrlRedireccion(string redirectionUrl)
        {
            // Validar que la URL de redirección sea HTTPS
            var uri = new Uri(redirectionUrl);
            return uri.Scheme == "https";
        }

        /// <summary>
        /// Extrae contenido HTML del mensaje de correo (prefiere HTML sobre texto)
        /// </summary>
        private string ObtenerContenidoHtmlCorreo(EmailMessage email)
        {
            try
            {
                if (email.Body.BodyType == BodyType.HTML)
                {
                    // Devolver contenido HTML directamente
                    string contenidoHtml = email.Body.Text ?? "";
                    return contenidoHtml;
                }
                else if (email.Body.BodyType == BodyType.Text)
                {
                    // Convertir texto plano a HTML básico
                    string contenidoTexto = email.Body.Text ?? "";
                    string htmlConvertido = $"<html><body><pre>{System.Net.WebUtility.HtmlEncode(contenidoTexto)}</pre></body></html>";
                    return htmlConvertido;
                }
            }
            catch (Exception)
            {
                // Error silencioso
            }

            return "";
        }
