using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ControlDeProducciónENAGAS.Clases
{
    /// <summary>
    /// Gestor de interfaz de usuario para MQTT - Separa la lógica de negocio del formulario
    /// para mejorar la compatibilidad con el Designer de Visual Studio
    /// </summary>
    public class MqttUIManager
    {
        #region Campos privados
        private readonly Form _parentForm;
        private readonly DataGridView _dgvMqttTopics;
        private readonly Button _btnRefrescarMQTT;
        private readonly RichTextBox _rtbLog;
        private ConexionMQTT? _mqtt;
        private bool _mqttConectado = false;
        #endregion

        #region Propiedades públicas
        /// <summary>
        /// Indica si MQTT está conectado
        /// </summary>
        public bool MQTTConectado 
        { 
            get => _mqttConectado; 
            private set => _mqttConectado = value; 
        }

        /// <summary>
        /// Instancia de conexión MQTT actual
        /// </summary>
        public ConexionMQTT? Mqtt => _mqtt;
        #endregion

        #region Constructor
        /// <summary>
        /// Constructor del gestor de UI MQTT
        /// </summary>
        /// <param name="parentForm">Formulario padre</param>
        /// <param name="dgvMqttTopics">DataGridView para mostrar topics</param>
        /// <param name="btnRefrescarMQTT">Botón de refrescar</param>
        /// <param name="rtbLog">RichTextBox para logs</param>
        public MqttUIManager(Form parentForm, DataGridView dgvMqttTopics, 
                           Button btnRefrescarMQTT, RichTextBox rtbLog)
        {
            _parentForm = parentForm ?? throw new ArgumentNullException(nameof(parentForm));
            _dgvMqttTopics = dgvMqttTopics ?? throw new ArgumentNullException(nameof(dgvMqttTopics));
            _btnRefrescarMQTT = btnRefrescarMQTT ?? throw new ArgumentNullException(nameof(btnRefrescarMQTT));
            _rtbLog = rtbLog ?? throw new ArgumentNullException(nameof(rtbLog));

            InicializarControles();
        }
        #endregion

        #region Métodos de inicialización
        /// <summary>
        /// Inicializa los controles de la interfaz
        /// </summary>
        private void InicializarControles()
        {
            try
            {
                // Configurar botón de refrescar
                _btnRefrescarMQTT.Enabled = false;
                
                // Verificar configuración del DataGridView
                LogMessage($"✅ DataGridView configurado con {_dgvMqttTopics.Columns.Count} columnas");
                
                // Listar columnas configuradas
                for (int i = 0; i < _dgvMqttTopics.Columns.Count; i++)
                {
                    var col = _dgvMqttTopics.Columns[i];
                    LogMessage($"🔍 Columna {i}: Name='{col.Name}', DataPropertyName='{col.DataPropertyName}'");
                }

                LogMessage("✅ MqttUIManager inicializado correctamente");
            }
            catch (Exception ex)
            {
                LogMessage($"❌ Error inicializando MqttUIManager: {ex.Message}");
            }
        }
        #endregion

        #region Métodos de conexión
        /// <summary>
        /// Conecta al broker MQTT usando la configuración proporcionada
        /// </summary>
        public async Task<bool> ConectarAsync(string protocolo, string host, int puerto, 
                                            string clientId, string usuario, string password, bool usarSslTls)
        {
            try
            {
                LogMessage($"🔄 Iniciando conexión MQTT a {host}:{puerto}");

                // Crear nueva instancia de conexión
                _mqtt = new ConexionMQTT();

                // Conectar usando la implementación existente
                bool conectado = await _mqtt.ConectarAsync(
                    protocolo, host, puerto, clientId, usuario, password, 
                    usarSslTls, true, false);

                if (conectado)
                {
                    MQTTConectado = true;
                    _btnRefrescarMQTT.Enabled = true;
                    
                    // Suscribir topics del CSV
                    await SuscribirTopicsDelCSV();
                    
                    LogMessage($"✅ Conectado a MQTT: {host}:{puerto}");
                    LogMessage("💡 Usa el botón 'Refrescar MQTT' para ver los topics disponibles");
                    
                    return true;
                }
                else
                {
                    LogMessage("❌ Error conectando a MQTT");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogMessage($"❌ Error conectando a MQTT: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Desconecta del broker MQTT
        /// </summary>
        public void Desconectar()
        {
            try
            {
                _mqtt?.Desconectar();
                MQTTConectado = false;
                _btnRefrescarMQTT.Enabled = false;
                
                // Limpiar DataGridView
                if (_parentForm.InvokeRequired)
                {
                    _parentForm.Invoke(new Action(() => _dgvMqttTopics.DataSource = null));
                }
                else
                {
                    _dgvMqttTopics.DataSource = null;
                }
                
                LogMessage("🔌 MQTT desconectado");
            }
            catch (Exception ex)
            {
                LogMessage($"❌ Error desconectando MQTT: {ex.Message}");
            }
        }
        #endregion

        #region Métodos de topics
        /// <summary>
        /// Suscribe a los topics definidos en el archivo CSV
        /// </summary>
        private async Task SuscribirTopicsDelCSV()
        {
            try
            {
                LogMessage("📋 Iniciando suscripción de topics desde CSV...");

                if (!File.Exists("topics.csv"))
                {
                    LogMessage("⚠️ topics.csv no existe, creando archivo de ejemplo...");
                    File.WriteAllText("topics.csv", "enagas/temperatura\nenagas/presion\nenagas/estado\nenagas/caudal");
                    LogMessage("✅ Archivo topics.csv creado");
                }

                string[] topics = File.ReadAllLines("topics.csv");
                LogMessage($"📋 Leyendo {topics.Length} líneas de topics.csv");

                int topicsSuscritos = 0;
                foreach (string topic in topics)
                {
                    if (!topic.StartsWith("#") && !string.IsNullOrEmpty(topic.Trim()))
                    {
                        LogMessage($"📡 Suscribiendo a: {topic.Trim()}");
                        await _mqtt!.SuscribirTopic(topic.Trim());
                        topicsSuscritos++;
                    }
                }

                LogMessage($"✅ Suscripción completada: {topicsSuscritos} topics");
            }
            catch (Exception ex)
            {
                LogMessage($"❌ Error en suscripción de topics: {ex.Message}");
            }
        }

        /// <summary>
        /// Actualiza la lista de topics en el DataGridView
        /// </summary>
        public void ActualizarListaTopics(bool verbose = false)
        {
            try
            {
                if (_mqtt == null || !_mqtt.EstaConectado)
                {
                    if (verbose) LogMessage("⚠️ MQTT no conectado");
                    return;
                }

                // Crear DataTable compatible con Designer
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("Topic", typeof(string));
                dataTable.Columns.Add("Valor", typeof(string));
                dataTable.Columns.Add("Timestamp", typeof(string));
                dataTable.Columns.Add("Estado", typeof(string));

                // Obtener topics de MQTT
                var topicsData = _mqtt.ObtenerTodosLosTopics();
                if (verbose) LogMessage($"📊 MQTT storage tiene {topicsData.Count} topics con valores");

                // Agregar topics con valores
                foreach (var topic in topicsData)
                {
                    dataTable.Rows.Add(
                        topic.Key,
                        topic.Value ?? "Sin datos",
                        DateTime.Now.ToString("HH:mm:ss"),
                        string.IsNullOrEmpty(topic.Value) ? "⏳ Esperando" : "✅ Activo"
                    );
                }

                // Agregar topics suscritos del CSV que no tengan valores
                if (File.Exists("topics.csv"))
                {
                    string[] csvTopics = File.ReadAllLines("topics.csv");
                    foreach (string csvTopic in csvTopics)
                    {
                        if (!csvTopic.StartsWith("#") && !string.IsNullOrEmpty(csvTopic.Trim()))
                        {
                            string topicName = csvTopic.Trim();
                            if (!topicsData.ContainsKey(topicName))
                            {
                                dataTable.Rows.Add(
                                    topicName,
                                    "Sin datos",
                                    DateTime.Now.ToString("HH:mm:ss"),
                                    "⏳ Esperando"
                                );
                            }
                        }
                    }
                }

                if (verbose) LogMessage($"📊 Total topics a mostrar: {dataTable.Rows.Count}");

                // Actualizar DataGridView
                if (_parentForm.InvokeRequired)
                {
                    _parentForm.Invoke(new Action(() => BindDataTableToGrid(dataTable, verbose)));
                }
                else
                {
                    BindDataTableToGrid(dataTable, verbose);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"❌ Error actualizando lista de topics: {ex.Message}");
            }
        }

        /// <summary>
        /// Vincula el DataTable al DataGridView
        /// </summary>
        private void BindDataTableToGrid(DataTable dataTable, bool verbose = false)
        {
            try
            {
                if (verbose) LogMessage("🔄 Iniciando vinculación de DataTable...");

                _dgvMqttTopics.DataSource = null;
                _dgvMqttTopics.DataSource = dataTable;

                if (verbose) LogMessage($"✅ DataTable vinculado - {_dgvMqttTopics.Rows.Count} filas");

                _dgvMqttTopics.Refresh();
                _dgvMqttTopics.Invalidate();
            }
            catch (Exception ex)
            {
                LogMessage($"❌ Error vinculando DataTable: {ex.Message}");
            }
        }
        #endregion

        #region Métodos de utilidad
        /// <summary>
        /// Registra un mensaje en el log
        /// </summary>
        private void LogMessage(string message)
        {
            try
            {
                string logEntry = $"{DateTime.Now:HH:mm:ss} {message}\n";
                
                if (_parentForm.InvokeRequired)
                {
                    _parentForm.Invoke(new Action(() => 
                    {
                        _rtbLog.AppendText(logEntry);
                        _rtbLog.ScrollToCaret();
                    }));
                }
                else
                {
                    _rtbLog.AppendText(logEntry);
                    _rtbLog.ScrollToCaret();
                }
            }
            catch (Exception ex)
            {
                // Evitar recursión infinita en caso de error de logging
                System.Diagnostics.Debug.WriteLine($"Error en LogMessage: {ex.Message}");
            }
        }
        #endregion
    }
}
